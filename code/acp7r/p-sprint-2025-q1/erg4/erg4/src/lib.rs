#[path = "libλ.rs"]
pub mod libλ;

mod module;
mod web;

#[derive(serde::Deserialize)]
pub struct AppConfig {
    pub webserver: approck::server::ModuleConfig,
}

pub struct AppStruct {
    pub webserver: approck::server::Module,
}

#[derive(Debug)]
pub struct IdentityStruct {}

// It makes sense to define documents in the web dir, but they should be referred to from here.
pub use crate::web::Document::Document as DocumentStruct;

impl approck::App for AppStruct {
    type Config = AppConfig;
    type Identity = IdentityStruct;
    async fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            webserver: approck::server::Module::new(config.webserver).await?,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        // get the crate name using the env! macro
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {}", crate_name);
        Ok(())
    }

    async fn auth(&self, _req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        Ok(IdentityStruct {})
    }
}

impl approck::Identity for crate::IdentityStruct {}

pub trait App {}
pub trait Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}
pub trait Document: bux::document::Base {}
