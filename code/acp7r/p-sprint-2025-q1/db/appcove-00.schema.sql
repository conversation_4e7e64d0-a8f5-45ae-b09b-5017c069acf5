-- AppCove Database Schema
-- This file contains the database schema for the AppCove application

-- Create the appcove schema
CREATE SCHEMA IF NOT EXISTS appcove;

-- Example table for AppCove application data
CREATE TABLE IF NOT EXISTS appcove.app_data (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Example table for user preferences (linked to auth_fence)
CREATE TABLE IF NOT EXISTS appcove.user_preferences (
    id SERIAL PRIMARY KEY,
    identity_uuid UUID NOT NULL REFERENCES auth_fence.identity(uuid),
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_user_preferences_identity_uuid ON appcove.user_preferences(identity_uuid);
CREATE INDEX IF NOT EXISTS idx_app_data_name ON appcove.app_data(name);
