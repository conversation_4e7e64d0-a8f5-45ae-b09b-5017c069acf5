CREATE SCHEMA IF NOT EXISTS auth_fence_provider;

-- Client base table
CREATE TABLE auth_fence_provider.oauth2_client (
    oauth2_client_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    create_ts timestamptz NOT NULL DEFAULT now(),
    update_ts timestamptz NOT NULL DEFAULT now(),
    name varchar(255) NOT NULL,
    secret_hash text NOT NULL,
    secret_salt text NOT NULL,
    redirect_uris text[] NOT NULL,
    scope_ids text[] NOT NULL,
    allowed_cidr cidr[] NOT NULL,
    note text,
    active boolean NOT NULL DEFAULT true,
    CONSTRAINT oauth2_client_pkey PRIMARY KEY (oauth2_client_uuid),
    CONSTRAINT oauth2_client_name_uniq UNIQUE (name)
);

-- User consent records
CREATE TABLE auth_fence_provider.oauth2_consent (
    oauth2_consent_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    oauth2_client_uuid uuid NOT NULL,
    identity_uuid uuid NOT NULL,
    create_ts timestamptz NOT NULL DEFAULT now(),
    update_ts timestamptz NOT NULL DEFAULT now(),
    scope_ids text[] NOT NULL,
    CONSTRAINT oauth2_consent_pkey PRIMARY KEY (oauth2_consent_uuid),
    CONSTRAINT oauth2_consent_unique UNIQUE (oauth2_client_uuid, identity_uuid),
    CONSTRAINT oauth2_consent_client_fkey FOREIGN KEY (oauth2_client_uuid) REFERENCES auth_fence_provider.oauth2_client(oauth2_client_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT oauth2_consent_identity_fkey FOREIGN KEY (identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- OAuth2 activity log
CREATE TABLE auth_fence_provider.oauth2_log (
    oauth2_log_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    oauth2_client_uuid uuid NOT NULL,
    identity_uuid uuid,
    create_ts timestamptz NOT NULL DEFAULT now(),
    create_addr inet,
    token_type varchar(32),
    token_hash varchar(1024),
    token_expire_ts timestamptz,
    action text,
    result text,
    data jsonb,
    CONSTRAINT oauth2_log_pkey PRIMARY KEY (oauth2_log_uuid),
    CONSTRAINT oauth2_log_client_fkey FOREIGN KEY (oauth2_client_uuid) REFERENCES auth_fence_provider.oauth2_client(oauth2_client_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT oauth2_token_type CHECK (token_type IN ('auth_code', 'access_token'))
);

-- OAuth2 scopes
CREATE TABLE auth_fence_provider.oauth2_scope (
    scope_id varchar(32) NOT NULL,
    create_ts timestamptz NOT NULL DEFAULT now(),
    update_ts timestamptz NOT NULL DEFAULT now(),
    name varchar(64) NOT NULL,
    description text NOT NULL,
    active boolean NOT NULL DEFAULT true,
    CONSTRAINT oauth2_scope_pkey PRIMARY KEY (scope_id),
    CONSTRAINT oauth2_scope_name_key UNIQUE (name)
);