
#!/bin/bash
# <PERSON>ript to run Smart test data generation

set -e

cd "$(dirname "$0")"

# Check if a specific database is requested
if [ "$1" = "rrr" ]; then
    echo "Generating test data for RRR database..."
    ../smart/conversion/run-test-data-rrr "${@:2}"
elif [ "$1" = "df4l" ]; then
    echo "Generating test data for DF4L database..."
    ../smart/conversion/run-test-data "${@:2}"
elif [ "$1" = "all" ]; then
    echo "Generating test data for all databases..."
    echo "Generating DF4L test data..."
    ../smart/conversion/run-test-data "${@:2}"
    echo "Generating RRR test data..."
    ../smart/conversion/run-test-data-rrr "${@:2}"
else
    echo "Usage: $0 {df4l|rrr|all} [options]"
    echo "  df4l  - Generate test data for DF4L database"
    echo "  rrr   - Generate test data for RRR database"
    echo "  all   - Generate test data for all databases"
    echo ""
    echo "Options:"
    echo "  --seed SEED  - Use specific seed for deterministic generation"
    exit 1
fi
