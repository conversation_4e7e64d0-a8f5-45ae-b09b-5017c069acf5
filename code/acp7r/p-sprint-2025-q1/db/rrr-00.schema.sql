
CREATE SCHEMA IF NOT EXISTS rrr;

CREATE TABLE rrr.agent (
    agent_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    identity_uuid uuid,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    agent_esid varchar(7) NOT NULL,
    gbu_agent_esid varchar(64),
    first_name varchar(128) NOT NULL,
    last_name varchar(128) NOT NULL,
    email varchar(256),
    phone varchar(64),
    address1 varchar(128),
    address2 varchar(128),
    city varchar(128),
    state varchar(16),
    zip varchar(20),
    country varchar(128),
    active boolean NOT NULL DEFAULT true,
    admin_note text,
    data jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT "agent_pkey" PRIMARY KEY ("agent_uuid"),
    CONSTRAINT fk_identity FOREIGN KEY (identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT fk_state FOREIGN KEY (state) REFERENCES addr_iso.us_state(state_code) ON DELETE RESTRICT,
    CONSTRAINT agent_esid_unique UNIQUE (agent_esid)
);

CREATE TABLE rrr.signup_agent (
    signup_agent_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    session_token varchar(64) NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    create_addr cidr NOT NULL,
    first_name varchar(20),
    last_name varchar(20),
    email varchar(128),
    email_code varchar(4),
    email_code_sent_ts timestamptz(6),
    email_code_expire_ts timestamptz(6),
    email_code_attempts int4 NOT NULL DEFAULT 0,
    email_verified_ts timestamptz(6),
    phone varchar(64),
    phone_code varchar(4),
    phone_code_sent_ts timestamptz(6),
    phone_code_expire_ts timestamptz(6),
    phone_code_attempts int4 NOT NULL DEFAULT 0,
    phone_verified_ts timestamptz(6),
    terms_document_uuid uuid,
    terms_revision varchar(64),
    terms_accepted_name varchar(100),
    terms_accepted_ts timestamptz(6),
    terms_accepted_addr cidr,
    signup_completed_ts timestamptz(6),
    agent_uuid_created uuid,
    CONSTRAINT "signup_agent_pkey" PRIMARY KEY (signup_agent_uuid),
    CONSTRAINT "signup_agent>>agent" FOREIGN KEY (agent_uuid_created) REFERENCES rrr.agent (agent_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT "signup_agent>>terms_document" FOREIGN KEY (terms_document_uuid) REFERENCES "legal_plane"."document" (document_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    
    UNIQUE (agent_uuid_created)
);
