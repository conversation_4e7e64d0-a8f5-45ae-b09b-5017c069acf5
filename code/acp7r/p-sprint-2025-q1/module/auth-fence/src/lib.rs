// TODO: Add property/attribute for redis session key for ease of use storing/retreiving user data
// TODO: Encrypt/decrypt token struct in redis
// TODO: Token introspection and revocation
// TODO: Fix logging, should log respective provider information including accurate protocol

pub use types::{BasicUserInfo, redis_session_key};

pub mod api;
pub mod core;
pub mod oauth2;
pub mod openid;
pub mod postgres;
pub mod web;

pub trait AuthProvider: Send + Sync {
    fn get_button_html(&self) -> String;
}

pub mod types {
    use crate::oauth2::provider::{
        OAuth2ProviderType, facebook::OAuth2FaceBookProvider, google::OAuth2GoogleProvider,
        linkedin::OAuth2ProviderLinkedIn, microsoft::OAuth2MicrosoftProvider,
    };
    use crate::openid::microsoft::OpenIDMicrosoftConfig;
    use crate::openid::{google::OpenIDGoogleProvider, microsoft::OpenIDMicrosoftProvider};

    pub use super::postgres;
    pub use oauth2::TokenResponse;
    pub use oauth2::basic::BasicTokenResponse;
    pub use reqwest;

    use indexmap::IndexMap;
    use std::collections::HashSet;

    #[derive(Debug, serde::Deserialize, serde::Serialize)]
    pub enum IdentityStruct {
        Anonymous,
        User(super::api::identity::Identity),
    }

    #[derive(serde::Deserialize, serde::Serialize, Debug)]
    pub struct BasicUserInfo {
        pub provider: String,
        pub id: String,
        pub email: String,
        pub first_name: String,
        pub last_name: String,
        pub avatar_uri: String,
    }

    pub enum AuthProviderType {
        OAuth2(Box<dyn crate::oauth2::OAuth2Provider>),
        OpenID(Box<dyn crate::openid::OpenIDProvider>),
    }

    // TODO: Fix pattern for OAuth2 config/provider registration
    #[derive(Debug, Clone, serde::Deserialize)]
    pub struct OAuth2Config {
        pub key: String,
        pub provider: OAuth2ProviderType,
        pub client_id: String,
        pub client_secret: String,
        pub scopes: Option<HashSet<String>>,
        pub pkce_enabled: Option<bool>,
    }

    #[derive(Debug, Clone, serde::Deserialize)]
    #[serde(tag = "provider", rename_all = "lowercase")]
    pub enum OpenIDConfig {
        Google(BaseOpenIDConfig),
        Microsoft(OpenIDMicrosoftConfig),
        Custom(BaseOpenIDConfig),
    }

    #[derive(Debug, serde::Deserialize, Clone)]
    //#[serde(tag = "provider")]
    pub struct BaseOpenIDConfig {
        pub key: String,
        pub client_id: String,
        pub client_secret: String,
        pub scopes: Option<HashSet<String>>,
    }

    #[derive(Debug, serde::Deserialize, Clone)]
    pub struct ModuleConfig {
        #[serde(default)]
        pub oauth2: IndexMap<String, OAuth2Config>,
        #[serde(default)]
        pub openid: IndexMap<String, OpenIDConfig>,
    }

    pub struct ModuleStruct {
        providers: IndexMap<String, Box<AuthProviderType>>,
    }

    impl std::fmt::Debug for ModuleStruct {
        fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
            // TODO: expand this to show more info, like the client ids and configured providers, redact secrets
            let mut providers = Vec::with_capacity(self.providers.len());

            for provider in self.providers.values() {
                match provider.as_ref() {
                    AuthProviderType::OAuth2(provider) => {
                        providers.push((provider.key(), provider.provider().to_string()));
                    }
                    AuthProviderType::OpenID(provider) => {
                        providers.push((provider.key(), provider.provider().to_string()));
                    }
                }
            }

            writeln!(f, "ModSystem {{")?;
            writeln!(f, "  providers: {:?}", providers)?;
            writeln!(f, "}}")?;

            Ok(())
        }
    }

    impl approck::Module for ModuleStruct {
        type Config = ModuleConfig;
        fn new(config: Self::Config) -> granite::Result<Self> {
            // TODO: Move all providers into a single structure by adding a protocol field
            // NOTE: No provider will ever be configured for both protocols for the same provider and set of credentials
            // this is where auth provider configs can be processed
            let mut system = ModuleStruct {
                providers: IndexMap::new(),
            };

            // TODO: Validate only one provider/protocol is configured for each key
            for provider in config.oauth2.values() {
                match provider.provider {
                    OAuth2ProviderType::Apple => {
                        println!("Apple OAuth2 provider not implemented");
                    }
                    OAuth2ProviderType::Facebook => {
                        let facebook = AuthProviderType::OAuth2(Box::new(OAuth2FaceBookProvider {
                            config: provider.clone(),
                        }));
                        system.register_provider(facebook);
                    }
                    OAuth2ProviderType::Github => {
                        println!("Github OAuth2 provider not implemented");
                    }
                    OAuth2ProviderType::Google => {
                        let google = AuthProviderType::OAuth2(Box::new(OAuth2GoogleProvider {
                            config: provider.clone(),
                        }));
                        system.register_provider(google);
                    }
                    OAuth2ProviderType::LinkedIn => {
                        let linkedin = AuthProviderType::OAuth2(Box::new(OAuth2ProviderLinkedIn {
                            config: provider.clone(),
                        }));
                        system.register_provider(linkedin);
                    }
                    OAuth2ProviderType::Microsoft => {
                        let microsoft =
                            AuthProviderType::OAuth2(Box::new(OAuth2MicrosoftProvider {
                                config: provider.clone(),
                            }));
                        system.register_provider(microsoft);
                    }
                    OAuth2ProviderType::Twitter => {
                        println!("Twitter OAuth2 provider not implemented");
                    }
                    OAuth2ProviderType::Custom(_) => {
                        println!(
                            "Custom OAuth2 providers are implemented and registered at the app level"
                        );
                    }
                }
            }

            for provider in config.openid.values() {
                match provider {
                    OpenIDConfig::Google(provider) => {
                        let google = AuthProviderType::OpenID(Box::new(OpenIDGoogleProvider {
                            config: provider.clone(),
                        }));
                        system.register_provider(google);
                    }
                    OpenIDConfig::Microsoft(provider) => {
                        let microsoft =
                            AuthProviderType::OpenID(Box::new(OpenIDMicrosoftProvider {
                                config: provider.clone(),
                            }));
                        system.register_provider(microsoft);
                    }
                    OpenIDConfig::Custom(_provider) => {
                        println!(
                            "Custom OpenID providers are implemented and registered at the app level"
                        );
                    }
                }
            }

            Ok(system)
        }
        async fn init(&self) -> granite::Result<()> {
            Ok(())
        }
    }

    impl ModuleStruct {
        pub async fn get_user_identity(
            &self,
            req_session_token: &str,
            redis: &'_ mut approck_redis::RedisCX<'_>,
        ) -> granite::Result<Option<crate::api::identity::Identity>> {
            let redis_session_key = super::redis_session_key(req_session_token);
            match redis
                .hget_val::<String>(redis_session_key.as_str(), "identity")
                .await
            {
                Ok(identity) => match serde_json::from_str(identity.as_str()).map(Some) {
                    Ok(info) => Ok(info),
                    Err(e) => Err(granite::process_error!("Error decoding user identity: {e}")
                        .add_context(format!("Session key `{}`", redis_session_key))),
                },
                Err(e) => {
                    use granite::Error;
                    // TODO: BETTER REDIS ERROR HANDLING AND NULL CHECKING!!!
                    match Error::get_source(&e) {
                        // null response means no user info - not an error
                        Some(source) if source.to_string().contains("response was nil") => Ok(None),
                        _ => Err(granite::process_error!(
                            "Error getting user identity from redis: {e}"
                        )
                        .add_context(format!("Session key `{}`", redis_session_key))),
                    }
                }
            }
        }

        pub fn register_provider(&mut self, provider: crate::types::AuthProviderType) {
            use crate::types::AuthProviderType;

            match provider {
                AuthProviderType::OAuth2(provider) => {
                    self.providers
                        .insert(provider.key(), Box::new(AuthProviderType::OAuth2(provider)));
                }
                AuthProviderType::OpenID(provider) => {
                    self.providers
                        .insert(provider.key(), Box::new(AuthProviderType::OpenID(provider)));
                }
            }
        }

        pub fn iter_providers(&self) -> impl Iterator<Item = &crate::types::AuthProviderType> {
            self.providers.values().map(|provider| provider.as_ref())
        }

        pub fn get_provider(&self, key: &str) -> Option<&crate::types::AuthProviderType> {
            self.providers.get(key).map(|provider| provider.as_ref())
        }
    }

    pub fn redis_session_key(req_session_token: &str) -> String {
        format!("{}/auth_fence", req_session_token)
    }

    pub async fn logout(
        ip_addr: &str,
        req_session_token: &str,
        auth_fence: &ModuleStruct,
        db: impl approck_postgres::DB,
        redis: &mut approck_redis::RedisCX<'_>,
    ) -> granite::Result<()> {
        let redis_session_key = crate::redis_session_key(req_session_token);

        // get the user info first - we need it for the log
        let identity = match auth_fence.get_user_identity(req_session_token, redis).await {
            Ok(user_info) => {
                if let Some(user_info) = user_info {
                    user_info
                } else {
                    return Err(granite::process_error!(
                        "No user info found in redis - no user to log out"
                    ));
                }
            }
            Err(e) => {
                return Err(granite::process_error!("Error getting user info").add_context(e));
            }
        };

        match redis.del(&redis_session_key).await {
            Ok(_) => {
                // TODO: AuthType is assumed at this point
                match postgres::log::auth_log(
                    &db,
                    postgres::log::AuthLogData {
                        create_addr: ip_addr.to_string(),
                        session_token: req_session_token.to_string(),
                        identity_uuid: Some(identity.identity_uuid),
                        user_esid: None,
                        user_email: identity.email,
                        auth_type: "OAuth2".to_string(),
                        auth_action: "Logout".to_string(),
                        auth_provider: None,
                        success: true,
                        blocked: false,
                        data: None,
                    },
                )
                .await
                {
                    Ok(_) => Ok(()),
                    Err(e) => {
                        Err(granite::process_error!("Error during auth logout logging")
                            .add_context(e))
                    }
                }
            }
            Err(e) => Err(
                // TODO: AuthType is assumed at this point
                match postgres::log::auth_log(
                    &db,
                    postgres::log::AuthLogData {
                        create_addr: ip_addr.to_string(),
                        session_token: req_session_token.to_string(),
                        identity_uuid: Some(identity.identity_uuid),
                        user_esid: None,
                        user_email: identity.email,
                        auth_type: "OAuth2".to_string(),
                        auth_action: "Logout".to_string(),
                        auth_provider: None,
                        success: false,
                        blocked: false,
                        data: Some(format!("Error deleting redis session: {e}")),
                    },
                )
                .await
                {
                    Ok(_) => {
                        granite::process_error!("Error deleting redis session during logout: {e}")
                            .add_context(format!("Session key `{}`", redis_session_key))
                    }
                    Err(e) => {
                        granite::process_error!("Error performing auth logging during logout")
                            .add_context(e)
                    }
                },
            ),
        }
    }
}

pub trait App:
    approck::App + approck::server::App + approck_redis::App + approck_postgres::App
{
    fn login_url<'a>(&self) -> &'a str {
        "/auth/"
    }
    fn login_username_url<'a>(&self) -> &'a str {
        "/auth/login"
    }
    fn logout_url<'a>(&self) -> &'a str {
        "/auth/logout"
    }
    fn after_login_next_url<'a>(&self, next_uri: Option<&'a str>) -> &'a str;
    fn auth_fence_system(&self) -> &types::ModuleStruct;
}

pub trait Identity: bux::Identity + std::fmt::Debug {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
    fn is_logged_in(&self) -> bool;
    fn identity_uuid(&self) -> Option<granite::Uuid>;
    fn remote_address(&self) -> std::net::IpAddr;
    fn session_token(&self) -> String;
}

pub trait Document: bux::document::Base + bux::document::PageNav {}

pub fn ml_myaccount_security(identity_uuid: granite::Uuid) -> String {
    format!("/myaccount/{}/security/", identity_uuid)
}
