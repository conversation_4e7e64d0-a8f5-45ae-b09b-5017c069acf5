use super::{OAuth2Config, OAuth2ProviderType, oauth2};
use crate::types::BasicUserInfo;
use async_trait::async_trait;

pub struct OAuth2ProviderLinkedIn {
    pub config: OAuth2Config,
}

#[allow(dead_code)]
#[derive(serde::Deserialize, Debug)]
pub struct LinkedInUserInfo {
    email: String,
    email_verified: bool,
    family_name: String,
    given_name: String,
    locale: LinkedInLocale,
    name: String,
    picture: String,
    sub: String,
}

#[allow(dead_code)]
#[derive(serde::Deserialize, Debug)]
pub struct LinkedInLocale {
    country: String,
    language: String,
}

impl From<LinkedInUserInfo> for BasicUserInfo {
    fn from(user_info: LinkedInUserInfo) -> Self {
        BasicUserInfo {
            provider: "linkedin".to_string(),
            id: user_info.sub,
            email: user_info.email,
            first_name: user_info.given_name,
            last_name: user_info.family_name,
            avatar_uri: user_info.picture,
        }
    }
}

#[async_trait]
impl crate::oauth2::OAuth2Provider for OAuth2ProviderLinkedIn {
    fn key(&self) -> String {
        self.config.key.clone()
    }
    fn provider(&self) -> OAuth2ProviderType {
        OAuth2ProviderType::LinkedIn
    }
    fn client_id(&self) -> String {
        self.config.client_id.clone()
    }
    fn client_secret(&self) -> String {
        self.config.client_secret.clone()
    }
    fn get_scopes(&self) -> Vec<String> {
        vec![
            "openid".to_string(),
            "profile".to_string(),
            "email".to_string(),
        ]
    }
    fn get_auth_uri(&self) -> String {
        // TODO: Enable PKCE flow - contact LinkedIn??? Previously referred to Stack Overflow
        // Documentation - https://learn.microsoft.com/en-us/linkedin/shared/authentication/authorization-code-flow-native
        //auth_uri: "https://www.linkedin.com/oauth/native-pkce/authorization".to_string(),
        "https://www.linkedin.com/oauth/v2/authorization".to_string()
    }
    fn get_token_uri(&self) -> String {
        "https://www.linkedin.com/oauth/v2/accessToken".to_string()
    }
    fn get_user_info_uri(&self) -> String {
        "https://api.linkedin.com/v2/userinfo".to_string()
    }
    fn get_button_html(&self, next_uri: Option<String>) -> maud::PreEscaped<String> {
        maud::html!(
            a href=(self.get_button_uri(next_uri)) class="btn btn-outline-custom btn-linkedin social-btn" {
                i class="fab fa-linkedin" { " LinkedIn" }
            }
        )
    }
    async fn get_user_info(
        &self,
        token: &oauth2::basic::BasicTokenResponse,
    ) -> granite::Result<BasicUserInfo> {
        use oauth2::TokenResponse;
        use reqwest;

        let reqwest_client = reqwest::Client::new();
        let user_info_uri = self.get_user_info_uri();
        let response = reqwest_client
            .get(user_info_uri.as_str())
            .header(
                "Authorization",
                format!("Bearer {}", token.access_token().secret()),
            )
            .send()
            .await
            .map_err(|e| granite::Error::new(granite::ErrorType::ProcessError).add_context(e))?;
        let user_info: BasicUserInfo = {
            let linkedin_user_info: LinkedInUserInfo =
                response.json::<LinkedInUserInfo>().await.map_err(|e| {
                    granite::Error::new(granite::ErrorType::ProcessError).add_context(e)
                })?;

            linkedin_user_info.into()
        };

        Ok(user_info)
    }
    async fn get_access_token(
        &self,
        code: &str,
        _pkce_code_verifier: &str,
        app_url: String,
    ) -> granite::Result<oauth2::basic::BasicTokenResponse> {
        use reqwest;

        // `oauth2::basic::BasicTokenResponse` needs `use oauth2::TokenResponse;` to be in scope to use `.access_token()`
        // TODO: Get proper PKCE flow enabled - LinkedIn staff has to enable PKCE workflow for each app
        let redirect_url = self.get_redirect_uri(app_url);
        let client_id = self.client_id();
        let client_secret = self.client_secret();

        let params = [
            ("code", code),
            //("code_verifier", pkce_code_verifier),
            ("grant_type", "authorization_code"),
            ("client_id", client_id.as_str()),
            ("client_secret", client_secret.as_str()),
            ("redirect_uri", redirect_url.as_str()),
        ];

        let reqwest_client = reqwest::Client::new();

        // TODO: clean this up a bit
        let token = match reqwest_client
            .post(self.get_token_uri().as_str())
            .form(&params)
            .send()
            .await
        {
            Ok(token) => match token.json::<oauth2::basic::BasicTokenResponse>().await {
                Ok(token) => token,
                Err(e) => {
                    return Err(granite::process_error!(
                        "Error parsing access token response: {:?}",
                        e
                    ));
                }
            },
            Err(e) => {
                return Err(granite::process_error!(
                    "Error getting access token: {:?}",
                    e
                ));
            }
        };

        Ok(token)
    }
}
