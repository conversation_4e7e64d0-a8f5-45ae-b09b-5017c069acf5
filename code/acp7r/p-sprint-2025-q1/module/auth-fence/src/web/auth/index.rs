#[approck::http(GET /auth/?next_uri=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(qs: QueryString, app: App, doc: Document) -> Response {
        use maud::html;

        let auth_fence = app.auth_fence_system();
        let next_uri = qs.next_uri;
        let email_uri = match next_uri {
            Some(ref next_uri) => format!("/auth/email?next_uri={}", next_uri),
            None => app.login_username_url().to_string(),
        };

        // get an html fragment, iterate over providers and call get_button_html on each appending to the fragment
        let mut button_html = Vec::new();
        for provider in auth_fence.iter_providers() {
            let next_uri = next_uri.clone();
            match provider {
                crate::types::AuthProviderType::OAuth2(p) => {
                    button_html.push(p.get_button_html(next_uri));
                }
                crate::types::AuthProviderType::OpenID(p) => {
                    button_html.push(p.get_button_html(next_uri));
                }
            }
        }

        doc.add_css("./mod.css");
        doc.add_js("./mod.js");

        doc.add_body(html!(
            auth-panel {
                auth-content {
                    header {
                        h2 { "Login" }
                    }
                    hr;
                    @if !button_html.is_empty() {
                        login-buttons {
                            @for button in button_html {
                                (button)
                            }
                        }
                        hr;
                        p style="text-align: center;" { "Or" }
                        hr;
                    }
                    p.te style="text-align: center;" {
                        a href=(email_uri) { "Login with username and password" }
                    }
                }
            }
        ));
        Response::HTML(doc.into())
    }
}
