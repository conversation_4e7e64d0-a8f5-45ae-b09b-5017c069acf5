#[approck::http(GET /auth/login; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Login");
        doc.add_css("/auth/login.css");
        doc.add_js("/auth/login.js");

        let mut login_panel = bux::component::login_form_panel("Login", "/auth/");

        login_panel.add_body(html!(
            (bux::input::text::string::name_label_value("username", "Username", None))
            (bux::input::text::password::bux_input_text_password("password", "Password", None))
            hr;
            (bux::input::checkbox::bux_checkbox("remember_me", "Remember me?", false))
        ));

        doc.add_body(html!((login_panel)));

        Response::HTML(doc.into())
    }
}
