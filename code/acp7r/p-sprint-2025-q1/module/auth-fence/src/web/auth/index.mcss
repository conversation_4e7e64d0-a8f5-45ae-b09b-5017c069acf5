
auth-panel {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80vh;
}

auth-content {
    width: 400px;
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #ccc;
}

h2 {
    text-align: center;
}

login-buttons {
    display: grid;
    gap: 0.5rem;

    a.btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        font-size: 1rem;
        font-weight: 500;
        background-color: #fff;
        color: #000;
        border: 1px solid #ddd;
        text-decoration: none;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        transition: background-color 0.3s ease;

        &:hover {
            background-color: #f0f0f0;
        }

        i {
            margin-right: 0.5rem;
        }

        &.google {
            border-color: #ea4335;

            i {
                color: #ea4335;
            }
        }

        &.microsoft {
            border-color: #0078d4;

            i {
                color: #0078d4;
            }
        }

        &.apple {
            border-color: #000;

            i {
                color: #000;
            }
        }
    }
}


footer{
    text-align: center;

    p {
        color: #6c757d;
    }
}
    
