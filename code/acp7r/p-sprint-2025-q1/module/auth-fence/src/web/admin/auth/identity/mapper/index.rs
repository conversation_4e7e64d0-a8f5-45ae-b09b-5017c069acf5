#[approck::http(GET /admin/auth/identity/{identity_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    use maud::html;

    use crate::api::admin::identity::detail::detail::Source as PermissionSource;

    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        let identity_uuid = path.identity_uuid;
        let identity_detail = crate::api::admin::identity::detail::detail::call(
            app,
            identity,
            crate::api::admin::identity::detail::detail::Input { identity_uuid },
        )
        .await?;

        // Fetch login details for the identity
        let login_details = match crate::api::admin::identity::login::detail::call(
            app,
            identity,
            crate::api::admin::identity::login::detail::Input { identity_uuid },
        )
        .await
        {
            Ok(details) => Some(details),
            Err(e) => {
                approck::warn!(
                    "Failed to fetch login details for identity {}: {}",
                    identity_uuid,
                    e
                );
                None
            }
        };

        // Fetch SSO providers for the identity
        let sso_providers = match crate::api::identity::sso::providers::index::call(
            app,
            identity,
            crate::api::identity::sso::providers::index::Input { identity_uuid },
        )
        .await
        {
            Ok(providers) => Some(providers.providers),
            Err(e) => {
                approck::warn!(
                    "Failed to fetch SSO providers for identity {}: {}",
                    identity_uuid,
                    e
                );
                None
            }
        };

        doc.set_title("Identity Details");

        // Prepare display values
        let display_email = identity_detail
            .email
            .as_deref()
            .unwrap_or("<EMAIL>");
        let display_name = &identity_detail.name;

        // Prepare URLs for edit links
        let edit_url = format!("/admin/auth/identity/{}/edit", identity_uuid);
        let roles_url = identity_detail.ml_admin("roles");
        let permissions_url = identity_detail.ml_admin("permissions");

        // Create separate InsightDeck instances for different logical groupings

        // 1. Identity Identification InsightDeck (with action buttons in header)
        let mut identity_identification =
            bux::component::insight_deck::InsightDeck::new("Identity Identification");
        identity_identification
            .description("Core identity identifiers and account creation information.");

        // Add general action buttons to the header (edit/delete only)
        identity_identification.add_button(bux::button::link::edit(&edit_url));
        identity_identification.add_button(html!(" "));
        identity_identification.add_button(bux::button::link::delete(&format!(
            "/admin/auth/identity/{}/delete",
            identity_uuid
        )));

        identity_identification.add_basic_tile(
            "fas fa-id-badge",
            "Identity UUID",
            html!((identity_detail.identity_uuid)),
        );

        identity_identification.add_basic_tile(
            "fas fa-user-tag",
            "Identity Type",
            html!((identity_detail.identity_type)),
        );

        identity_identification.add_basic_tile(
            "fas fa-calendar-plus",
            "Created On",
            html!((identity_detail.create_ts)),
        );

        // 2. Account Information InsightDeck
        let mut account_information =
            bux::component::insight_deck::InsightDeck::new("Account Information");
        account_information
            .description("Login credentials and authentication methods for this identity.");

        if let Some(ref email) = identity_detail.email {
            account_information.add_edit_row_email(Some(email), &edit_url);
        } else {
            account_information.add_edit_row_email(None, &edit_url);
        }

        // Login information
        if let Some(ref login) = login_details {
            if let Some(ref login_info) = login.login {
                account_information.add_edit_row(
                    "fas fa-user-circle",
                    "Username",
                    html!((login_info.username)),
                    &edit_url,
                );

                account_information.add_edit_row(
                    "fas fa-toggle-on",
                    "Login Status",
                    html! {
                        @if login_info.active {
                            label-tag.success { "Active" }
                        } @else {
                            label-tag.danger { "Inactive" }
                        }
                    },
                    &edit_url,
                );
            } else {
                account_information.add_basic_row(
                    "fas fa-user-circle",
                    "Username",
                    html!(span.text-muted { "No username/password login configured" }),
                );
            }
        } else {
            account_information.add_basic_row(
                "fas fa-user-circle",
                "Username",
                html!(span.text-muted { "Login information unavailable" }),
            );
        }

        // SSO Sign-in methods if available
        if let Some(ref providers) = sso_providers {
            let google_provider = providers
                .iter()
                .find(|p| p.ssopro_xsid.to_lowercase() == "google");
            let microsoft_provider = providers
                .iter()
                .find(|p| p.ssopro_xsid.to_lowercase() == "microsoft");

            account_information.add_basic_row(
                "fas fa-key",
                "SSO Sign-in Methods",
                html! {
                    div.signin-methods {
                        @if let Some(google) = google_provider {
                            div.signin-method {
                                i.fab.fa-google aria-hidden="true" {}
                                span { "Google: " }
                                @if google.is_connected {
                                    label-tag.success { "Connected" }
                                } @else {
                                    label-tag.warning { "Disconnected" }
                                }
                            }
                        } @else {
                            div.signin-method {
                                i.fab.fa-google aria-hidden="true" {}
                                span { "Google: " }
                                label-tag.secondary { "Not Available" }
                            }
                        }
                        @if let Some(microsoft) = microsoft_provider {
                            div.signin-method {
                                i.fab.fa-microsoft aria-hidden="true" {}
                                span { "Microsoft: " }
                                @if microsoft.is_connected {
                                    label-tag.success { "Connected" }
                                } @else {
                                    label-tag.warning { "Disconnected" }
                                }
                            }
                        } @else {
                            div.signin-method {
                                i.fab.fa-microsoft aria-hidden="true" {}
                                span { "Microsoft: " }
                                label-tag.secondary { "Not Available" }
                            }
                        }
                    }
                },
            );
        }

        // Add note if available
        if let Some(ref note) = identity_detail.note {
            if !note.trim().is_empty() {
                account_information.add_edit_row(
                    "fas fa-sticky-note",
                    "Note",
                    html!((note)),
                    &edit_url,
                );
            }
        }

        // Add avatar URI if available
        if let Some(ref avatar_uri) = identity_detail.avatar_uri {
            if !avatar_uri.trim().is_empty() {
                account_information.add_edit_row(
                    "fas fa-image",
                    "Avatar URI",
                    html!((avatar_uri)),
                    &edit_url,
                );
            }
        }

        // 3. Authorization Information InsightDeck
        let mut authorization_information =
            bux::component::insight_deck::InsightDeck::new("Authorization Information");
        authorization_information.description("Roles and permissions assigned to this identity.");

        // Add authorization-related action buttons to the header
        authorization_information.add_button(bux::button::link::label_icon_class(
            "Manage Roles",
            "fas fa-users-cog",
            &roles_url,
            "sm primary",
        ));
        authorization_information.add_button(html!(" "));
        authorization_information.add_button(bux::button::link::label_icon_class(
            "Manage Permissions",
            "fas fa-key",
            &permissions_url,
            "sm secondary",
        ));

        authorization_information.add_basic_row(
            "fas fa-users-cog",
            "Roles",
            html! {
                @if identity_detail.roles.is_empty() {
                    span.text-muted { "No roles assigned" }
                } @else {
                    @for role in &identity_detail.roles {
                        div { (role.name) }
                    }
                }
            },
        );

        authorization_information.add_basic_row(
            "fas fa-key",
            "Permissions",
            html! {
                @if identity_detail.permissions.is_empty() {
                    span.text-muted { "No permissions assigned" }
                } @else {
                    @for perm in &identity_detail.permissions {
                        div {
                            (perm.name)
                            small {
                                em {
                                    @match perm.source {
                                        PermissionSource::Role => { " (Inherited)" }
                                        PermissionSource::Permission => { " (Assigned)" }
                                        PermissionSource::RolePermission => { " (Inherited, Assigned)" }
                                    }
                                }
                            }
                        }
                    }
                }
            },
        );

        authorization_information.add_edit_row(
            "fas fa-toggle-on",
            "Status",
            html! {
                @if identity_detail.active {
                    label-tag.success { "Active" }
                } @else {
                    label-tag.danger { "Inactive" }
                }
            },
            &edit_url,
        );

        doc.add_body(html!(
            admin-identity-detail {
                grid-12 {
                    cell-3 {
                        panel {
                            content {
                                contact-info {
                                    h1 { (display_name) }
                                    p.email {
                                        @if let Some(email) = &identity_detail.email {
                                            a href=(format!("mailto:{}", email)) { (email) }
                                        } @else {
                                            (display_email)
                                        }
                                    }
                                    hr;
                                    @if identity_detail.active {
                                        label-tag.success { "Active Identity" }
                                    } @else {
                                        label-tag.danger { "Inactive Identity" }
                                    }
                                }
                            }
                        }
                    }
                    cell-9 {
                        // This is the new rendering implementation for identity details using the insight deck
                        @for deck in &[&identity_identification, &account_information, &authorization_information] {
                            (deck)
                        }
                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
