//! # Twilio SMS API Module
//!
//! This module provides SMS functionality through the Twilio API.
//!
//! ## Usage
//!
//! ```ignore
//! // In your application code:
//! let result = app.twilio().send_sms("support", "+15551234567", "Hello from <PERSON><PERSON><PERSON>!").await?;
//! ```
//!
//! ## Configuration
//!
//! Configure in your app's config file:
//! ```toml
//! [api_twilio]
//! key_sid = "AC9086bafa42b30433dbd64eb7f5db570a"
//! key_secret = "your_auth_token_here"
//!
//! [api_twilio.sender]
//! support = "+15551234567"
//! alerts = "+15559876543"
//! ```

pub mod api;
pub mod web;

use std::collections::HashMap;

pub trait App: approck::App + approck_postgres::App {}

pub trait Identity: approck::Identity {}

pub trait Document: bux::document::Base {}

#[derive(serde::Deserialize)]
pub struct ModuleConfig {
    pub key_sid: String,
    pub key_secret: String,
    pub sender: HashMap<String, String>,
}

pub struct ModuleStruct {
    pub key_sid: String,
    pub key_secret: String,
    pub sender_map: HashMap<String, String>,
}

impl ModuleStruct {
    pub async fn send_sms(&self, sender_key: &str, to: &str, message: &str) -> granite::Result<()> {
        let from_number = self
            .sender_map
            .get(sender_key)
            .ok_or_else(|| granite::Error::validation(format!("Unknown sender: {}", sender_key)))?;

        let key_sid = &self.key_sid;
        let key_secret = &self.key_secret;

        send_sms(key_sid, key_secret, from_number, to, message).await
    }
}

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;
    async fn new(config: Self::Config) -> granite::Result<Self> {
        Ok(Self {
            key_sid: config.key_sid,
            key_secret: config.key_secret,
            sender_map: config.sender,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}

async fn send_sms(
    key_sid: &str,
    key_secret: &str,
    from_number: &str,
    to_number: &str,
    message: &str,
) -> granite::Result<()> {
    let client = reqwest::Client::new();

    let url = format!(
        "https://api.twilio.com/2010-04-01/Accounts/{}/Messages.json",
        key_sid
    );

    let form_data = [("From", from_number), ("To", to_number), ("Body", message)];

    let response = client
        .post(&url)
        .basic_auth(key_sid, Some(key_secret))
        .form(&form_data)
        .send()
        .await
        .map_err(|e| granite::Error::api_request_error(format!("Failed to send SMS: {}", e)))?;

    if response.status().is_success() {
        Ok(())
    } else {
        let status = response.status();
        let body = response
            .text()
            .await
            .unwrap_or_else(|_| "Unable to read response body".to_string());
        Err(granite::Error::api_request_error(format!(
            "Twilio API error {}: {}",
            status, body
        )))
    }
}
