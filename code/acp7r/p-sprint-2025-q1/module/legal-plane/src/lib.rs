pub mod api;
pub mod web;
use granite::Uuid;

pub trait App:
    approck::App + approck::server::App + approck_redis::App + approck_postgres::App + auth_fence::App
{
}

pub trait Identity: bux::Identity + auth_fence::Identity + std::fmt::Debug {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
    fn document_read(&self) -> bool {
        false
    }
    fn document_list(&self) -> bool {
        false
    }
}

pub trait Document: bux::document::Base + bux::document::PageNav + auth_fence::Document {}

#[derive(Debug, serde::Deserialize, Default)]
pub struct ModuleConfig {
    // Configuration options for legal-plane module
}

pub struct ModuleStruct {
    // Module state and configuration
}

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;

    async fn new(_config: Self::Config) -> granite::Result<Self> {
        let system = ModuleStruct {};
        Ok(system)
    }

    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}

pub fn ml_document_details(document_uuid: Uuid) -> String {
    format!("/admin/legal/document/{}/", document_uuid)
}
