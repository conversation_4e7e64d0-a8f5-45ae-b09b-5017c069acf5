[package]
name = "legal-plane"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.acp]
module = {}

extends = ["approck", "bux", "granite", "auth-fence"]

[dependencies]
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-redis = { workspace = true }
approck-postgres = { workspace = true }
auth-fence = { workspace = true }

async-trait = { workspace = true }
indexmap = { workspace = true, features = ["serde"] }
maud = { workspace = true }
postgres-types = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tokio = { workspace = true }
chrono = { workspace = true, features = ["serde"] }