pub mod api;
pub mod web;

pub trait App: approck::App + approck_postgres::App {}

pub trait Identity: approck::Identity {}

pub trait Document: bux::document::Base {}

#[derive(serde::Deserialize)]
pub struct ModuleConfig {
    pub api_key: String,
    pub domain: String,
}
pub struct ModuleStruct {
    pub api_key: String,
    pub domain: String,
}

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;
    async fn new(config: Self::Config) -> granite::Result<Self> {
        Ok(Self {
            api_key: config.api_key,
            domain: config.domain,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}
