[package]
name = "auth-fence-provider"
version = "0.1.0"
edition = "2024"

[package.metadata.acp]
module = {}
extends = ["auth-fence", "approck", "bux", "granite"]

[dependencies]
auth-fence = { workspace = true }
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-postgres = { workspace = true }
approck-redis = { workspace = true }


aes-gcm = { workspace = true }
base64_light = { workspace = true }
chrono = { workspace = true }
maud = { workspace = true }
rand = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
sha2 = { workspace = true }
urlencoding = { workspace = true }
