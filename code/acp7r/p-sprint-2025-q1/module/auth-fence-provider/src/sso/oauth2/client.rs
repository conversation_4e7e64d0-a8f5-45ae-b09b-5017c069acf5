pub struct OAuth2Client {
    pub client_uuid: granite::Uuid,
    pub name: String,
    pub secret_hash: String,
    pub secret_salt: String,
    pub redirect_uris: Vec<String>,
    pub scope_ids: Vec<String>,
    pub allowed_cidr: Vec<String>,
    pub active: bool,
}

// load client object
pub async fn load_client(
    db: &impl approck_postgres::DB,
    client_uuid: &granite::Uuid,
) -> granite::Result<OAuth2Client> {
    approck::debug!("Loading OAuth2 client from database: {}", client_uuid);

    // look up the client record
    match granite::pg_row!(
        db = db;
        args = {
            $client_uuid: client_uuid,
        };
        row = {
            client_uuid: Uuid,
            name: String,
            secret_hash: String,
            secret_salt: String,
            redirect_uris: Vec<String>,
            scope_ids: Vec<String>,
            allowed_cidr: Vec<String>,
            active: bool,
        };
        SELECT
            oauth2_client_uuid,
            name,
            secret_hash,
            secret_salt,
            redirect_uris::text[] AS redirect_uris,
            scope_ids::text[] AS scope_ids,
            allowed_cidr::text[] AS allowed_cidr,
            active
        FROM
            auth_fence_provider.oauth2_client
        WHERE TRUE
            AND oauth2_client_uuid = $client_uuid::uuid
    )
    .await
    {
        Ok(row) => {
            approck::debug!("OAuth2 client loaded successfully: {}", row.name);
            approck::debug!("Client redirect URIs: {:?}", row.redirect_uris);
            approck::debug!("Client allowed scopes: {:?}", row.scope_ids);
            approck::debug!("Client allowed CIDR ranges: {:?}", row.allowed_cidr);

            Ok(OAuth2Client {
                client_uuid: row.client_uuid,
                name: row.name,
                secret_hash: row.secret_hash,
                secret_salt: row.secret_salt,
                redirect_uris: row.redirect_uris,
                scope_ids: row.scope_ids,
                allowed_cidr: row.allowed_cidr,
                active: row.active,
            })
        }
        Err(e) => {
            approck::info!("Error loading OAuth2 client: {}", e);
            Err(granite::process_error!("Client not found").add_context(e))
        }
    }
}

impl OAuth2Client {
    pub fn validate_secret(&self, secret: &str) -> granite::Result<bool> {
        approck::debug!("Validating client secret for client: {}", self.client_uuid);

        // hash the incoming secret and salt
        let secret_sha256_incoming =
            granite::sha256_str(format!("{}{}", secret, self.secret_salt).as_str());

        // Check the incoming secret against the stored secret
        if secret_sha256_incoming != self.secret_hash {
            approck::info!(
                "Client secret validation failed for client: {}",
                self.client_uuid
            );
            return Err(granite::Error::authentication(
                "Invalid client secret".to_string(),
            ));
        }

        approck::debug!("Client secret validation successful");
        Ok(true)
    }

    pub fn validate_redirect_uri(&self, redirect_uri: &String) -> granite::Result<bool> {
        approck::debug!(
            "Validating redirect URI: {} for client: {}",
            redirect_uri,
            self.client_uuid
        );

        if !self.redirect_uris.contains(redirect_uri) {
            approck::info!(
                "Redirect URI validation failed: {} not in allowed URIs: {:?}",
                redirect_uri,
                self.redirect_uris
            );
            return Err(granite::Error::authentication(
                "Invalid redirect URI".to_string(),
            ));
        }

        approck::debug!("Redirect URI validation successful");
        Ok(true)
    }

    pub fn validate_scope_set(&self, scope_set: &Vec<String>) -> granite::Result<()> {
        approck::debug!(
            "Validating scopes: {:?} for client: {}",
            scope_set,
            self.client_uuid
        );

        // check scopes against client record
        approck::debug!("Allowed scopes: {:?}", self.scope_ids);

        // create two sets - a collection of those that check out against client and those that don't
        let mut unpermitted_scopes: Vec<String> = Vec::new();

        for scope in scope_set {
            if !self.scope_ids.contains(scope) {
                unpermitted_scopes.push(scope.clone());
            }
        }

        if !unpermitted_scopes.is_empty() {
            approck::info!(
                "Scope validation failed: scopes {:?} not allowed",
                unpermitted_scopes
            );
            return Err(granite::Error::authentication("Invalid scope".to_string()));
        }

        approck::debug!("Scope validation successful");

        Ok(())
    }

    pub fn validate_ip_address(&self, ip_address: &str) -> granite::Result<bool> {
        approck::debug!(
            "Validating IP address: {} for client: {}",
            ip_address,
            self.client_uuid
        );

        // TODO: add cidr range validation, not just straight ip address containment
        for cidr in &self.allowed_cidr {
            if cidr.contains(ip_address) {
                approck::debug!(
                    "IP address validation successful: {} matches CIDR: {}",
                    ip_address,
                    cidr
                );
                return Ok(true);
            }
        }

        // Currently always returns true even if no match - this should be fixed in the future
        approck::debug!("IP address validation bypassed (not implemented)");
        Ok(true)
    }
}
