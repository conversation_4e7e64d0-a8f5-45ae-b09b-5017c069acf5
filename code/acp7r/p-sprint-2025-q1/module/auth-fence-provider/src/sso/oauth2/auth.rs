use crate::sso::decrypt_data;
use sha2::{Digest, Sha256};

#[derive(serde::Serialize, serde::Deserialize)]
pub struct OAuth2AuthCode {
    auth_code: String,
    pub client_uuid: granite::Uuid,
    pub identity_uuid: granite::Uuid,
    pub scopes: Vec<String>,
    pub code_challenge: String,
    pub code_challenge_method: String,
    pub redirect_uri: String,
    pub create_ts: granite::DateTime<granite::Utc>,
    pub expires_in: i64,
    pub state: String,
    pub ip: String,
}

pub fn validate_response_type(response_type: &str) -> bool {
    approck::debug!("Validating response type: {}", response_type);

    response_type == "code"
}

pub fn redis_key(code: &str) -> String {
    // get the hash of the auth code
    let code_hash = granite::sha256_str(code);

    format!("oauth2:authcode:{}", code_hash)
}

pub async fn load_auth_code(
    redis: &mut approck_redis::RedisCX<'_>,
    auth_code: &str,
) -> granite::Result<OAuth2AuthCode> {
    approck::debug!("Loading authorization code from Redis");

    // get the hash of the auth code
    // look up the auth code object from redis
    let redis_key = redis_key(auth_code);
    approck::debug!("Redis key: {}", redis_key);

    match redis.get_json::<OAuth2AuthCode>(&redis_key).await {
        Ok(val) => {
            approck::debug!(
                "Authorization code loaded successfully for client: {}",
                val.client_uuid
            );

            // make sure if for some reason redis didn't expire the token, that we do it now
            if val.create_ts + chrono::Duration::seconds(val.expires_in) < granite::Utc::now() {
                approck::debug!("Authorization code expired, deleting from redis");
                redis.del(&redis_key).await?;

                return Err(granite::authorization_error!("Authorization code expired"));
            }

            Ok(val)
        }
        Err(e) => {
            approck::info!("Failed to load authorization code: {}", e);
            Err(
                granite::process_error!("Error getting authorization code from redis")
                    .add_context(e),
            )
        }
    }
}

impl OAuth2AuthCode {
    pub fn expire_ts(&self) -> granite::DateTime<granite::Utc> {
        self.create_ts + chrono::Duration::seconds(self.expires_in)
    }

    pub fn code(&self, key: &[u8]) -> granite::Result<String> {
        // code is stored encrypted
        let auth_code = &self.auth_code;

        let decrypted_code = match decrypt_data(key, auth_code) {
            Ok(encrypted_bytes) => {
                approck::debug!("Decrypted auth code successfully");
                encrypted_bytes
            }
            Err(e) => {
                approck::info!("Decrypting auth code failed: {}", e);
                return Err(
                    granite::process_error!("Error decrypting authorization code").add_context(e),
                );
            }
        };

        Ok(decrypted_code)
    }

    //Auth Code Storage:
    //- Key Pattern: `oauth2:authcode:{client_uuid}:{identity_uuid}`
    //- TTL: Configured by auth_code_ttl settings
    //- Value (JSON): OAuth2AuthCode
    pub async fn store_auth_code(
        &self,
        aes_key: &[u8],
        redis: &mut approck_redis::RedisCX<'_>,
    ) -> granite::Result<()> {
        approck::debug!(
            "Storing authorization code in Redis for client: {}",
            self.client_uuid
        );

        let auth_code = match self.code(aes_key) {
            Ok(code) => code,
            Err(e) => {
                approck::info!("Failed to grab auth code: {}", e);
                return Err(e);
            }
        };

        let redis_key = redis_key(&auth_code);
        approck::debug!("Redis key: {}", redis_key);

        match redis.set_json(&redis_key, self).await {
            Ok(_) => {
                approck::debug!("Authorization code stored successfully");

                redis.set_expire(&redis_key, self.expires_in).await?;

                Ok(())
            }
            Err(e) => {
                approck::info!("Failed to store authorization code: {}", e);
                Err(
                    granite::process_error!("Error storing authorization code in redis")
                        .add_context(e),
                )
            }
        }
    }

    //

    // TODO: add other code_challenge methods
    pub fn verify_code_challenge(&self, code_verifier: String) -> bool {
        approck::debug!("Verifying code challenge with verifier");
        // hash the code_verifier with sha256
        let mut hasher = Sha256::new();
        hasher.update(code_verifier.as_bytes());
        let hash = hasher.finalize();

        // base64url encode the hash without padding
        let computed_challenge = base64_light::base64url_encode_bytes(&hash);

        // strip the padding
        let computed_challenge = computed_challenge.trim_end_matches('=').to_string();
        // compare the hash to the code_challenge
        let result = computed_challenge == self.code_challenge;
        if result {
            approck::debug!("Code challenge verification successful");
        } else {
            approck::debug!("Code challenge verification failed");
        }
        result
    }

    // TODO: Expand code_challenge_method validation
    pub fn validate_code_challenge_method(&self) -> bool {
        approck::debug!(
            "Validating code challenge method: {}",
            self.code_challenge_method
        );
        self.code_challenge_method == "S256"
    }
}

pub mod create_code {
    use super::OAuth2AuthCode;
    use crate::sso::{encrypt_data, generate_random_code};

    pub struct Input {
        pub client_uuid: granite::Uuid,
        pub identity_uuid: granite::Uuid,
        pub scopes: Vec<String>,
        pub code_challenge: String,
        pub code_challenge_method: String,
        pub redirect_uri: String,
        pub state: String,
        pub ip: String,
    }

    pub fn call(
        aes_key: &[u8],
        auth_code_ttl: i64,
        input: Input,
    ) -> granite::Result<OAuth2AuthCode> {
        approck::debug!(
            "Creating new authorization code for client: {}",
            input.client_uuid
        );
        let create_ts = granite::Utc::now();

        // TODO: Should TTL be per-client?
        let expires_in = auth_code_ttl;
        approck::debug!("Authorization code TTL: {} seconds", expires_in);

        let auth_code = generate_random_code();

        let encrypted_auth_code = match encrypt_data(aes_key, &auth_code) {
            Ok(encrypted_bytes) => {
                approck::debug!("Encrypted auth code successfully");
                encrypted_bytes
            }
            Err(e) => {
                approck::info!("Encrypting auth code failed: {}", e);
                return Err(
                    granite::process_error!("Error encrypting authorization code").add_context(e),
                );
            }
        };

        let code = OAuth2AuthCode {
            auth_code: encrypted_auth_code,
            client_uuid: input.client_uuid,
            identity_uuid: input.identity_uuid,
            scopes: input.scopes.clone(),
            code_challenge: input.code_challenge,
            code_challenge_method: input.code_challenge_method,
            redirect_uri: input.redirect_uri.clone(),
            create_ts,
            expires_in,
            state: input.state,
            ip: input.ip,
        };

        approck::debug!("Authorization code created with scopes: {:?}", input.scopes);

        Ok(code)
    }
}
