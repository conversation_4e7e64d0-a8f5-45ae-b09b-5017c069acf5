#[approck::http(GET /sso/oauth2/auth?
    client_id=String
    &state=String
    &response_type=String
    &scope=String
    &code_challenge=String
    &code_challenge_method=String
    &redirect_uri=String;
    AUTH None; return Redirect;
)]
pub mod authorize {
    use crate::sso::oauth2::auth::{create_code, validate_response_type};
    use crate::sso::oauth2::client;
    use crate::sso::oauth2::consent;

    fn get_redis_key_returnto(key: &str) -> String {
        format!("oauth2:auth:original_uri:{}", key)
    }

    // TODO: Implement rate limiting
    pub async fn request(
        qs: QueryString,
        app: App,
        redis: Redis,
        req: Request,
        db: Postgres,
        identity: Identity,
    ) -> Result<Response> {
        // TODO: Lock this down tighter
        let ip_addr = req.remote_address().ip();
        let ip_str = ip_addr.to_string();

        // handle the query paramaters
        let client_uuid = match granite::Uuid::parse_str(&qs.client_id) {
            Ok(uuid) => uuid,
            Err(e) => {
                approck::info!("OAuth2 authorization failed: Invalid client_id: {}", e);
                return Err(e.into());
            }
        };

        let state = qs.state;
        let response_type = qs.response_type;
        let scope = qs.scope;
        let code_challenge = qs.code_challenge;
        let code_challenge_method = qs.code_challenge_method;
        let redirect_uri = qs.redirect_uri;

        approck::debug!("Client UUID: {}", client_uuid);
        approck::debug!("Requested scopes: {}", scope);
        approck::debug!("Code challenge method: {}", code_challenge_method);
        approck::debug!("Redirect URI: {}", redirect_uri);

        approck::debug!("OAuth2 authorization: Check if user is logged in");
        let identity_uuid = match identity.identity_uuid() {
            Some(identity_uuid) => identity_uuid,
            None => {
                // TODO: Log this event
                // TODO: lock this logic down tighter
                // TODO: Stash original uri and check it for integrity upon return

                // Log the authentication attempt
                approck::debug!("OAuth2 authorization: User not logged in, stashing original url");

                // Store the original request uri in Redis for later retrieval
                // Session token will change after user logs in so we need a random key to index it instead
                let redis_key_returnto_key = granite::random_hex(128);
                let redis_key_returnto = get_redis_key_returnto(&redis_key_returnto_key);

                // Construct the login URL with a return_to parameter
                let original_uri = granite::util::make_uri(
                    "/sso/oauth2/auth",
                    &[
                        ("client_id", client_uuid.to_string().as_str()),
                        ("state", state.as_str()),
                        ("response_type", response_type.as_str()),
                        ("scope", scope.as_str()),
                        ("code_challenge", code_challenge.as_str()),
                        ("code_challenge_method", code_challenge_method.as_str()),
                        ("redirect_uri", redirect_uri.as_str()),
                    ],
                );

                redis.set_val(&redis_key_returnto, original_uri).await?;
                redis.set_expire(&redis_key_returnto, 60 * 15).await?; // Expire in 15 minutes

                // where to go after login is complete
                let next_uri = granite::util::make_uri(
                    "/sso/oauth2/redirect",
                    &[("return_to", redis_key_returnto_key.as_str())],
                );

                // where to go next
                let login_uri =
                    granite::util::make_uri("/auth/", &[("next_uri", next_uri.as_str())]);

                approck::info!("OAuth2 authorization: User not logged in, redirecting to login");
                approck::debug!(
                    "OAuth2 authorization: Redirecting to login with return_to: {}",
                    login_uri
                );

                // Redirect to login page
                return Ok(Response::Redirect(login_uri.as_str().into()));
            }
        };

        // validate response type (only one for now)
        if !validate_response_type(response_type.as_str()) {
            approck::info!(
                "OAuth2 authorization failed: Invalid response type: {}",
                response_type
            );
            return Err(granite::Error::new(granite::ErrorType::Validation)
                .set_external_message("Invalid response type".to_string()));
        }

        let client = match client::load_client(&db, &client_uuid).await {
            Ok(client) => {
                approck::debug!("Client loaded successfully: {}", client.name);
                client
            }
            Err(e) => {
                approck::info!(
                    "OAuth2 authorization failed: Error loading client info ( {} ): {}",
                    client_uuid,
                    e
                );
                return Err(e);
            }
        };

        // validate ip
        match client.validate_ip_address(&ip_str) {
            Ok(_) => approck::debug!("IP address validation successful"),
            Err(e) => {
                approck::info!(
                    "OAuth2 authorization failed: IP address validation failed: {}",
                    e
                );
                return Err(e);
            }
        };

        // split scopes by space
        let scopes = scope
            .split(" ")
            .map(|s| s.to_string())
            .collect::<Vec<String>>();

        // validate scopes
        match client.validate_scope_set(&scopes) {
            Ok(_) => approck::debug!("Scope validation successful"),
            Err(e) => {
                approck::info!(
                    "OAuth2 authorization failed: Scope validation failed: {}",
                    e
                );
                return Err(e);
            }
        };

        // validate redirect uri
        match client.validate_redirect_uri(&redirect_uri) {
            Ok(_) => approck::debug!("Redirect URI validation successful"),
            Err(e) => {
                approck::info!(
                    "OAuth2 authorization failed: Redirect URI validation failed: {}",
                    e
                );
                return Err(e);
            }
        };

        // TODO: record the origin_uri -  because of redirects we want to be sure the uri is unaltered
        let consent_uri = granite::util::make_uri(
            "/sso/consent/",
            &[
                ("client_id", client_uuid.to_string().as_str()),
                ("state", state.as_str()),
                ("response_type", response_type.as_str()),
                ("scope", scope.as_str()),
                ("code_challenge", code_challenge.as_str()),
                ("code_challenge_method", code_challenge_method.as_str()),
                ("redirect_uri", redirect_uri.as_str()),
            ],
        );

        // check for consent
        match consent::load_consent(app, &client_uuid, &identity_uuid).await {
            Ok(Some(consent)) => {
                approck::debug!(
                    "Consent found for client: {} and identity: {}, validating requested scopes",
                    client_uuid,
                    identity_uuid
                );

                match consent.validate_consent(&scopes) {
                    Ok(true) => approck::debug!("Consent scope validation successful"),
                    Ok(false) => {
                        approck::info!(
                            "OAuth2 authorization: Consent scopes ({:?}) invalid, redirecting to consent page",
                            scopes
                        );
                        return Ok(Response::Redirect(consent_uri.as_str().into()));
                    }
                    Err(e) => {
                        approck::info!(
                            "OAuth2 authorization failed: Error validating consent scopes: {}",
                            e
                        );
                        return Err(e);
                    }
                }
            }
            Ok(None) => {
                approck::info!(
                    "OAuth2 authorization: No consent found, redirecting to consent page"
                );
                // no consent, redirect to consent page
                return Ok(Response::Redirect(consent_uri.as_str().into()));
            }
            Err(e) => {
                approck::info!("OAuth2 authorization failed: Error loading consent: {}", e);
                return Err(e);
            }
        }

        // create auth code
        approck::debug!("Creating authorization code");
        let auth_provider = app.auth_fence_provider();
        let aes_key = auth_provider.aes_key();
        let auth_code_ttl = auth_provider.auth_code_ttl();

        let auth_code = match create_code::call(
            &aes_key,
            auth_code_ttl,
            create_code::Input {
                client_uuid,
                identity_uuid,
                scopes,
                code_challenge,
                code_challenge_method,
                redirect_uri: redirect_uri.clone(),
                state: state.clone(),
                ip: ip_str.clone(),
            },
        ) {
            Ok(code) => {
                approck::debug!("Authorization code created");
                code
            }
            Err(e) => {
                approck::info!("Authorization code creation failed: {}", e);
                return Err(
                    granite::process_error!("Authorization code creation failed").add_context(e),
                );
            }
        };

        // validate response type
        if !auth_code.validate_code_challenge_method() {
            approck::info!(
                "OAuth2 authorization failed: Invalid code challenge method: {}",
                auth_code.code_challenge_method
            );
            return Err(granite::Error::new(granite::ErrorType::Validation)
                .set_external_message("Invalid code challenge method".to_string()));
        }

        // store auth code
        match auth_code.store_auth_code(&aes_key, &mut redis).await {
            Ok(_) => approck::debug!("Authorization code stored successfully"),
            Err(e) => {
                approck::info!(
                    "OAuth2 authorization failed: Error storing authorization code: {}",
                    e
                );
                return Err(e);
            }
        };

        let auth_code_str = match auth_code.code(&aes_key) {
            Ok(code) => code,
            Err(e) => {
                approck::info!("OAuth2 authorization failed to grab auth code: {}", e);
                return Err(e);
            }
        };

        let success_uri = granite::util::make_uri(
            redirect_uri.as_str(),
            &[("code", &auth_code_str), ("state", state.as_str())],
        );

        approck::info!(
            "OAuth2 authorization successful, redirecting to: {}",
            success_uri
        );
        Ok(Response::Redirect(success_uri.as_str().into()))
    }
}
