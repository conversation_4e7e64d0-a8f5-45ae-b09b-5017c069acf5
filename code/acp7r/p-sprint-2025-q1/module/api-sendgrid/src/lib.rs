pub mod api;
pub mod web;

use std::collections::HashMap;

pub trait App: approck::App + approck_postgres::App {}

pub trait Identity: approck::Identity {}

pub trait Document: bux::document::Base {}

#[derive(serde::Deserialize)]
pub struct ModuleConfig {
    pub api_key: String,
    pub sender: HashMap<String, String>,
}

pub struct ModuleStruct {
    pub api_key: String,
    pub sender_map: HashMap<String, String>,
}

impl ModuleStruct {
    pub async fn send_email(
        &self,
        sender_key: &str,
        to: &str,
        subject: &str,
        message: &str,
    ) -> granite::Result<()> {
        let from_email = self
            .sender_map
            .get(sender_key)
            .ok_or_else(|| granite::Error::validation(format!("Unknown sender: {}", sender_key)))?;

        self.send_email_internal(from_email, to, subject, message)
            .await
    }

    async fn send_email_internal(
        &self,
        from_email: &str,
        to_email: &str,
        subject: &str,
        message: &str,
    ) -> granite::Result<()> {
        let client = reqwest::Client::new();

        let payload = serde_json::json!({
            "personalizations": [{
                "to": [{"email": to_email}]
            }],
            "from": {"email": from_email},
            "subject": subject,
            "content": [{
                "type": "text/plain",
                "value": message
            }]
        });

        let response = client
            .post("https://api.sendgrid.com/v3/mail/send")
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await
            .map_err(|e| granite::Error::api_request_error(format!("Failed to send email: {}", e)))?;

        if response.status().is_success() {
            Ok(())
        } else {
            let status = response.status();
            let body = response
                .text()
                .await
                .unwrap_or_else(|_| "Unable to read response body".to_string());
            Err(granite::Error::api_request_error(format!(
                "SendGrid API error {}: {}",
                status, body
            )))
        }
    }
}

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;
    async fn new(config: Self::Config) -> granite::Result<Self> {
        Ok(Self {
            api_key: config.api_key,
            sender_map: config.sender,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;
    use approck::Module;

    #[test]
    fn test_module_creation() {
        let mut sender_map = HashMap::new();
        sender_map.insert("support".to_string(), "<EMAIL>".to_string());
        sender_map.insert("noreply".to_string(), "<EMAIL>".to_string());

        let config = ModuleConfig {
            api_key: "test_api_key".to_string(),
            sender: sender_map,
        };

        let rt = tokio::runtime::Runtime::new().unwrap();
        let module = rt.block_on(async {
            ModuleStruct::new(config).await
        }).unwrap();

        assert_eq!(module.api_key, "test_api_key");
        assert_eq!(module.sender_map.get("support"), Some(&"<EMAIL>".to_string()));
        assert_eq!(module.sender_map.get("noreply"), Some(&"<EMAIL>".to_string()));
    }

    #[test]
    fn test_unknown_sender() {
        let mut sender_map = HashMap::new();
        sender_map.insert("support".to_string(), "<EMAIL>".to_string());

        let config = ModuleConfig {
            api_key: "test_api_key".to_string(),
            sender: sender_map,
        };

        let rt = tokio::runtime::Runtime::new().unwrap();
        let module = rt.block_on(async {
            ModuleStruct::new(config).await
        }).unwrap();

        let result = rt.block_on(async {
            module.send_email("unknown_sender", "<EMAIL>", "Test", "Test message").await
        });

        assert!(result.is_err());
        let error_msg = format!("{}", result.unwrap_err());
        assert!(error_msg.contains("Unknown sender: unknown_sender"));
    }
}
