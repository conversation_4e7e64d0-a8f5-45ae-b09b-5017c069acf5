pub mod api;
pub mod web;

pub trait App: approck::App + approck_postgres::App {}

pub trait Identity: approck::Identity {}

pub trait Document: bux::document::Base {}

#[derive(serde::Deserialize)]
pub struct ModuleConfig {
    pub secret_key: String,
    pub public_key: String,
}
pub struct ModuleStruct {
    pub secret_key: String,
    pub public_key: String,
}

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;
    async fn new(config: Self::Config) -> granite::Result<Self> {
        Ok(Self {
            secret_key: config.secret_key,
            public_key: config.public_key,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}
