mod date;
mod error;
mod gtype;
mod rand;
mod util;
mod webpath;

pub use crate::error::{
    Error, ErrorType, HTTPResponse, HTTPResponseCallback, Result, ResultExt, StdError,
};

pub use crate::rand::{random_code_3, random_code_4, random_hex, ts_random_hex};

pub use crate::date::{age_in_years, current_date, current_year};

pub use base64_light::{base64_decode_str, base64_encode_bytes};

pub use crate::util::{rpartition, uuid_v7};

pub use chrono::DateTime;
pub use chrono::Datelike;
pub use chrono::Duration;
pub use chrono::FixedOffset;
pub use chrono::NaiveDate;
pub use chrono::NaiveDateTime;
pub use chrono::TimeZone;
pub use chrono::Utc;

// IDEA: Perhaps these need upgraded our own types?
pub type Date = NaiveDate;
pub type DateUtc = NaiveDate;
pub type DateTimeTz = DateTime<FixedOffset>;
pub type DateTimeUtc = DateTime<Utc>;

pub use rust_decimal::{Decimal, RoundingStrategy};
pub use rust_decimal_macros::dec;

pub use num_bigint::BigInt;

pub use crate::gtype::{GTypeDecode, GTypeEncode, GTypeValidate, NestedError};
pub use std::net::IpAddr;

pub use serde_json::{Value as JsonValue, json};
pub type JsonArray = Vec<JsonValue>;
pub type JsonObject = serde_json::Map<String, JsonValue>;

pub use uuid::{Timestamp as UuidTimestamp, Uuid};

// TODO: Populate per App
pub type UuidMap = std::collections::HashMap<Uuid, String>;

pub use crate::webpath::{WebPath, WebPathCapture, WebPathFragment, WebPathSegment};
