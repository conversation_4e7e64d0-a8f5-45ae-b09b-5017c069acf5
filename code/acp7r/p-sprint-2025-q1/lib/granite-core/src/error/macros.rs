#[macro_export]
macro_rules! invalid_operation {
    ($($arg:tt)*) => {
        $crate::Error::new($crate::ErrorType::InvalidOperation).add_context(format!($($arg)*))
    };
}

#[macro_export]
macro_rules! return_invalid_operation {
    ($($arg:tt)*) => {
        return Err($crate::invalid_operation!($($arg)*));
    };
}

#[macro_export]
macro_rules! authorization_error {
    ($($arg:tt)*) => {
        {
            let msg = format!($($arg)*);
            $crate::Error::new($crate::ErrorType::Authorization).add_context(msg.clone()).set_external_message(msg)
        }
    };
}

#[macro_export]
macro_rules! return_authorization_error {
    ($($arg:tt)*) => {
        return Err($crate::authorization_error!($($arg)*));
    };
}

// process_error
#[macro_export]
macro_rules! process_error {
    ($($arg:tt)*) => {
        $crate::Error::new($crate::ErrorType::ProcessError).add_context(format!($($arg)*))
    };
}

#[macro_export]
macro_rules! return_process_error {
    ($($arg:tt)*) => {
        return Err($crate::process_error!($($arg)*));
    };
}

#[macro_export]
macro_rules! from_error_stack {
    ($e:expr) => {
        approck::Error::new(approck::ErrorType::Unexpected).add_context(format!("{:#?}", $e))
    };
    () => {
        approck::Error::new(approck::ErrorType::Unexpected)
    };
}
