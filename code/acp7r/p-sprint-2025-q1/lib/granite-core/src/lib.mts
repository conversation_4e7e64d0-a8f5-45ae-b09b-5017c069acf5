export * from "./gtype.mts";
export * from "./util.mts";
import { type Result } from "./gtype.mts";

export { Decimal } from "./decimal.mts";

export function panic(message: string, data: unknown): never {
    throw new Error(`${message}: ${data}`);
}

export function expect<T, E>(result: Result<T, E>, message: string): T {
    if ("Err" in result) {
        panic(message, result.Err);
    }
    return result.Ok;
}
