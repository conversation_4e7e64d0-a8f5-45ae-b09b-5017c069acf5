#![allow(unused_variables)]

use super::{
    GADT, GInner, GInnerEnum, GInnerStruct, GMacroInput, GScalar, GStyle, GType, GVariant,
};
use crate::{gtype::IdentExt, ident_str};
use proc_macro2::TokenStream;
use quote::{ToTokens, quote};
use syn::Ident;

/// Returns a TokenStream containing the generated GADT struct or enum along with requested GTypeDecode or GTypeEncode implementations
pub fn g_macro_input_to_token_stream(g_macro_input: GMacroInput) -> TokenStream {
    let mut item_tokens = quote! {};

    let derive_option = g_macro_input.macro_option;

    for gadt in [
        &g_macro_input.gadt_type,
        &g_macro_input.gadt_partial,
        &g_macro_input.gadt_error,
    ] {
        if gadt.code_gen.rs_type {
            item_tokens.extend(rs_type(gadt));
        }
        if gadt.code_gen.rs_validate {
            item_tokens.extend(rs_validate(gadt));
        }
        if gadt.code_gen.rs_encode {
            item_tokens.extend(rs_type_encode(gadt));
        }
        if gadt.code_gen.rs_decode {
            item_tokens.extend(rs_type_decode(gadt));
        }
    }

    item_tokens
}

fn rs_type(gadt: &GADT) -> TokenStream {
    let type_exp = gadt.ident();
    let vis = &gadt.vis;
    let attrs = &gadt.attrs;

    let derive_line = {
        let mut derive_tokens = vec![];

        if gadt.code_gen.rs_debug {
            derive_tokens.push(quote! { ::std::fmt::Debug });
        }

        if gadt.code_gen.rs_clone {
            derive_tokens.push(quote! { ::std::clone::Clone });
        }

        if gadt.code_gen.rs_partial_eq {
            derive_tokens.push(quote! { ::std::cmp::PartialEq });
        }

        if derive_tokens.is_empty() {
            quote! {}
        } else {
            quote! {
                #[derive(#(#derive_tokens),*)]
            }
        }
    };

    match &gadt.g_inner {
        GInner::Struct(GInnerStruct::Unit) => {
            quote! {
                #derive_line
                #(#attrs)*
                #vis struct #type_exp;
            }
        }
        GInner::Struct(GInnerStruct::Tuple(doubles)) => {
            let field_tokens = doubles.iter().map(|(vis, gtype)| {
                let gtype_token = gtype.type_exp();
                quote! {
                    #vis #gtype_token
                }
            });
            quote! {
                #derive_line
                #(#attrs)*
                #vis struct #type_exp(#(#field_tokens),*);
            }
        }
        GInner::Struct(GInnerStruct::Named(triples)) => {
            let field_tokens = triples.iter().map(|(vis, ident, gtype)| {
                let ident_token = ident.to_token_stream();
                let gtype_token = gtype.type_exp();
                quote! {
                    #vis #ident_token: #gtype_token
                }
            });
            quote! {
                #derive_line
                #(#attrs)*
                #vis struct #type_exp {
                    #(#field_tokens),*
                }
            }
        }
        GInner::Enum(GInnerEnum(ident_variant_list)) => {
            let mut variant_tokens = Vec::with_capacity(ident_variant_list.len());

            for (ident, gvariant) in ident_variant_list {
                match gvariant {
                    GVariant::Unit => {
                        variant_tokens.push(quote! {
                            #ident
                        });
                    }
                    GVariant::Tuple(fields) => {
                        let fields_token = fields.iter().map(|(gtype,)| {
                            let gtype_token = gtype.type_exp();
                            quote! {
                                #gtype_token
                            }
                        });
                        variant_tokens.push(quote! {
                            #ident(#(#fields_token),*)
                        });
                    }
                    GVariant::Struct(singles) => {
                        let singles_token = singles.iter().map(|(ident, gtype)| {
                            let ident_token = ident.to_token_stream();
                            let gtype_token = gtype.type_exp();
                            quote! {
                                #ident_token: #gtype_token
                            }
                        });
                        variant_tokens.push(quote! {
                            #ident { #(#singles_token),* }
                        });
                    }
                }
            }

            quote! {
                #derive_line
                #(#attrs)*
                #vis enum #type_exp {
                    #(#variant_tokens),*
                }
            }
        }
        GInner::Type(inner_type) => {
            let type_tokens = inner_type.path();
            quote! {
                #derive_line
                #(#attrs)*
                #vis type #type_exp = #type_tokens;
            }
        }
    }
}

fn rs_validate(gadt: &GADT) -> TokenStream {
    match &gadt.g_style {
        GStyle::Type {
            type_ident,
            error_ident,
        } => rs_validate_type(gadt, type_ident, error_ident),
        GStyle::Partial {
            type_ident,
            partial_ident,
            error_ident,
        } => rs_validate_partial(gadt, type_ident, partial_ident, error_ident),
        GStyle::Error { .. } => {
            unreachable!("derive_validate for error type is not supported");
        }
    }
}

fn rs_validate_type(gadt: &GADT, type_exp: &Ident, error_exp: &Ident) -> TokenStream {
    match &gadt.g_inner {
        GInner::Struct(GInnerStruct::Unit) => {
            quote! {
                impl ::granite::GTypeValidate for #type_exp {
                    type Type = Self;
                    type Error = ::granite::NestedError<#error_exp>;
                    fn gtype_validate(self) -> ::std::result::Result<Self::Type, Self::Error>
                    where Self: Sized
                    {
                        ::std::result::Result::Ok(self)
                    }
                }
            }
        }
        GInner::Struct(GInnerStruct::Tuple(gtypes)) => {
            let mut field_locals = Vec::with_capacity(gtypes.len());
            let mut field_indexes = Vec::with_capacity(gtypes.len());

            for (index, gtype) in gtypes.iter().enumerate() {
                let field_local = quote::format_ident!("v{}", index);
                let field_index = proc_macro2::Literal::usize_unsuffixed(index);
                field_locals.push(field_local);
                field_indexes.push(field_index);
            }

            quote! {
                impl ::granite::GTypeValidate for #type_exp {
                    type Type = Self;
                    type Error = ::granite::NestedError<#error_exp>;
                    fn gtype_validate(self) -> ::std::result::Result<Self::Type, Self::Error>
                    where Self: Sized {
                        #(let #field_locals = ::granite::GTypeValidate::gtype_validate(self.#field_indexes);)*
                        match (#(#field_locals,)*) {
                            ( #(::std::result::Result::Ok(#field_locals),)* ) => {
                                ::std::result::Result::Ok(#type_exp(#(#field_locals),*))
                            }
                            ( #(#field_locals,)* ) => {
                                ::std::result::Result::Err(::granite::NestedError{
                                    outer: "validation error".to_string(),
                                    inner: ::std::option::Option::Some(#error_exp(#(#field_locals.err()),*)),
                                })
                            }
                        }
                    }
                }
            }
        }
        GInner::Struct(GInnerStruct::Named(vis_ident_gtype_list)) => {
            let mut field_locals = Vec::with_capacity(vis_ident_gtype_list.len());
            let mut field_idents = Vec::with_capacity(vis_ident_gtype_list.len());

            for (_vis, ident, gtype) in vis_ident_gtype_list {
                field_locals.push(ident);
                field_idents.push(ident);
            }

            quote! {
                impl ::granite::GTypeValidate for #type_exp {
                    type Type = Self;
                    type Error = ::granite::NestedError<#error_exp>;
                    fn gtype_validate(self) -> ::std::result::Result<Self::Type, Self::Error>
                    where Self: Sized {
                        #(let #field_locals = ::granite::GTypeValidate::gtype_validate(self.#field_idents);)*
                        match (#(#field_locals,)*) {
                            ( #(::std::result::Result::Ok(#field_locals),)* ) => {
                                ::std::result::Result::Ok(#type_exp { #(#field_idents: #field_locals),* })
                            }
                            ( #(#field_locals,)* ) => {
                                ::std::result::Result::Err(::granite::NestedError{
                                    outer: "validation error".to_string(),
                                    inner: ::std::option::Option::Some(#error_exp { #(#field_idents: #field_locals.err()),* }),
                                })
                            }
                        }
                    }
                }
            }
        }
        GInner::Enum(GInnerEnum(ident_variant_list)) => {
            let mut variant_tokens = Vec::with_capacity(ident_variant_list.len());

            for (variant_ident, gvariant) in ident_variant_list {
                match gvariant {
                    GVariant::Unit => {
                        variant_tokens.push(quote! {
                            #type_exp::#variant_ident => {
                                Ok(self)
                            }
                        });
                    }
                    GVariant::Tuple(fields) => {
                        let mut field_locals = Vec::with_capacity(fields.len());
                        for (index, gtype) in fields.iter().enumerate() {
                            let field_local = quote::format_ident!("v{}", index);
                            field_locals.push(field_local);
                        }

                        variant_tokens.push(quote! {
                            #type_exp::#variant_ident(#(#field_locals),*) => {
                                #(let #field_locals = ::granite::GTypeValidate::gtype_validate(#field_locals);)*
                                match (#(#field_locals,)*) {
                                    ( #(::std::result::Result::Ok(#field_locals),)* ) => {
                                        ::std::result::Result::Ok(#type_exp::#variant_ident(#(#field_locals),*))
                                    }
                                    ( #(#field_locals,)* ) => {
                                        ::std::result::Result::Err(::granite::NestedError{
                                            outer: "validation error".to_string(),
                                            inner: ::std::option::Option::Some(#error_exp::#variant_ident(#(#field_locals.err()),*)),
                                        })
                                    }
                                }
                            }
                        });
                    }
                    GVariant::Struct(fields) => {
                        let mut field_locals = Vec::with_capacity(fields.len());
                        let mut field_idents = Vec::with_capacity(fields.len());
                        for (field_ident, gtype) in fields {
                            field_locals.push(field_ident);
                            field_idents.push(field_ident);
                        }

                        variant_tokens.push(quote! {
                            #type_exp::#variant_ident {#(#field_locals),*} => {
                                #(let #field_locals = ::granite::GTypeValidate::gtype_validate(#field_locals);)*
                                match (#(#field_locals,)*) {
                                    ( #(::std::result::Result::Ok(#field_locals),)* ) => {
                                        ::std::result::Result::Ok(#type_exp::#variant_ident { #(#field_idents: #field_locals),* })
                                    }
                                    ( #(#field_locals,)* ) => {
                                        ::std::result::Result::Err(::granite::NestedError{
                                            outer: "validation error".to_string(),
                                            inner: ::std::option::Option::Some(#error_exp::#variant_ident { #(#field_idents: #field_locals.err()),* }),
                                        })
                                    }
                                }
                            }
                        });
                    }
                }
            }

            quote! {
                impl ::granite::GTypeValidate for #type_exp {
                    type Type = Self;
                    type Error = ::granite::NestedError<#error_exp>;
                    fn gtype_validate(self) -> ::std::result::Result<Self::Type, Self::Error>
                    where Self: Sized
                    {
                        match self {
                            #(#variant_tokens)*
                        }
                    }
                }
            }
        }
        GInner::Type(_ty) => {
            quote! {}
        }
    }
}

fn rs_validate_partial(
    gadt: &GADT,
    type_exp: &Ident,
    partial_exp: &Ident,
    error_exp: &Ident,
) -> TokenStream {
    quote! {}
}

fn rs_type_encode(gadt: &GADT) -> TokenStream {
    let type_exp = gadt.ident();

    match &gadt.g_inner {
        GInner::Struct(GInnerStruct::Unit) => {
            quote! {
                impl ::granite::GTypeEncode for #type_exp {
                    fn gtype_encode(&self) -> ::granite::JsonValue {
                        ::granite::JsonValue::Bool(true)
                    }
                }
            }
        }
        GInner::Struct(GInnerStruct::Tuple(gtypes)) => {
            let mut field_tokens = Vec::with_capacity(gtypes.len());

            for (index, _gtype) in gtypes.iter().enumerate() {
                let index_unsuffixed = proc_macro2::Literal::usize_unsuffixed(index);

                field_tokens.push(quote! {
                    ::granite::GTypeEncode::gtype_encode(&self.#index_unsuffixed)
                });
            }

            quote! {
                impl ::granite::GTypeEncode for #type_exp {
                    fn gtype_encode(&self) -> ::granite::JsonValue {
                        ::granite::JsonValue::Array(vec![#(#field_tokens),*])
                    }
                }
            }
        }
        GInner::Struct(GInnerStruct::Named(vis_ident_gtype_list)) => {
            let field_count = vis_ident_gtype_list.len();
            let mut field_assign_tokens = Vec::with_capacity(field_count);

            for (_vis, field_ident, _gtype) in vis_ident_gtype_list {
                let map_key_string = field_ident.to_map_key();
                field_assign_tokens.push(quote!{
                    map.insert(#map_key_string.to_string(), ::granite::GTypeEncode::gtype_encode(&self.#field_ident));
                });
            }

            quote! {
                impl ::granite::GTypeEncode for #type_exp {
                    fn gtype_encode(&self) -> ::granite::JsonValue {
                        let mut map = ::granite::JsonObject::with_capacity(#field_count);
                        #(#field_assign_tokens)*
                        ::granite::JsonValue::Object(map)
                    }
                }
            }
        }
        GInner::Enum(GInnerEnum(ident_variant_list)) => {
            let variants = ident_variant_list.iter().map(|(variant_ident, gvariant)| {
                let variant_map_key = variant_ident.to_map_key();
                match gvariant {
                    // unit variants are encoded like this: "VariantName"
                    GVariant::Unit => {
                        quote! {
                            Self::#variant_ident => {
                                ::granite::JsonValue::String(#variant_map_key.to_string())
                            }
                        }
                    }
                    // tuple variants are encoded like this: { "VariantName": [v0, v1, v2] }
                    GVariant::Tuple(gfields) => {
                        let variant_ident_str = variant_ident.to_string();
                        let fields_len = gfields.len();

                        let mut tuple_vars = vec![];
                        let mut array_pushes = vec![];

                        for (i, _field) in gfields.iter().enumerate() {
                            let tuple_var:Ident = syn::Ident::new(&format!("v{i}"), proc_macro2::Span::call_site());
                            tuple_vars.push(quote! {#tuple_var});

                            array_pushes.push(quote! {
                                arr.push(::granite::GTypeEncode::gtype_encode(#tuple_var));
                            });
                        }

                        quote! {
                            Self::#variant_ident(#(#tuple_vars,)*) => {
                                let mut map = ::granite::JsonObject::with_capacity(1);
                                let mut arr = ::std::vec::Vec::with_capacity(#fields_len);
                                #(#array_pushes)*
                                map.insert(#variant_ident_str.to_string(), ::granite::JsonValue::Array(arr));
                                ::granite::JsonValue::Object(map)
                            }
                        }
                    }
                    // struct variants are encoded like this: { "VariantName": { "field0": v0, "field1": v1, ... } }
                    GVariant::Struct(ident_gtype_list) => {
                        let variant_ident_string = variant_ident.to_string();
                        let fields_len = ident_gtype_list.len();
                        let mut struct_vars = vec![];
                        let mut field_assignments = vec![];

                        for (field_ident, _gtype) in ident_gtype_list.iter() {
                            let map_key_string = field_ident.to_map_key();
                            struct_vars.push(quote! {#field_ident});

                            field_assignments.push(quote! {
                                map2.insert(#map_key_string.to_string(), ::granite::GTypeEncode::gtype_encode(#field_ident));
                            });
                        }
                        quote! {
                            Self::#variant_ident {#(#struct_vars),*} => {
                                let mut map1 = ::granite::JsonObject::with_capacity(1);
                                let mut map2 = ::granite::JsonObject::with_capacity(#fields_len);
                                #(#field_assignments)*
                                map1.insert(#variant_ident_string.to_string(), ::granite::JsonValue::Object(map2));
                                ::granite::JsonValue::Object(map1)
                            }
                        }
                    }
                }
            });

            quote! {
                impl ::granite::GTypeEncode for #type_exp {
                    fn gtype_encode(&self) -> ::granite::JsonValue {
                        match self {
                            #(#variants,)*
                        }
                    }
                }

            }
        }
        GInner::Type(_) => {
            quote! {}
        }
    }
}

fn rs_type_decode(gadt: &GADT) -> TokenStream {
    let type_exp = gadt.ident();

    match &gadt.g_inner {
        GInner::Struct(GInnerStruct::Unit) => {
            quote! {
                impl ::granite::GTypeDecode for #type_exp {
                    fn gtype_decode(data: ::std::option::Option<::granite::JsonValue>) -> ::std::result::Result<Self, String>
                    where Self: Sized
                    {
                        Ok(Self)
                    }
                }
            }
        }
        GInner::Struct(GInnerStruct::Tuple(gtypes)) => {
            let mut assign_tokens: Vec<TokenStream> = Vec::with_capacity(gtypes.len());
            let mut field_tokens = Vec::with_capacity(gtypes.len());

            for (index, gtype) in gtypes.iter().enumerate() {
                let field_variable = ident_str!(format!("f{index}"));

                assign_tokens.push(quote! {
                    let #field_variable = match ::granite::GTypeDecode::gtype_decode(drain_iter.next()) {
                        Ok(v) => v,
                        // TODO: include formatting of error message
                        Err(_) => return Err(format!("Failed to decode field {}", #index)),
                    };
                });

                field_tokens.push(quote! {
                    #field_variable
                });
            }

            quote! {
                impl ::granite::GTypeDecode for #type_exp {
                    fn gtype_decode(data: ::std::option::Option<::granite::JsonValue>) -> ::std::result::Result<Self, String>
                    where Self: Sized {
                        match data {
                            Some(::granite::JsonValue::Array(mut data)) => {
                                let mut drain_iter = data.drain(..);
                                #(#assign_tokens)*;
                                Ok(Self(#(#field_tokens),*))
                            },
                            Some(v) => Err("wrong json type".to_string()),
                            None => Err("value is missing".to_string()),
                        }
                    }
                }
            }
        }
        GInner::Struct(GInnerStruct::Named(vis_ident_gtype_list)) => {
            let mut assign_tokens = Vec::with_capacity(vis_ident_gtype_list.len());
            let mut field_tokens = Vec::with_capacity(vis_ident_gtype_list.len());

            for (_vis, field_ident, _gtype) in vis_ident_gtype_list {
                let map_key_string = field_ident.to_map_key();

                assign_tokens.push(quote! {
                    let #field_ident = match ::granite::GTypeDecode::gtype_decode(map.remove(#map_key_string)) {
                        Ok(v) => v,
                        Err(e) => return Err(format!("Failed to decode field {}: {}", #map_key_string, e)),
                    };
                });

                field_tokens.push(quote! {
                    #field_ident
                });
            }

            quote! {
                impl ::granite::GTypeDecode for #type_exp {
                    fn gtype_decode(data: ::std::option::Option<::granite::JsonValue>) -> ::std::result::Result<Self, String>
                    where Self: Sized
                    {
                        match data {
                            Some(::granite::JsonValue::Object(mut map)) => {
                                #(#assign_tokens)*;
                                Ok(Self{#(#field_tokens),*})
                            },
                            Some(v) => Err("wrong json type".to_string()),
                            None => Err("value is missing".to_string()),
                        }
                    }
                }
            }
        }
        GInner::Enum(GInnerEnum(ident_variant_list)) => {
            let mut variant_tokens = Vec::with_capacity(ident_variant_list.len());

            for (variant_ident, gvariant) in ident_variant_list {
                let variant_map_key = variant_ident.to_map_key();

                match gvariant {
                    GVariant::Unit => {
                        variant_tokens.push(quote! {
                            (#variant_map_key, ::granite::JsonValue::Bool(true)) => {
                                Ok(Self::#variant_ident)
                            }
                        });
                    }
                    GVariant::Tuple(fields) => {
                        let mut assign_tokens = Vec::with_capacity(fields.len());
                        let mut field_tokens = Vec::with_capacity(fields.len());

                        for (index, _gtype) in fields.iter().enumerate() {
                            let field_variable = ident_str!(format!("f{index}"));

                            assign_tokens.push(quote! {
                                let #field_variable = match ::granite::GTypeDecode::gtype_decode(drain_iter.next()) {
                                    Ok(v) => v,
                                    Err(e) => return Err(format!("Failed to decode field {}: {}", #index, e)),
                                };
                            });

                            field_tokens.push(quote! {
                                #field_variable
                            });
                        }
                        variant_tokens.push(quote! {
                            (#variant_map_key, val) => match val {
                                ::granite::JsonValue::Array(mut val) => {
                                    let mut drain_iter = val.drain(..);
                                    #(#assign_tokens)*;
                                    Ok(Self::#variant_ident(#(#field_tokens),*))
                                },
                                _ => return Err("wrong json type".to_string()),
                            }
                        });
                    }
                    GVariant::Struct(fields) => {
                        let mut assign_tokens = Vec::with_capacity(fields.len());
                        let mut field_tokens = Vec::with_capacity(fields.len());

                        for (field_ident, gtype) in fields {
                            let map_key_string = field_ident.to_map_key();

                            assign_tokens.push(quote! {
                                let #field_ident = match ::granite::GTypeDecode::gtype_decode(map.remove(#map_key_string)) {
                                    Ok(v) => v,
                                    Err(e) => return Err(format!("Failed to decode field {}: {}", #map_key_string, e)),
                                };
                            });
                            field_tokens.push(quote! {
                                #field_ident
                            });
                        }

                        variant_tokens.push(quote! {
                            (#variant_map_key, val) => {
                                match val {
                                    ::granite::JsonValue::Object(mut map) => {
                                        #(#assign_tokens)*;
                                        Ok(Self::#variant_ident{#(#field_tokens),*})
                                    },
                                    _ => return Err("wrong json type".to_string()),
                                }
                            }
                        })
                    }
                }
            }

            quote! {
                impl ::granite::GTypeDecode for #type_exp {
                    fn gtype_decode(data: ::std::option::Option<::granite::JsonValue>) -> ::std::result::Result<Self, String>
                    where Self: Sized
                    {
                        let (variant, variant_data) = match data {
                            Some(::granite::JsonValue::String(variant)) => {
                                (variant, ::granite::JsonValue::Bool(true))
                            },
                            Some(::granite::JsonValue::Object(mut map)) => {
                                // Get the first (and only) key in this object
                                match map.into_iter().next() {
                                    Some((k, v)) => (k, v),
                                    None => return Err("enum object empty".to_string()),
                                }
                            },
                            Some(v) => return Err("wrong json type".to_string()),
                            None => return Err("value is missing".to_string()),
                        };

                        // match on it
                        match (variant.as_str(), variant_data) {
                            #(#variant_tokens,)*
                            (k, _) => return Err(format!("unexpected enum variant: {}", k))
                        }
                    }
                }
            }
        }
        GInner::Type(_ty) => {
            quote! {}
        }
    }
}

trait GTypeExt {
    fn type_exp(&self) -> TokenStream;
}

impl GTypeExt for GType {
    fn type_exp(&self) -> TokenStream {
        match self {
            GType::Undefinable(gtype) => {
                let inner_type = gtype.type_exp();
                quote! {
                    ::std::option::Option<#inner_type>
                }
            }
            GType::NestedError(gtype) => {
                let inner_type = gtype.type_exp();
                quote! {
                    ::granite::NestedError<#inner_type>
                }
            }
            GType::Undefined => {
                quote! {
                    ()
                }
            }
            GType::GType(ident) => ident.to_token_stream(),
            GType::Scalar(gscalar) => gscalar.type_exp(),
            GType::Option {
                zero_to_none,
                some_type: vtype,
            } => {
                let inner_token = vtype.type_exp();
                quote! {
                    ::std::option::Option<#inner_token>
                }
            }
            GType::Result(ok, err) => {
                let ok_token = ok.type_exp();
                let err_token = err.type_exp();
                quote! {
                    ::std::result::Result<#ok_token, #err_token>
                }
            }
            GType::Vec(gtype) => {
                let inner_token = gtype.type_exp();
                quote! {
                    Vec<#inner_token>
                }
            }
            GType::HashSet(gscalar) => {
                let scalar_token = gscalar.type_exp();
                quote! {
                    ::std::collections::HashSet<#scalar_token>
                }
            }
            GType::HashMap {
                key_type,
                value_type,
                max_items,
            } => {
                let key_type = key_type.type_exp();
                let value_type = value_type.type_exp();
                quote! {
                    ::std::collections::HashMap<#key_type, #value_type>
                }
            }
            GType::BTreeMap(key_gscalar, value_gtype) => {
                let key_type = key_gscalar.type_exp();
                let value_type = value_gtype.type_exp();
                quote! {
                    ::std::collections::BTreeMap<#key_type, #value_type>
                }
            }
            GType::IndexMap(key_gscalar, value_gtype) => {
                let key_type = key_gscalar.type_exp();
                let value_type = value_gtype.type_exp();
                quote! {
                    ::indexmap::IndexMap<#key_type, #value_type>
                }
            }
            GType::JsonValue => {
                quote! {
                    ::granite::JsonValue
                }
            }
            GType::JsonObject => {
                quote! {
                    ::granite::JsonObject
                }
            }
            GType::JsonArray => {
                quote! {
                    ::granite::JsonArray
                }
            }
            GType::Range(_scalar) => {
                todo!("not implemented yet");
            }
        }
    }
}

impl GTypeExt for GScalar {
    fn type_exp(&self) -> TokenStream {
        match self {
            GScalar::char => quote! { char },
            GScalar::bool => quote! { bool },
            GScalar::i8 { .. } => quote! { i8 },
            GScalar::u8 { .. } => quote! { u8 },
            GScalar::i16 { .. } => quote! { i16 },
            GScalar::u16 { .. } => quote! { u16 },
            GScalar::i32 { .. } => quote! { i32 },
            GScalar::u32 { .. } => quote! { u32 },
            GScalar::i64 { .. } => quote! { i64 },
            GScalar::u64 { .. } => quote! { u64 },
            GScalar::f32 { .. } => quote! { f32 },
            GScalar::f64 { .. } => quote! { f64 },
            GScalar::String { .. } => quote! { String },
            GScalar::Uuid { .. } => quote! { ::granite::Uuid },
            GScalar::Decimal { .. } => quote! { ::granite::Decimal },
            GScalar::Integer => quote! { i32 },
            GScalar::BigInt => quote! { ::granite::BigInt },
            GScalar::DateUtc => quote! { ::granite::DateUtc },
            GScalar::DateTz => quote! { todo!("not implemented") },
            GScalar::DateTimeUtc => quote! { ::granite::DateTimeUtc },
            GScalar::DateTimeTz => quote! { ::granite::DateTimeTz },
            GScalar::Duration => quote! { todo!("not implemented") },
            GScalar::Time => quote! { todo!("not implemented") },
            GScalar::IpAddr => quote! { ::std::net::IpAddr },
        }
    }
}
