import { Decimal } from "./lib.mts";

/// A specific type for HTML strings
export type HTML = string;

/// htmlspecialchars equivalent
/// powered by the he.encode() function
export function HS(str: string | null | undefined): HTML {
    if (str === null || str === undefined) {
        return "";
    }

    // replace special chars
    return str
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

export function QA(str: string | null | undefined): HTML {
    if (str === null || str === undefined) {
        return `""`;
    }
    return `"${HS(str)}"`;
}

export function SE<T extends HTMLElement>(
    $search_element: HTMLElement | DocumentFragment | Document,
    selector: string,
): T {
    const $found_element: T = $search_element.querySelector(selector) as T;
    if ($found_element === null) {
        console.error("Element not found for selector `", selector, "` within", $search_element);
        throw new Error(`Element not found for selector "${selector}"`);
    }
    return $found_element;
}

export function SE_nullable<T extends HTMLElement | null>(
    $search_element: HTMLElement | DocumentFragment | Document,
    selector: string,
): T {
    const $found_element: T = $search_element.querySelector(selector) as T;
    return $found_element;
}

/// Takes arbitrary string input like "Welcome, Company, Inc.!" and turns it into "welcome-company-inc"
export function DASH(str: string): string {
    return str
        .replace(/[^a-z0-9]+/gi, "-")
        .replace(/^-|-$/g, "")
        .toLowerCase();
}

/// handy shortcut to creating an element with this syntax:
/// `elem("div.my-class", "inner html")`
export function make_element(tag_and_classes: string, inner_html: string): HTMLElement {
    const [tag, ...classes] = tag_and_classes.split(".");
    if (tag === undefined) {
        throw new Error("Format must be 'tag' or 'tag.class' or 'tag.class1.class2'");
    }
    const element = document.createElement(tag);
    element.innerHTML = inner_html;
    for (const cls of classes) {
        element.classList.add(cls);
    }
    return element;
}

/// Pass some CSS and get a style element back
export function make_style(css: string): HTMLStyleElement {
    const style = document.createElement("style");
    style.innerHTML = css;
    return style;
}

/// round to 0 digits keeping it numeric
export function ROUND0(x: number | Decimal) {
    if (x instanceof Decimal) {
        return x.to_scale(2);
    } else {
        return Math.round(x);
    }
}
/// round to 2 digits keeping it numeric
export function ROUND2(x: number | Decimal) {
    if (x instanceof Decimal) {
        return x.to_scale(2);
    } else {
        return Math.round(x * 100) / 100;
    }
}

const INT0_formatter = new Intl.NumberFormat("en-US", {
    style: "decimal",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
});

export function INT0(x: number | Decimal, empty_char?: string): string {
    if (x instanceof Decimal) {
        x = x.toNumber();
    }
    x = Math.round(x);
    if (x === 0 && empty_char !== undefined) {
        return empty_char;
    }
    return INT0_formatter.format(Math.round(x));
}

const INT2_formatter = new Intl.NumberFormat("en-US", {
    style: "decimal",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
});

export function INT2(x: number | Decimal, empty_char?: string): string {
    if (x instanceof Decimal) {
        x = x.to_scale(2).toNumber();
    } else {
        x = Math.round(x * 100) / 100;
    }
    if (x === 0 && empty_char !== undefined) {
        return empty_char;
    }
    return INT2_formatter.format(x);
}

const CUR0_formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
});

export function CUR0(x: number | Decimal, empty_char?: string): string {
    if (x instanceof Decimal) {
        x = x.toNumber();
    }

    if (Math.round(x) === 0 && empty_char !== undefined) {
        return empty_char;
    }
    return CUR0_formatter.format(Math.round(x));
}

const CUR2_formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
});

export function CUR2(x: number | Decimal, empty_char?: string): string {
    if (x instanceof Decimal) {
        x = x.to_scale(2).toNumber();
    }
    if (Math.round(x * 10000) === 0 && empty_char !== undefined) {
        return empty_char;
    }
    return CUR2_formatter.format(Math.round(x * 100) / 100);
}

const PER0_formatter = new Intl.NumberFormat("en-US", {
    style: "percent",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
});

export function PER0(x: number | Decimal, empty_char?: string): string {
    if (x instanceof Decimal) {
        x = x.toNumber();
    }
    if (Math.round(x * 100) === 0 && empty_char !== undefined) {
        return empty_char;
    }
    return PER0_formatter.format(x);
}

const PER2_formatter = new Intl.NumberFormat("en-US", {
    style: "percent",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
});

export function PER2(x: number | Decimal): string {
    if (x instanceof Decimal) {
        x = x.toNumber();
    }
    return PER2_formatter.format(x);
}

export function PLUR(quantity: number | Decimal, singular: string, plural?: string): string {
    /*
    A utility function for returning "1 mile" or "2 miles" or "1.0 miles".
    It uses identity comparison with 1, so 1.0 will NOT be singular.

    Usage:
    plur(100, 'Cat')          ->  "100 Cats"
    plur(1, 'Cat')            ->  "1 Cat"
    plur(2, 'Bunny')          ->  "2 Bunnies"
    plur(3, 'Woman', 'Women') ->  "3 Women"
    */
    if (quantity instanceof Decimal) {
        quantity = quantity.toNumber();
    }

    if (plural === undefined) {
        if (singular.endsWith("ies")) {
            plural = `${singular.slice(0, -3)}y`;
        } else if (singular.endsWith("s")) {
            plural = singular;
        } else {
            plural = `${singular}s`;
        }
    }

    quantity = Math.round(quantity);
    return `${quantity} ${quantity === 1 ? singular : plural}`;
}
