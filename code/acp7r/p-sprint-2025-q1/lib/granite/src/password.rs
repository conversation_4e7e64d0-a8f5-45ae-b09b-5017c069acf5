use serde::{Deserialize, Serialize};

/// Defines the structure for the password analysis result.
#[derive(Debug, Serialize, Deserialize, PartialEq)]
pub struct PasswordAnalysis {
    /// The entropy of the password in bits.
    pub entropy: f64,
    /// A normalized strength value from 0 to 100, suitable for a progress meter.
    pub meter_value: u8,
    /// A text string indicating the strength of the password.
    pub meter_text: String,
    /// True if the password is acceptable.
    pub acceptable: bool,
    /// True if the password contains lowercase letters.
    pub has_lowercase: bool,
    /// True if the password contains uppercase letters.
    pub has_uppercase: bool,
    /// True if the password contains numbers.
    pub has_numbers: bool,
    /// True if the password contains common symbols.
    pub has_symbols: bool,
    /// True if the password contains any non-ASCII (Unicode) characters.
    pub has_unicode: bool,
}

impl Default for PasswordAnalysis {
    fn default() -> Self {
        Self {
            entropy: 0.0,
            meter_value: 0,
            meter_text: "Create a new password.".to_string(),
            acceptable: false,
            has_lowercase: false,
            has_uppercase: false,
            has_numbers: false,
            has_symbols: false,
            has_unicode: false,
        }
    }
}

/// Calculates the entropy of a given password, representing its strength in bits,
/// and provides additional analysis for a strength meter.
pub fn password_analysis(password: &str) -> PasswordAnalysis {
    if password.is_empty() {
        return PasswordAnalysis::default();
    }

    let mut analysis = PasswordAnalysis::default();
    let mut character_pool_size = 0.0;

    const LOWERCASE_CHARS: &str = "abcdefghijklmnopqrstuvwxyz";
    const UPPERCASE_CHARS: &str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const NUMERIC_CHARS: &str = "0123456789";
    const SYMBOL_CHARS: &str = "!@#$%^&*()-_=+[{]}\\|;:'\",<.>/?`~";
    const UNICODE_POOL_ADDITION: f64 = 8617.0;

    for char in password.chars() {
        if char.is_ascii() {
            let ascii_char = char as u8;
            if (0x20..=0x7E).contains(&ascii_char) {
                if LOWERCASE_CHARS.contains(char) {
                    analysis.has_lowercase = true;
                } else if UPPERCASE_CHARS.contains(char) {
                    analysis.has_uppercase = true;
                } else if NUMERIC_CHARS.contains(char) {
                    analysis.has_numbers = true;
                } else if SYMBOL_CHARS.contains(char) {
                    analysis.has_symbols = true;
                }
            }
        } else {
            analysis.has_unicode = true;
        }
    }

    if analysis.has_lowercase {
        character_pool_size += LOWERCASE_CHARS.len() as f64;
    }
    if analysis.has_uppercase {
        character_pool_size += UPPERCASE_CHARS.len() as f64;
    }
    if analysis.has_numbers {
        character_pool_size += NUMERIC_CHARS.len() as f64;
    }
    if analysis.has_symbols {
        character_pool_size += SYMBOL_CHARS.len() as f64;
    }
    if analysis.has_unicode {
        character_pool_size += UNICODE_POOL_ADDITION;
    }

    if character_pool_size == 0.0 {
        return analysis;
    }

    analysis.entropy = password.len() as f64 * character_pool_size.log2();

    const MAX_ENTROPY_FOR_METER: f64 = 140.0;
    analysis.meter_value =
        (100.0_f64.min((analysis.entropy / MAX_ENTROPY_FOR_METER) * 100.0)) as u8;
    analysis.acceptable = analysis.meter_value >= 60;

    analysis.meter_text = if analysis.entropy < 28.0 {
        "Very Weak.  Add more characters.".to_string()
    } else if analysis.entropy < 60.0 {
        "Weak. Add more character types.".to_string()
    } else if analysis.entropy < 90.0 {
        "Good. More characters increase strength.".to_string()
    } else if analysis.entropy < 120.0 {
        "Strong.  You are good to go.".to_string()
    } else {
        "Very Strong.  Excellent!".to_string()
    };

    analysis
}
