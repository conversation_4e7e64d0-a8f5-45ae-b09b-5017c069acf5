[package]
name = "approck-postgres"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
granite = { workspace = true }

bb8 = { workspace = true }
bb8-postgres = { workspace = true }
serde = { workspace = true, features = ["derive"] }
tokio-postgres = { workspace = true, features = ["with-serde_json-1", "with-time-0_3", "with-uuid-1", "with-chrono-0_4"] }
tracing = { workspace = true }
approck = { workspace = true }