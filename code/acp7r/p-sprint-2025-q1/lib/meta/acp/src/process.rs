use std::io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use std::path::PathBuf;
use std::process::{Command, Stdio};
use std::thread;

pub fn deno_binary(workspace: &'static crate::Workspace) -> PathBuf {
    workspace
        .acp_config
        .get_target_binary_dependency("deno")
        .expect("no deno binary available")
        .target_path()
}

pub fn esbuild_binary(workspace: &'static crate::Workspace) -> PathBuf {
    workspace
        .acp_config
        .get_target_binary_dependency("esbuild")
        .expect("no esbuild binary available")
        .target_path()
}

pub fn call_deno_fmt<I, S>(
    workspace: &'static crate::Workspace,
    paths: I,
) -> std::process::ExitStatus
where
    I: IntoIterator<Item = S>,
    S: AsRef<std::ffi::OsStr>,
{
    let mut cmd = std::process::Command::new(workspace.path.join(deno_binary(workspace)));

    cmd.current_dir(&workspace.path);
    cmd.arg("fmt");
    cmd.arg("--config").arg("deno.jsonc");
    cmd.arg("--quiet");
    cmd.args(paths);

    cmd.status().expect("failed to execute process")
}

pub fn call_deno<I, S>(
    workspace: &'static crate::Workspace,
    subcommand: &str,
    args: I,
) -> std::process::ExitStatus
where
    I: IntoIterator<Item = S>,
    S: AsRef<std::ffi::OsStr>,
{
    let mut cmd = std::process::Command::new(workspace.path.join(deno_binary(workspace)));

    cmd.current_dir(&workspace.path);
    cmd.arg(subcommand);
    cmd.args(args);

    cmd.status().expect("failed to execute process")
}

pub fn call_tsc(workspace: &'static crate::Workspace, args: &[&str]) {
    let args = [
        "--allow-env",
        "--allow-read",
        "--allow-write",
        "node_modules/typescript/lib/tsc.js",
    ]
    .into_iter()
    .chain(args.iter().cloned())
    .collect::<Vec<_>>();

    let status = call_deno(workspace, "run", args.as_slice());
    if !status.success() {
        panic!("Call to tsc failed: {:?}, Args = {:?}", status, args);
    }
}

pub fn call_cargo_limited(
    workspace: &'static crate::Workspace,
    packages: Option<&[String]>,
    subcmd: &str,
    args: &[&str],
) {
    let build_crates = match packages {
        Some(packages) => workspace.get_filtered_build_crates(packages),
        None => workspace.get_build_crates(),
    };

    let mut cmd = std::process::Command::new("cargo");
    cmd.current_dir(&workspace.path);
    cmd.arg(subcmd);
    for crate_ref in build_crates {
        cmd.arg("-p");
        cmd.arg(&crate_ref.crate_name.0);
    }
    cmd.args(args);

    let status = cmd.status().expect("failed to execute process");

    if !status.success() {
        std::process::exit(status.code().unwrap_or(1));
    }
}

pub fn call_rustfmt<I, S>(workspace: &'static crate::Workspace, args: I)
where
    I: IntoIterator<Item = S>,
    S: AsRef<std::ffi::OsStr>,
{
    let mut cmd = std::process::Command::new("rustfmt");
    cmd.current_dir(&workspace.path);
    cmd.arg("--edition").arg("2024");

    cmd.args(args);

    let status = cmd.status().expect("failed to execute rustfmt");

    if !status.success() {
        panic!("Call to rustfmt failed: {:?}", status);
    }
}

pub fn call_cargo_all(workspace: &'static crate::Workspace, args: &[&str]) {
    let mut cmd = std::process::Command::new("cargo");
    cmd.current_dir(&workspace.path);
    cmd.args(args);

    let status = cmd.status().expect("failed to execute process");

    if !status.success() {
        std::process::exit(status.code().unwrap_or(1));
    }
}

pub fn call_cargo_run_multi(
    workspace: &'static crate::Workspace,
    packages: Option<&[String]>,
    release: bool,
) {
    let apps = match packages {
        Some(packages) => workspace.get_filtered_build_apps(packages),
        None => workspace.get_build_apps(),
    };

    let mut max_length = 0;
    let variant = if release {
        "target/release"
    } else {
        "target/debug"
    };

    let executables = apps
        .iter()
        .map(|app| {
            if app.crate_name.0.len() > max_length {
                max_length = app.crate_name.0.len();
            }

            let p = workspace.path.join(variant).join(&app.crate_name.0);
            (app.crate_name.clone(), p)
        })
        .filter(|(_app_name, app_binary)| app_binary.exists())
        .collect::<Vec<_>>();

    let handles: Vec<_> = executables
        .into_iter()
        .map(|(app_name, executable_path)| {
            thread::spawn(move || {
                let mut child = Command::new(executable_path)
                    .current_dir(&workspace.path)
                    .stdout(Stdio::piped())
                    .spawn()
                    .expect("Failed to start process");

                // prefix should be padded to a consistent length based on max_length
                let prefix = format!("[{:width$}] ", app_name, width = max_length);

                let stdout = child.stdout.take().expect("Failed to take stdout of child");
                let reader = BufReader::new(stdout);

                for line in reader.lines() {
                    match line {
                        Ok(line) => println!("{}{}", prefix, line),
                        Err(e) => eprintln!("{} Error reading line: {}", prefix, e),
                    }
                }

                child.wait().expect("Failed to wait for child process");
                // Optional: Handle termination of child process if Ctrl+C is pressed
            })
        })
        .collect();

    // Wait for all threads to finish
    for handle in handles {
        handle.join().expect("Thread panicked");
    }

    // Optional: Cleanup or signal handling for child processes
}
