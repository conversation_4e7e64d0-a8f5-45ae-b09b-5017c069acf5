use std::{io::Read, process::Command};

pub(super) fn test_crate(workspace: &'static crate::Workspace, crate_name: &str) {
    crate::process::call_cargo_all(workspace, &["test", "-p", crate_name]);
}

pub(crate) fn test_local(workspace: &'static crate::Workspace) {
    crate::process::call_cargo_limited(workspace, None, "test", &[]);
    crate::build::deno2::deno_test(workspace);
}

pub(crate) fn test_postgres(workspace: &'static crate::Workspace) {
    let mut script_path = workspace.path.clone();
    script_path.push("meta/Docker/test-postgres/");

    let mut command = Command::new("sh");
    command.arg("run.sh");
    command.current_dir(&script_path);

    let mut child = command.spawn().expect("Unable to run shellscript");
    let exit_status = &child.wait().unwrap();
    if !exit_status.success() {
        if let Some(mut stderr) = child.stderr {
            let mut buffer = vec![];
            stderr.read_to_end(&mut buffer).unwrap();

            let message = String::from_utf8_lossy(&buffer);
            eprintln!("run.sh was unsuccessful: [{}]\n{}", exit_status, message)
        } else {
            eprintln!("run.sh failed with {}", exit_status)
        }
    } else {
        println!("\nrun.sh completed successfully")
    }
}

pub(crate) fn test_redis(workspace: &crate::Workspace) {
    let mut script_path = workspace.path.clone();
    script_path.push("meta/Docker/test-redis/");

    let mut command = Command::new("sh");
    command.arg("run.sh");
    command.current_dir(&script_path);

    let mut child = command.spawn().expect("Unable to run shellscript");
    let exit_status = &child.wait().unwrap();
    if !exit_status.success() {
        if let Some(mut stderr) = child.stderr {
            let mut buffer = vec![];
            stderr.read_to_end(&mut buffer).unwrap();

            let message = String::from_utf8_lossy(&buffer);
            eprintln!("run.sh was unsuccessful: [{}]\n{}", exit_status, message)
        } else {
            eprintln!("run.sh failed with {}", exit_status)
        }
    } else {
        println!("\nrun.sh completed successfully")
    }
}
