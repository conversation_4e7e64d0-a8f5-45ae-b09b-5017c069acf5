// TODO: this should be refactored to base all paths on the workspace path and not each crate path.  Then, all entrypoints for all building apps should be invoked at once, and the final output should be merged together appropriately so that each app has it's own set of .js and .css files. That is also the time to do conflict detection and error handling.

// TODO: make sure that the raw output of esbuild is moved into a single directory per app but that those files are not actually written to if they haven't changed, so that include_str!() won't cause a recompile if not needed

use crate::Workspace;
use approck_compiler::{CompileError, NodeTree};
use std::path::{Path, PathBuf};

pub fn run_esbuild(
    workspace: &'static Workspace,
    app_ref: &'static crate::Application,
) -> Result<NodeTree, CompileError> {
    let esbuild_target = workspace
        .path
        .join("target/esbuild")
        .join(app_ref.crate_name.to_string());

    // create it
    std::fs::create_dir_all(&esbuild_target).expect("Error creating esbuild target directory");

    let app_crate = approck_compiler::compiler::CrateInfo {
        name: app_ref.crate_name.0.clone(),
        ident: app_ref.ident.clone(),
        rel_path: app_ref.rel_path.clone(),
        abs_path: app_ref.abs_path.clone(),
    };

    // get the extended crates for this app (in the approck format)
    let extended_crates: Vec<approck_compiler::compiler::CrateInfo> = app_ref
        .get_extended_crates(workspace)
        .iter()
        .map(|extended_crate| approck_compiler::compiler::CrateInfo {
            name: extended_crate.crate_name.0.clone(),
            ident: extended_crate.ident.clone(),
            rel_path: extended_crate.rel_path.clone(),
            abs_path: extended_crate.abs_path.clone(),
        })
        .collect();

    // merge entrypoints into one flatmap
    let entrypoints: Vec<PathBuf> = app_ref
        .get_extended_crates(workspace)
        .iter()
        .flat_map(|crate_ref| crate_ref.get_entrypoints())
        .collect();

    // Invoke esbuild all at once
    let es_build_map = esbuild(workspace, &esbuild_target, &entrypoints);

    approck_compiler::compiler::scan_filesystem_and_produce_node_tree(
        &workspace.path,
        &app_crate,
        &extended_crates,
        es_build_map,
    )
}

fn esbuild(
    workspace: &'static crate::Workspace,
    output_directory: &Path,
    relative_entrypoints: &[PathBuf],
) -> approck_compiler::EsBuildMap {
    // Collect timestamps into a Vec
    let timestamps = std::fs::read_dir(output_directory)
        .expect("Failed to read directory")
        .filter_map(|entry| {
            let entry = entry.expect("Failed to get directory entry");
            let path = entry.path();
            if path.is_file()
                && path.file_name().unwrap_or_default().to_string_lossy() != "esbuild.json"
            {
                let metadata = std::fs::metadata(&path).expect("Failed to get file metadata");
                let mtime = metadata
                    .modified()
                    .expect("Failed to get modification time");
                Some((path, mtime))
            } else {
                None
            }
        })
        .collect::<Vec<_>>();

    // invoke esbuild
    let mut cmd = std::process::Command::new(
        workspace
            .path
            .join(crate::process::esbuild_binary(workspace)),
    );

    let metafile_path = output_directory.join("esbuild.json");

    cmd.current_dir(&workspace.path);
    cmd.arg("--bundle");
    cmd.arg("--sourcemap");
    cmd.arg("--splitting");
    cmd.arg("--format=esm");
    cmd.arg("--loader:.mts=ts");
    cmd.arg("--loader:.mcss=css");
    cmd.arg("--entry-names=entry-[hash]");
    cmd.arg("--chunk-names=chunk-[hash]");
    cmd.arg("--asset-names=asset-[hash]");
    cmd.arg("--log-level=warning");
    cmd.arg(format!("--outdir={}", output_directory.to_string_lossy()));
    cmd.arg(format!("--metafile={}", metafile_path.to_string_lossy()));
    cmd.arg(format!("--outbase={}", output_directory.to_string_lossy()));
    cmd.args(relative_entrypoints);

    // run it and check for errors
    let status = cmd.status().unwrap_or_else(|e| {
        eprintln!("Error running {:?} due to {}", cmd, e);
        std::process::exit(1);
    });

    if !status.success() {
        println!("Status code of cmd: {:?} was {}", cmd, status);
        std::process::exit(1);
    }

    // Parse the meta file.  All metafile paths are relative to the workspace.
    let es_build_map = approck_compiler::parse_esbuild_metafile(&workspace.path, &metafile_path);

    // run lightningcss on all css files
    {
        let fs = lightningcss::bundler::FileProvider::new();

        for path in es_build_map.iter_abs_output_css_paths() {
            let mut bundler = lightningcss::bundler::Bundler::new(
                &fs,
                None,
                lightningcss::stylesheet::ParserOptions::default(),
            );

            let stylesheet = bundler.bundle(path).unwrap();

            let printoptions = lightningcss::printer::PrinterOptions {
                ..lightningcss::printer::PrinterOptions::default()
            };

            let css = stylesheet.to_css(printoptions).unwrap();

            // write css.code to outputfile
            std::fs::write(path, css.code).unwrap();
        }
    }

    // Reset timestamps for all existing files using FileTimes
    for (path, mtime) in timestamps {
        let file = std::fs::File::options()
            .write(true)
            .open(&path)
            .unwrap_or_else(|e| panic!("Failed to open {}: {}", path.display(), e));
        let times = std::fs::FileTimes::new().set_modified(mtime);
        file.set_times(times).unwrap_or_else(|e| {
            panic!("Failed to restore timestamp for {}: {}", path.display(), e)
        });
    }

    es_build_map
}
