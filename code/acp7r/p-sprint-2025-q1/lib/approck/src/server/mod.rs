pub use chasetls::tls::{CertificateId, TlsCertificateKeyPair};
use granite::{ResultExt, uuid_v7};
use headers::{CacheControl, HeaderMapExt};
use tracing::Instrument;

use http::{
    HeaderMap, HeaderValue, StatusCode,
    header::{SET_COOKIE, UPGRADE},
};
use http_body_util::BodyExt;
use std::{
    net::{IpAddr, SocketAddr},
    sync::Arc,
    time::Duration,
};
pub use tls::{CertificateRequestState, TlsCertificateKeyPemPair, TlsConfig};

pub mod exports;
pub mod proxy;
pub mod response;
pub mod tls;
pub mod websocket;

pub trait App: crate::App {
    fn webserver_system(&self) -> &Module;

    fn webserver_route(
        &'static self,
        req: Request,
    ) -> impl std::future::Future<Output = crate::server::response::Result> + Send;

    fn handle_api(
        &'static self,
        _identity: &Arc<Self::Identity>,
        _api: &str,
        _input: granite::JsonValue,
    ) -> impl std::future::Future<Output = granite::JsonValue> + Send;

    /// Provide the user with some nice looking response.
    fn webserver_handle_error(&self, mut error: granite::Error) -> crate::server::response::Result {
        println!("ERROR: {error:#?}");

        if let Some(http_response) = error.render_http_response() {
            return Ok(http_response.into());
        }

        Ok(standard_handle_error(error))
    }

    fn request_tls_alpns_challenge_response(
        &self,
        _server_name: &str,
    ) -> impl std::future::Future<
        Output = granite::Result<Option<(CertificateId, TlsCertificateKeyPair)>>,
    > + Send {
        std::future::ready(Ok(None))
    }

    /// Request a certificate.
    ///
    /// The default implementation of this trait method uses the
    /// [`TlsCertificateKeyPair::development()`] for all requests.
    fn request_certificate(
        &self,
        _server_name: &str,
    ) -> impl std::future::Future<Output = granite::Result<CertificateRequestState>> + Send {
        std::future::ready(Ok(CertificateRequestState::Ready(std::sync::Arc::new(
            TlsCertificateKeyPemPair::development()
                .try_into_key_pair()
                .expect("valid development pem"),
        ))))
    }
}

pub trait Identity: crate::Identity {}

#[derive(Debug, serde::Deserialize, serde::Serialize)]
pub struct ModuleConfig {
    #[serde(default = "default_host")]
    pub host: IpAddr,

    pub port: u16,

    #[serde(default)]
    pub tls: TlsMode,

    pub domain: Option<String>,
}

/// Required because TOML doesn't support explicit nil values and we'd like to have a default of
/// Some with the ability to specify none.
#[derive(Debug, serde::Deserialize, serde::Serialize)]
#[serde(rename_all = "snake_case")]
pub enum TlsMode {
    /// Use TLS for the HTTP connection.
    Tls(TlsConfig),
    /// Don't attempt to use TLS for the HTTP connection.
    None,
}

impl Default for TlsMode {
    fn default() -> Self {
        Self::Tls(TlsConfig::default())
    }
}

fn default_host() -> IpAddr {
    "127.0.0.1".parse().unwrap()
}

#[derive(Debug)]
pub struct Module {
    host: IpAddr,
    port: u16,
    tls_mode: TlsMode,
    domain: String,
}

impl crate::Module for Module {
    type Config = ModuleConfig;
    fn new(config: Self::Config) -> granite::Result<Self> {
        Ok(Self {
            host: config.host,
            port: config.port,
            tls_mode: config.tls,
            domain: config
                .domain
                .unwrap_or_else(|| "local.acp7.net".to_string()),
        })
    }
    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}

impl Module {
    pub fn host(&self) -> IpAddr {
        self.host
    }
    pub fn port(&self) -> u16 {
        self.port
    }

    pub fn tls_mode(&self) -> &TlsMode {
        &self.tls_mode
    }
    pub fn domain(&self) -> String {
        self.domain.clone()
    }
    pub fn url(&self) -> String {
        format!("https://{}:{}", self.domain(), self.port())
    }
}

#[derive(Debug)]
pub struct Request {
    parts: http::request::Parts,
    body: Option<hyper::body::Incoming>,
    remote_address: SocketAddr,
    request_uuid: String,
}

impl TryFrom<Request> for hyper::Request<hyper::body::Incoming> {
    type Error = granite::Error;

    fn try_from(request: Request) -> Result<Self, Self::Error> {
        let Request { parts, body, .. } = request;
        let body = body.ok_or_else(|| {
            granite::Error::new(granite::ErrorType::Unexpected)
                .add_context("Body of this request has already been consumed")
        })?;

        Ok(hyper::Request::from_parts(parts, body))
    }
}

#[non_exhaustive]
pub enum Frame {
    Data(bytes::Bytes),
    Trailers(http::HeaderMap),
}

impl From<Vec<u8>> for Frame {
    fn from(value: Vec<u8>) -> Self {
        Self::Data(value.into())
    }
}

impl From<&'static [u8]> for Frame {
    fn from(value: &'static [u8]) -> Self {
        Self::Data(value.into())
    }
}

impl From<bytes::Bytes> for Frame {
    fn from(value: bytes::Bytes) -> Self {
        Self::Data(value)
    }
}

impl From<HeaderMap> for Frame {
    fn from(value: HeaderMap) -> Self {
        Self::Trailers(value)
    }
}

impl Request {
    /// Construct a [`hyper::Request`] from this request.
    ///
    /// **NOTE:** this will move the body out of this request and into the
    /// [`hyper::Request`].
    pub fn as_hyper(&mut self) -> granite::Result<hyper::Request<hyper::body::Incoming>> {
        Ok(hyper::Request::from_parts(
            self.parts.clone(),
            self.body.take().ok_or_else(|| {
                granite::Error::new(granite::ErrorType::Unexpected)
                    .add_context("Body of this request has already been consumed")
            })?,
        ))
    }
    /// Construct a [`Request`] from a [`hyper::Request`], and the
    /// `remote_address` (the address of the client that made the request).
    pub fn from_hyper(
        hyper_request: hyper::Request<hyper::body::Incoming>,
        remote_address: SocketAddr,
    ) -> Self {
        let (parts, body) = hyper_request.into_parts();
        let request_uuid = uuid_v7().to_string();
        Self {
            parts,
            body: Some(body),
            remote_address,
            request_uuid,
        }
    }

    /// The address of the client that made this request.
    pub fn remote_address(&self) -> SocketAddr {
        self.remote_address
    }

    /// The IP address of the client that made this request.
    pub fn remote_ip(&self) -> std::net::IpAddr {
        self.remote_address.ip()
    }

    pub fn request_uuid(&self) -> &str {
        &self.request_uuid
    }

    /// If the request is a websocket upgrade request, will return a websocket upgrade response and
    /// run the `websocket_handler` with the created socket, otherwise this method will return
    /// `None`.
    ///
    /// The `websocket_handler` is an async function that you provide which communicates with the
    /// provided [`websocket::WebSocket`] and returns a [`granite::Result`], with a
    /// [`granite::Result::Error`] variant for any errors that occur during upgrade of the
    /// connection which are fatal and the connection will be closed.
    pub async fn upgrade_to_websocket<HANDLER, FUT>(
        &mut self,
        websocket_handler: HANDLER,
        // ins_gk_string: String,
    ) -> granite::Result<Option<crate::server::response::WebSocketUpgrade>>
    where
        HANDLER: FnOnce(websocket::WebSocket) -> FUT + Send + 'static,
        FUT: std::future::Future<Output = granite::Result<()>> + Send + 'static,
    {
        if !self.is_upgrade() {
            return Ok(None);
        }

        websocket::upgrade(self, websocket_handler).await.map(Some)
    }

    /// Get the chunks of the path as a vec.  For example:  
    ///   `/a/b/c/d` -> `["a", "b", "c", "d"]`
    pub fn path_chunks(&self) -> Vec<&str> {
        self.parts.uri.path().split('/').skip(1).collect()
    }

    pub fn path(&self) -> &str {
        self.parts.uri.path()
    }

    pub fn uri(&self) -> &http::Uri {
        &self.parts.uri
    }

    /// return the content type as a Mime if a valid one was provided, otherwise None
    // TODO: unit test this
    pub fn content_type(&self) -> Option<mime::Mime> {
        if let Some(content_type) = self.parts.headers.typed_get::<headers::ContentType>() {
            let mime: mime::Mime = content_type.into();
            Some(mime)
        } else {
            None
        }
    }

    /// return the content length or None if no valid content type was provided
    // TODO: unit test this
    pub fn content_length(&self) -> Option<u64> {
        self.parts
            .headers
            .typed_get::<headers::ContentLength>()
            .map(|cl| cl.0)
    }

    pub fn uri_string(&self) -> String {
        self.parts.uri.to_string()
    }

    /// Get the http::HeaderMap from the request
    pub fn headers(&self) -> &http::HeaderMap {
        &self.parts.headers
    }

    pub fn session_token(&self) -> String {
        if let Some(cookies) = self.parts.headers.typed_get::<headers::Cookie>() {
            if let Some(cookie) = cookies.get("SessionToken") {
                let cookie = cookie.trim();
                if cookie.len() == 64 {
                    return cookie.to_owned();
                }
            }
        }

        granite::ts_random_hex(64)
    }

    pub fn iter_query_pairs(&self) -> url::form_urlencoded::Parse<'_> {
        url::form_urlencoded::parse(self.parts.uri.query().unwrap_or("").as_bytes())
    }

    pub async fn read_body_as_bytes(&mut self) -> Result<bytes::Bytes, granite::Error> {
        let body = self.body.take().ok_or_else(|| {
            granite::Error::new(granite::ErrorType::Unexpected)
                .add_context("Body of request has already been consumed")
        })?;
        let collected = body.collect().await?;
        Ok(collected.to_bytes())
    }

    pub fn is_upgrade(&self) -> bool {
        let connection = match self.parts.headers.typed_get::<headers::Connection>() {
            Some(connection) => connection,
            None => return false,
        };
        let upgrade = match self.parts.headers.typed_get::<headers::Upgrade>() {
            Some(upgrade) => upgrade,
            None => return false,
        };
        connection.contains(UPGRADE) && upgrade == headers::Upgrade::websocket()
    }

    // write a function to iter_body_query_pairs
    pub async fn read_body_query_pairs(&mut self) -> Vec<(String, String)> {
        let bytes = self.read_body_as_bytes().await.unwrap();
        let rval: Vec<_> = url::form_urlencoded::parse(&bytes)
            .map(|(a, b)| (a.to_string(), b.to_string()))
            .collect();
        rval
    }

    pub async fn read_body_as_json_value(&mut self) -> Result<granite::JsonValue, granite::Error> {
        let body = self.read_body_as_bytes().await?;
        let body = std::str::from_utf8(&body)?;
        let body: ::granite::JsonValue = serde_json::from_str(body)?;
        Ok(body)
    }

    pub async fn read_body_as_json_object(
        &mut self,
    ) -> Result<granite::JsonObject, granite::Error> {
        let body = self.read_body_as_bytes().await?;
        let body = std::str::from_utf8(&body)?;
        let body: ::granite::JsonObject = serde_json::from_str(body)?;
        Ok(body)
    }

    pub async fn read_body_as_json_array(&mut self) -> Result<granite::JsonArray, granite::Error> {
        let body = self.read_body_as_bytes().await?;
        let body = std::str::from_utf8(&body)?;
        let body: ::granite::JsonArray = serde_json::from_str(body)?;
        Ok(body)
    }

    pub fn has_query_string(&self) -> bool {
        self.parts.uri.query().is_some()
    }

    pub fn is_post(&self) -> bool {
        self.parts.method == http::Method::POST
    }

    pub fn is_get(&self) -> bool {
        self.parts.method == http::Method::GET
    }

    pub fn method(&self) -> http::Method {
        self.parts.method.to_owned()
    }

    // TODO: make this more robust and tested
    pub fn auth_basic(&self) -> Option<(String, String)> {
        let auth_header = self.parts.headers.get("Authorization")?;
        let auth_header = auth_header.to_str().ok()?;
        let auth_header = auth_header.trim();
        if auth_header.starts_with("Basic ") {
            let auth_header = auth_header.trim_start_matches("Basic ");
            let auth_header = granite::base64_decode_str(auth_header);
            let mut auth_header: std::str::SplitN<char> = auth_header.splitn(2, ':');
            let username = auth_header.next()?.to_owned();
            let password = auth_header.next()?.to_owned();
            Some((username, password))
        } else {
            None
        }
    }

    pub fn auth_bearer(&self) -> Option<String> {
        let auth_header = self.parts.headers.get("Authorization")?;
        let auth_header = auth_header.to_str().ok()?;
        let auth_header = auth_header.trim();
        if auth_header.starts_with("Bearer ") {
            let auth_header = auth_header.trim_start_matches("Bearer ");
            Some(auth_header.to_owned())
        } else {
            None
        }
    }
}

// We would like to have different traits that APP implements, different projects might implement
// different sets of traits. How do we make these traits available in the application code after
// passing through here?
pub async fn serve<APP>(app: &'static APP) -> granite::Result<()>
where
    APP: self::App + Send + Sync + 'static,
{
    let webserver_system = &app.webserver_system();

    let addr = std::net::SocketAddr::from((webserver_system.host(), webserver_system.port()));

    let tcp_listener: tokio::net::TcpListener = tokio::net::TcpListener::bind(addr).await?;
    match webserver_system.tls_mode() {
        TlsMode::None => {
            println!("Listening on http://{addr}");
            loop {
                let (tcp_stream, remote_address) = tcp_listener.accept().await?;
                crate::info!(%remote_address, "New connection");
                let hyper_service = hyper::service::service_fn(move |request| {
                    handle_request(app, request, remote_address)
                });
                let io = hyper_util::rt::TokioIo::new(tcp_stream);

                tokio::task::spawn(
                    async move {
                        if let Err(err) = hyper_util::server::conn::auto::Builder::new(
                            hyper_util::rt::tokio::TokioExecutor::new(),
                        )
                        .serve_connection_with_upgrades(io, hyper_service)
                        .await
                        {
                            crate::error!(error = ?err, "Error serving connection");
                        }
                        crate::info!("Connection closed");
                    }
                    .instrument(tracing::info_span!("connection", %remote_address)),
                );
            }
        }
        TlsMode::Tls(tls_config) => {
            println!("Listening on https://{addr}");
            tls::serve(tls_config, app, tcp_listener).await
        }
    }
}

/// Basic rendering of errors.
pub fn standard_handle_error(error: granite::Error) -> crate::server::response::Response {
    let error_type = error.get_error_type();
    let error_uuid = error.get_error_uuid();
    let request_uuid = error.get_request_uuid().unwrap_or("-");

    let uri = error.get_uri().unwrap_or("-");

    let code_display = match error.get_code() {
        Some(code) => &format!("({code}) "),
        None => "",
    };

    let message = error.get_external_message().unwrap_or_default();

    let text = format!(
        "type: {error_type}\nerror: {error_uuid}\nrequest: {request_uuid}\nuri: {uri}\n{code_display}{message}\n"
    );

    response::Response::Text(response::Text::new_with_status(
        text,
        StatusCode::INTERNAL_SERVER_ERROR,
    ))
}

/// `remote_address` - the IP address of the client making the request.
async fn handle_request<APP>(
    app: &'static APP,
    hyper_request: hyper::Request<hyper::body::Incoming>,
    remote_address: SocketAddr,
) -> Result<hyper::Response<response::HyperResponseBody>, hyper::Error>
where
    APP: self::App + Send + Sync + 'static,
{
    let request = Request::from_hyper(hyper_request, remote_address);
    let span = tracing::info_span!(
        "request",
        id = %request.request_uuid(),
        method = %request.method(),
        uri = %request.uri(),
    );

    async move {
        crate::debug!(headers = ?request.headers(), "Handling request");
        // TODO(luke): do we want connection upgrade h2c HTTP2 via plaintext?
        let path = request.uri().path().to_owned();
        let session_token = request.session_token();
        let response = match app.webserver_route(request).await {
            Ok(response) => response,
            Err(error) => app
                .webserver_handle_error(error)
                .unwrap_or_else(standard_handle_error),
        };
        let mut hyper_response: hyper::Response<response::HyperResponseBody> = response.into();
        // TODO(luke): should this be moved into the JavaScript and CSS response implementations?
        if path.ends_with(".js") || path.ends_with(".css") {
            hyper_response.headers_mut().typed_insert(
                CacheControl::new()
                    .with_public()
                    .with_max_age(Duration::from_secs(30))
                    .with_immutable(),
            );
        }

        //         // additional header to let browsers know that they can use QUIC/HTTP3
        //         // Note: this must be added after .render() or it won't be included.
        //         response.headers.insert(
        //             "alt-svc",
        //             format!(r#"h3=":{port}"; ma=2592000"#).parse().unwrap(),
        //         );

        // Add session_token cookie
        match HeaderValue::try_from(
            cookie::Cookie::build(("SessionToken", session_token))
                .http_only(true)
                .max_age(cookie::time::Duration::days(3650))
                .path("/")
                .build()
                .to_string(),
        )
        .amend(|e| e.add_context("Error converting Cookie into HeaderValue"))
        {
            Ok(header_value) => {
                hyper_response
                    .headers_mut()
                    .insert(SET_COOKIE, header_value);
            }
            Err(error) => eprintln!("{error}"),
        }

        Ok(hyper_response)
    }
    .instrument(span)
    .await
}
