mod codegen;
mod filesystem;
mod inputfile;

#[cfg(test)]
mod tests;

use std::path::{Path, PathBuf};

/// A filesystem path hit (e.g. git-grep matches a macro line)
#[allow(dead_code)]
struct PathHit {
    /// The absolute path to the file (e.g. /home/<USER>/code/workspace/client/project-crate/src/web/admin/edit.rs)
    abs_path: PathBuf,

    /// Workspace relative path to the file (e.g. client/project-crate/src/web/admin/edit.rs)
    rel_path: String,

    /// The file extension
    ext: Option<String>,

    /// The name of the crate that this file belongs to (e.g. project-crate)
    crate_name: String,

    /// The relative path to the crate from the workspace (e.g. client/project-crate)
    crate_rel_path: String,

    /// A syn::Path that refers to the file (e.g. project_crate::web::admin::edit)
    syn_path: syn::Path,
}

#[derive(Debug)]
pub struct CrateInfo {
    pub name: String,
    pub ident: String,
    pub rel_path: String,
    pub abs_path: PathBuf,
}

impl PathHit {
    /// Returns the web_dir and script_path from the rel_path (e.g. ("web", "admin/edit.rs")).
    /// Note that the script_path never starts with a /
    pub fn web_dir_and_script_path(&self) -> (String, String) {
        // remove the prefix of the crate path + src/
        let prefix = format!("{}/src/", self.crate_rel_path);

        let src_path = self
            .rel_path
            .strip_prefix(&prefix)
            .expect("0x8274872648: Failed to strip prefix from rel_path")
            .to_string();

        let (web_dir, script_path) = src_path.split_once('/').expect("Failed to split src_path");

        // web, web_api, web_sales1, are all potentially valid web_dirs
        assert!(web_dir.starts_with("web"));

        (web_dir.to_string(), script_path.to_string())
    }
}

/// Scans the filesystem for approck::http and approck::prefix attributes and returns a NodeTree type
pub fn scan_filesystem_and_produce_node_tree(
    workspace_path: &Path,
    app_crate: &CrateInfo,
    extended_crates: &[CrateInfo],
    es_build_map: crate::EsBuildMap,
) -> Result<crate::NodeTree, crate::CompileError> {
    // consumes the paths and reuturns a Vec<PathHit>

    // this contains paths that have both approck::http and approck::prefix within them
    let path_hits = self::filesystem::scan(workspace_path, app_crate, extended_crates);

    let parse_response = self::inputfile::parse_all(app_crate, path_hits)?;

    let mut node_tree = crate::NodeTree::new(es_build_map, 64);

    // Source #2: [approck::prefix] auth and menu functions
    for prefix_module in parse_response.prefix_modules {
        node_tree.ingest_prefix_module(prefix_module);
    }

    // Source #3: [approck::http] request functions
    // TODO: remove Clone support through this chain
    for http_module in parse_response.http_modules {
        node_tree.ingest_http_module(http_module);
    }

    // Source #4: [approck::api] request functions
    for api_module in parse_response.api_modules {
        node_tree.ingest_api_module(api_module);
    }

    // hack to return both the codegen and the route tree
    Ok(node_tree)
}
