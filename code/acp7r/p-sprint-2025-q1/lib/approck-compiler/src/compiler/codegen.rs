use crate::MenuParamType;

use crate::{
    <PERSON><PERSON><PERSON><PERSON><PERSON>, MenuFunction, RequestH<PERSON><PERSON>, RouteExt, RouteTreeRoute, RouteTreeStaticFile,
    StaticFileHandler,
};
use granite::{WebPath, WebPathCapture, WebPathSegment};
use granite_compiler::webpath::WebPathTokens;
use proc_macro2::TokenStream;
use quote::{format_ident, quote};
use syn::Ident;

impl crate::RouteTree {
    pub fn codegen_router(&self, version_file_path: &std::path::Path) -> syn::File {
        let codegen_router_inner_tokens = self.codegen_router_inner();

        // TODO: this is stubbed in with hardcoded support for web instead of iterating
        let menu_calls_tokens = menu_calls(&self.get("web").unwrap().self_menu_functions);
        let static_phf_tokens = static_pfh(self.iter_static_files());
        let api_matches = self.make_api_matches();

        // Convert PathBuf to string literal for include_str!
        let version_file_str = version_file_path.to_string_lossy();

        let token_stream = quote! {
            #![allow(clippy::let_and_return)]
            use granite::ResultExt;
            use std::sync::Arc;

            #[allow(clippy::match_single_binding, unused_variables, clippy::single_match, unused_mut)]
            pub async fn api(
                app: &'static crate::AppStruct,
                identity: &Arc<crate::IdentityStruct>,
                api: &str,
                input: granite::JsonValue,
            ) -> granite::JsonValue {
                match api {
                    #(#api_matches)*
                    api_name => granite::json!({ "Err": { "Other": [format!("Unknown API: {api_name}")] }}),
                }
            }

            #static_phf_tokens

            pub async fn router(
                app: &'static crate::AppStruct,
                mut req: approck::server::Request,
            ) -> granite::Result<approck::server::response::Response> {
                let identity = Arc::new(approck::App::auth(app, &req).await.amend(|e| {
                    e.set_uri(req.uri_string())
                        .set_request_uuid(req.request_uuid().to_string())
                })?);

                router_inner(app, &mut req, &identity).await.amend(|e| {
                    e.set_uri(req.uri_string())
                        .set_identity(format!("{:?}", *identity))
                        .set_request_uuid(req.request_uuid().to_string())
                })
            }
            #[allow(clippy::match_single_binding, unused_variables, clippy::single_match, unused_mut)]
            async fn router_inner(
                app: &'static crate::AppStruct,
                req: &mut approck::server::Request,
                identity: &Arc<crate::IdentityStruct>,
            ) -> approck::Result<approck::server::response::Response> {

                // .socket check
                {
                    if req.path() == "/.socket" {
                        return approck::app_socket::app_socket_handler(app, req, identity).await;
                    }
                }

                // Static file check
                {
                    if let Some((content_type, file)) = STATIC_FILE_MAP.get(req.path()) {
                        return Ok(approck::server::response::bytes_content_type_long_cache(*file, content_type)?);
                    }
                }

                let path_vec = req.path_chunks();
                let mut path_parts = path_vec.into_iter();

                // this will be built up as we go
                let mut web_path = ::granite::WebPath::new("web".to_string());

                // setup the root menu
                let mut menu = ::approck::Menu::default();
                #menu_calls_tokens

                // setup the uuid map
                let uuid_map = ::granite::UuidMap::new();
                #codegen_router_inner_tokens
            }

            // Version information constant
            pub const VERSION_INFO: &str = include_str!(#version_file_str);
        };

        syn::parse2::<syn::File>(token_stream).expect("Failed to parse")
    }

    fn make_api_matches(&self) -> Vec<TokenStream> {
        self.iter_api_modules()
            .map(|api_module| {
                let api_name = &api_module.api_name;
                let syn_path = &api_module.syn_path;

                quote! {
                    #api_name => {
                        #syn_path::wrap(app, identity, input).await
                    }
                }
            })
            .collect()
    }

    fn codegen_router_inner(&self) -> TokenStream {
        // Helper function to recursively flatten the tree

        // RECURSIVE FUNCTION NOTES
        // This function operates on a single route, and is responsible for a complete match arm
        // for that route.
        //
        // The match was done on the next path part, which will be Some(String) or None if it is
        // the end of the path.
        //
        // Within the body of the match, it should have another match statement for
        // handling the children.
        // This inner match should
        // 1. check on None which means this was the actual route requested
        // 2. over each match arm of the children by recursing into this function.
        // 3. handle _ which is not found
        fn generate_arm(parent_path: &WebPath, route: &RouteTreeRoute) -> TokenStream {
            // Create the current path by appending this route's segment to parent path
            let current_path = {
                let mut p = parent_path.clone();
                p.push(route.path_segment.clone());
                p
            };

            // The final path segment has to be None in order to match a hanlder, and this should only
            // be checked in the event that there actually is a handler at this route.
            let none_arm_for_handler = match &route.request_handler {
                Some(RequestHandler::StaticFile(static_file_handler)) => {
                    let serve_tokens = serve_static_file(static_file_handler);
                    quote! {
                        None => {
                            #serve_tokens
                        }
                    }
                }
                Some(RequestHandler::Dynamic(dynamic_handler)) => {
                    let serve_tokens = serve_dynamic_handler(route, dynamic_handler);
                    quote! {
                        None => {
                            #serve_tokens
                        }
                    }
                }
                Some(RequestHandler::StaticContent(_static_content_handler)) => {
                    unimplemented!("Not yet")
                }
                Some(RequestHandler::Redirect(_redirect_handler)) => {
                    unimplemented!("Not yet")
                }
                None => {
                    quote! {}
                }
            };

            // Recursively process children.  Index doesn't have children, but it will return empty vec
            let child_arms: Vec<TokenStream> = route
                .children()
                .iter()
                .map(|child| generate_arm(&current_path, child))
                .collect();

            let web_path_segment_tokens = route.path_segment.to_tokens();
            let menu_calls_tokens = menu_calls(&route.self_menu_functions);

            let match_arm_inner = quote! {
                {
                    // Build up the current web path at each level
                    web_path.push(#web_path_segment_tokens);

                    // this will call menu.set_group(...) followed by each menu call for this level
                    #menu_calls_tokens

                    // Match on the next path, which of course has None in case it is the end
                    match path_parts.next() {
                        #none_arm_for_handler
                        #(#child_arms)*
                        _ =>{
                            Ok(approck::server::response::not_found())
                        }
                    }
                }
            };

            // This is actual route arm, and it depedns on the type of WebPathSegment
            match &route.path_segment {
                WebPathSegment::Index => {
                    quote! {
                        Some("") => #match_arm_inner
                    }
                }
                WebPathSegment::Literal(literal) => {
                    quote! {
                        Some(#literal) => #match_arm_inner
                    }
                }

                WebPathSegment::Capture { name, capture } => {
                    let capture_ident = granite_compiler::ident_str!(format!("capture_{}", name));
                    let (guard, guard_conversion) = get_guard(&capture_ident, capture);
                    quote! {
                        Some(#capture_ident) #guard => {
                            #guard_conversion
                            #match_arm_inner
                        }
                    }
                }
            }
        }

        let mut top_level_arms = Vec::new();

        // Process each top-level route
        for route in &self.get("web").unwrap().children() {
            // For top-level routes, start with an empty path
            let empty_path = WebPath::new("web".to_string());
            top_level_arms.push(generate_arm(&empty_path, route));
        }

        quote! {
            match &path_parts.next() {
                #(#top_level_arms)*
                _ => {
                    Ok(approck::server::response::not_found())
                }
            }
        }
    }
}

fn static_pfh<'a>(static_files: impl Iterator<Item = &'a RouteTreeStaticFile>) -> TokenStream {
    // Use phf_codegen to generate a map at compile time for static files
    let mut map_builder = phf_codegen::Map::new();

    for file in static_files {
        let uri = &file.uri;
        // Create a string representation of the include_bytes! expression
        let content_type = file.content_type;
        let abs_path = &file.abs_path.to_str().unwrap();
        let value_expression = quote! {
            (#content_type, include_bytes!(#abs_path))
        };
        map_builder.entry(uri, &value_expression.to_string());
    }

    // Build the map and convert to a string
    let map_code = map_builder.build().to_string();

    // parse map_code into tokens
    let map_literal = syn::parse_str::<syn::Expr>(&map_code).expect("Failed to parse map_code");

    // The map is: {uri: (content_type, &[u8])}
    quote! {
        static STATIC_FILE_MAP: ::approck::phf::Map<&'static str, (&'static str, &'static [u8])> =
            ::approck #map_literal;
    }
}

fn serve_static_file(static_file_handler: &StaticFileHandler) -> TokenStream {
    let abs_path = &static_file_handler.abs_path.to_string_lossy().to_string();

    #[allow(clippy::manual_unwrap_or_default)]
    let ext = match abs_path.rsplit_once('.') {
        Some((_, ext)) => ext,
        None => "",
    };

    match ext {
        "map" => {
            quote! {
                let f = include_str!(#abs_path);
                Ok(approck::server::response::json(f))
            }
        }
        "js" => {
            quote! {
                let f = include_str!(#abs_path);
                Ok(approck::server::response::javascript(f))
            }
        }
        "css" => {
            quote! {
                let f = include_str!(#abs_path);
                Ok(approck::server::response::css(f))
            }
        }
        _ => {
            quote! {
                let f = include_bytes!(#abs_path);
                Ok(approck::server::response::bytes(f))
            }
        }
    }
}

fn serve_dynamic_handler(
    _route: &RouteTreeRoute,
    dynamic_handler: &DynamicHandler,
) -> proc_macro2::TokenStream {
    let wrapper_syn_path = &dynamic_handler.syn_path_to_wrap_fn;
    let capture_arg_names = &dynamic_handler.capture_arg_names;

    // This block of code handles having a document, and presenting errors in said document
    // A menu is needed because there is a document.
    if let Some(document) = &dynamic_handler.document {
        let document_ident = quote::format_ident!("{}Struct", document.ident);

        let mut document_tokens = Vec::new();

        // Used for error and handler
        if let Some(js_bundle_uri) = &document.js_bundle_uri {
            document_tokens.push(quote! {
                document.set_document_js_bundle_uri(#js_bundle_uri);
            });
        }

        // Used for error and handler
        if let Some(css_bundle_uri) = &document.css_bundle_uri {
            document_tokens.push(quote! {
                document.set_document_css_bundle_uri(#css_bundle_uri);
            });
        }

        // Grab a snapshot o fthe tokens for the error handling
        let document_error_tokens = document_tokens.clone();

        // Only used for handler
        if let Some(js_bundle_uri) = &dynamic_handler.js_bundle_uri {
            document_tokens.push(quote! {
                document.set_handler_js_bundle_uri(#js_bundle_uri);
            });
        }

        // Only used for handler
        if let Some(css_bundle_uri) = &dynamic_handler.css_bundle_uri {
            document_tokens.push(quote! {
                document.set_handler_css_bundle_uri(#css_bundle_uri);
            });
        }

        let make_handler_document_tokens = quote! {
            // Braces needed because multiple expressions
            {
                use bux::document::Base;
                let mut document = crate::#document_ident::new(app, identity, req);
                document.menu_replace(menu.clone());
                #(#document_tokens)*
                document
            }
        };

        let make_error_document_tokens = quote! {
            // Braces needed because multiple expressions
            {
                use bux::document::Base;
                let mut document = crate::#document_ident::new(app, identity, req);
                document.menu_replace(menu.clone());
                #(#document_error_tokens)*
                document
            }
        };

        quote! {
            let document = #make_handler_document_tokens;
            let result = #wrapper_syn_path(
                app,
                req,
                identity,
                #(#capture_arg_names,)* //no extra trailing comma
                document
            ).await;
            let result = result.map_err(|e| {
                let mut document = #make_error_document_tokens;
                e.set_http_response_renderer(Box::new(move |error| {
                    bux::document::Base::add_error(&mut document, error);
                    let content = bux::document::Base::render(&document).into_string();
                    granite::HTTPResponse::new_500(content)
                }))
            });
            result
        }
    }
    // This block of code handles not having a document or trying to present errors in a special way
    // No menu is needed if there is no document.
    else {
        quote! {
            #wrapper_syn_path(
                app,
                req,
                identity,
                #(#capture_arg_names,)* //no extra trailing comma
            ).await // no semicolon
        }
    }
}

// produce tokens to call the menu functions (should be called after set_group())
fn menu_calls(menu_functions: &[MenuFunction]) -> TokenStream {
    if menu_functions.is_empty() {
        return quote! {};
    }

    let menu_calls_vec: Vec<TokenStream> = menu_functions
        .iter()
        .map(|menu_function| {
            let syn_path = &menu_function.syn_path;
            let params: Vec<TokenStream> = menu_function
                .params
                .0
                .iter()
                .map(|param| match &param.param_type {
                    MenuParamType::App => quote! { app },
                    MenuParamType::Capture(capture) => {
                        let capture_ident = format_ident!("capture_{}", &param.param_name);
                        match &capture {
                            WebPathCapture::String => quote! { #capture_ident.clone() },
                            _ => quote! { #capture_ident },
                        }
                    }
                    MenuParamType::Identity => quote! { identity },
                    MenuParamType::Menu => quote! { menu.group_proxy(web_path.fragment()) },
                    MenuParamType::UuidMap => quote! { &uuid_map },
                })
                .collect();

            // Specific calling code for the menu functions along with required params
            quote! {
                #syn_path(#(#params),*);
            }
        })
        .collect();

    quote! {
        #(#menu_calls_vec)*
    }
}

fn get_guard(capture_ident: &Ident, capture: &WebPathCapture) -> (TokenStream, TokenStream) {
    let (guard, guard_conversion) = match capture {
        // Strings don't have a guard
        WebPathCapture::String => (
            quote! { if !#capture_ident.is_empty() },
            quote! { let #capture_ident = #capture_ident.to_string(); },
        ),

        // Integers need cast into their respective types
        WebPathCapture::i8 => (
            quote! { if #capture_ident.parse::<i8>().is_ok() },
            quote! { let #capture_ident = #capture_ident.parse::<i8>()?; },
        ),
        WebPathCapture::u8 => (
            quote! { if #capture_ident.parse::<u8>().is_ok() },
            quote! { let #capture_ident = #capture_ident.parse::<u8>()?; },
        ),
        WebPathCapture::i32 => (
            quote! { if #capture_ident.parse::<i32>().is_ok() },
            quote! { let #capture_ident = #capture_ident.parse::<i32>()?; },
        ),
        WebPathCapture::u32 => (
            quote! { if #capture_ident.parse::<u32>().is_ok() },
            quote! { let #capture_ident = #capture_ident.parse::<u32>()?; },
        ),
        WebPathCapture::i64 => (
            quote! { if #capture_ident.parse::<i64>().is_ok() },
            quote! { let #capture_ident = #capture_ident.parse::<i64>()?; },
        ),
        WebPathCapture::u64 => (
            quote! { if #capture_ident.parse::<u64>().is_ok() },
            quote! { let #capture_ident = #capture_ident.parse::<u64>()?; },
        ),
        WebPathCapture::usize => (
            quote! { if #capture_ident.parse::<usize>().is_ok() },
            quote! { let #capture_ident = #capture_ident.parse::<usize>()?; },
        ),
        WebPathCapture::Uuid => (
            quote! { if #capture_ident.parse::<granite::Uuid>().is_ok() },
            quote! { let #capture_ident = #capture_ident.parse::<granite::Uuid>()?; },
        ),
    };

    (guard, guard_conversion)
}
