pub mod node_tree;
pub mod route_tree;

use std::path::PathBuf;

pub use node_tree::{Node, NodeTree};
pub use route_tree::{
    AuthFunction, DynamicHandler, DynamicHandlerDocument, MenuFunction, RequestHandler, RouteExt,
    RouteTree, RouteTreeApiModule, RouteTreeRoute, RouteTreeStaticFile, RouteTreeWebDir,
    StaticContentHandler, StaticFileHandler,
};

use granite_compiler::syn_path_push_ident;

pub fn into_route_tree(node_tree: NodeTree) -> RouteTree {
    fn recurse_routes(node_tree: &NodeTree, node: &Node) -> RouteTreeRoute {
        let mut route = RouteTreeRoute::new(node.path_segment().clone());

        // add the request handler if any
        let static_files = node.static_files();
        let http_modules = node.http_modules();
        match (static_files.len(), http_modules.len()) {
            // Neither handler found
            (0, 0) => {}

            // Only 1 static file found
            (1, 0) => {
                let static_file = static_files[0];
                route.set_request_handler(RequestHandler::StaticFile(StaticFileHandler {
                    abs_path: static_file.abs_path.clone(),
                }));
            }
            // Only 1 dynamic handler found
            (0, 1) => {
                let http_module = http_modules[0];

                let (js_bundle_uri, css_bundle_uri) = {
                    match node_tree.get_esbuild_output(&PathBuf::from(&http_module.rel_path)) {
                        Some(es_build_output) => (
                            es_build_output.js_bundle_uri(),
                            es_build_output.css_bundle_uri(),
                        ),
                        None => (None, None),
                    }
                };

                // If a `document_ident` is present, then calculate the document information
                let document = http_module.document_ident().map(|document_ident| {
                    let document_rel_path = http_module
                        .crate_rel_path
                        .join("src/web")
                        .join(format!("{}.rs", document_ident));

                    let (document_js_bundle_uri, document_css_bundle_uri) = {
                        match node_tree.get_esbuild_output(&document_rel_path) {
                            Some(es_build_output) => (
                                es_build_output.js_bundle_uri(),
                                es_build_output.css_bundle_uri(),
                            ),
                            None => (None, None),
                        }
                    };

                    DynamicHandlerDocument {
                        ident: document_ident.clone(),
                        rel_path: document_rel_path,
                        js_bundle_uri: document_js_bundle_uri,
                        css_bundle_uri: document_css_bundle_uri,
                    }
                });

                route.set_request_handler(RequestHandler::Dynamic(DynamicHandler {
                    rel_path: http_module.rel_path.clone(),
                    js_bundle_uri,
                    css_bundle_uri,
                    line_number: http_module.line_number,
                    syn_path_to_wrap_fn: http_module.syn_path_to_wrap_fn(),
                    capture_arg_names: http_module.capture_arg_names(),
                    document,
                }));
            }
            // Anything else is an error
            _ => {
                let e = static_files
                    .iter()
                    .map(|static_file| format!("static file: {}", static_file))
                    .chain(
                        http_modules
                            .iter()
                            .map(|http_module| format!("{}", http_module)),
                    )
                    .collect::<Vec<_>>()
                    .join(", ");

                panic!(
                    "conflicting request handlers on path {}: {:?}",
                    node.path_segment(),
                    e
                );
            }
        }

        // process the lineage prefix modules
        // THIS IS THE FULL LIST OF FUNCTIONS TO BE CALLED FOR ALL LEVELS OF THIS ROUTE
        for prefix_module in node_tree.lineage_prefix_modules(node) {
            if let Some(auth_fn) = prefix_module.auth_fn() {
                route.append_lineage_auth_function(AuthFunction {
                    web_path: prefix_module.web_path.clone(),
                    rel_path: prefix_module.rel_path.clone(),
                    line_number: auth_fn.line_number,
                    syn_path: syn_path_push_ident(
                        prefix_module.syn_path.clone(),
                        auth_fn.ident.clone(),
                    ),
                    params: auth_fn.params.clone(),
                });
            }

            if let Some(menu_fn) = prefix_module.menu_fn() {
                route.append_lineage_menu_function(MenuFunction {
                    web_path: prefix_module.web_path.clone(),
                    rel_path: prefix_module.rel_path.clone(),
                    line_number: menu_fn.line_number,
                    syn_path: syn_path_push_ident(
                        prefix_module.syn_path.clone(),
                        menu_fn.ident.clone(),
                    ),
                    params: menu_fn.params.clone(),
                });
            }
        }

        // process the prefix modules
        // THIS IS THE FUNCTIONS TO BE CALLED AT THIS LEVEL OF THE ROUTE
        for prefix_module in node_tree.self_prefix_modules(node) {
            if let Some(auth_fn) = prefix_module.auth_fn() {
                route.append_self_auth_function(AuthFunction {
                    web_path: prefix_module.web_path.clone(),
                    rel_path: prefix_module.rel_path.clone(),
                    line_number: auth_fn.line_number,
                    syn_path: syn_path_push_ident(
                        prefix_module.syn_path.clone(),
                        auth_fn.ident.clone(),
                    ),
                    params: auth_fn.params.clone(),
                });
            }

            if let Some(menu_fn) = prefix_module.menu_fn() {
                route.append_self_menu_function(MenuFunction {
                    web_path: prefix_module.web_path.clone(),
                    rel_path: prefix_module.rel_path.clone(),
                    line_number: menu_fn.line_number,
                    syn_path: syn_path_push_ident(
                        prefix_module.syn_path.clone(),
                        menu_fn.ident.clone(),
                    ),
                    params: menu_fn.params.clone(),
                });
            }
        }

        // add the children
        for child in node_tree.children(node) {
            route.append_route(recurse_routes(node_tree, child));
        }

        // critical to deliver routes that are fully sorted per the Ord rules
        route.sort();

        route
    }

    // iterate over the web dirs, then inside there, iterate
    let mut route_tree = RouteTree::new(
        // ARG1: iterator of RoutTreeStaticFiles
        node_tree.iter_static_files().map(|(uri, abs_path)| {
            let extension = match uri.rsplit_once('.') {
                Some((_, ext)) => ext,
                None => "",
            };

            RouteTreeStaticFile {
                uri: uri.to_string(),
                abs_path: abs_path.clone(),
                content_type: crate::extension_to_content_type(extension),
            }
        }),
        // ARG2: iterator of RouteTreeWebDirs
        node_tree.iter_web_dirs().map(|web_dir_node| {
            // Create the new RouteTreeWebDir by recursing into the children
            let mut route_tree_web_dir = RouteTreeWebDir::new(
                web_dir_node.web_dir().to_string(),
                node_tree
                    .children(web_dir_node)
                    .into_iter()
                    .map(|child_node| recurse_routes(&node_tree, child_node)),
            );

            // Append any self auth and menu functions
            for prefix_module in node_tree.self_prefix_modules(web_dir_node) {
                if let Some(auth_fn) = prefix_module.auth_fn() {
                    route_tree_web_dir.append_self_auth_function(AuthFunction {
                        web_path: prefix_module.web_path.clone(),
                        rel_path: prefix_module.rel_path.clone(),
                        line_number: auth_fn.line_number,
                        syn_path: syn_path_push_ident(
                            prefix_module.syn_path.clone(),
                            auth_fn.ident.clone(),
                        ),
                        params: auth_fn.params.clone(),
                    });
                }

                if let Some(menu_fn) = prefix_module.menu_fn() {
                    route_tree_web_dir.append_self_menu_function(MenuFunction {
                        web_path: prefix_module.web_path.clone(),
                        rel_path: prefix_module.rel_path.clone(),
                        line_number: menu_fn.line_number,
                        syn_path: syn_path_push_ident(
                            prefix_module.syn_path.clone(),
                            menu_fn.ident.clone(),
                        ),
                        params: menu_fn.params.clone(),
                    });
                }
            }

            route_tree_web_dir
        }),
        // ARG3: iterator of RouteTreeApiModules
        node_tree
            .iter_api_modules()
            .map(|api_module| RouteTreeApiModule {
                rel_path: api_module.rel_path.clone(),
                line_number: api_module.line_number,
                api_name: api_module.api_name.clone(),
                syn_path: api_module.syn_path.clone(),
            }),
    );

    // critical to deliver routes that are fully sorted per the Ord rules
    route_tree.sort();

    // create the route tree
    route_tree
}
