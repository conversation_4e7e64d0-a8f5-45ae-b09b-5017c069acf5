pub mod codegen;
pub mod macro_request_line;
pub mod macro_return_types;
pub mod post_form_struct;
pub mod request_function_return;
pub mod request_params;

#[cfg(test)]
mod tests;

use std::path::PathBuf;

use crate::QueryString;
use granite::{WebPath, WebPathCapture, WebPathFragment, WebPathSegment};
use granite_compiler::tokenator::{Token, TokenError, TokenIter};
use itertools::Itertools;
use quote::ToTokens;
use syn::{Visibility::Public, spanned::Spanned};

/// This represents an [approck:http()] function in-place in a web project
/// It knows where the code is, and owns the inner function struct
#[allow(clippy::manual_non_exhaustive)]
pub struct HttpModule {
    pub rel_path: String,
    pub crate_rel_path: PathBuf,
    pub web_path: WebPath,
    pub script_path: String,
    pub line_number: usize,
    pub syn_path: syn::Path,
    pub inner: HttpModuleInner,

    #[allow(dead_code)]
    _private: (), // Private field to prevent direct construction
}

impl HttpModule {
    pub fn new(
        rel_path: String,
        crate_rel_path: PathBuf,
        web_path: WebPath,
        script_path: String,
        line_number: usize,
        inner: HttpModuleInner,
        syn_path: syn::Path,
    ) -> Result<Self, crate::CompileError> {
        use itertools::Position::{Last, Only};
        // validate that script path matches the web path

        fn l2p(s: &str) -> String {
            s.replace(['-', '.'], "_")
        }

        let zipper = script_path.split('/').zip(web_path.iter()).with_position();

        let calculated_script_path = zipper
            .map(|(position, (_script_segment, web_path_segment))| {
                match (web_path_segment, position) {
                    (WebPathSegment::Literal(literal), Last | Only) => {
                        format!("{}.rs", l2p(literal))
                    }
                    (WebPathSegment::Literal(literal), _) => l2p(literal),
                    (WebPathSegment::Capture { .. }, _) => "mapper".to_string(),
                    (WebPathSegment::Index, _) => "index.rs".to_string(),
                }
            })
            .join("/");

        if script_path != calculated_script_path {
            let mut error = crate::CompileError::default();
            error.path_match_error(&web_path, &rel_path, &script_path, &calculated_script_path);
            println!("{}", error);
            //return Err(error);
        }

        Ok(Self {
            rel_path,
            crate_rel_path,
            web_path,
            script_path,
            line_number,
            inner,
            syn_path,
            _private: (),
        })
    }
}

/// This is the whole parsed contents of the approck_http attribute that comes
/// from the ... part of #[approck::http(...)]
///
/// It is not aware of where it exists in a web project because when called from a
/// proc_macro that info is not available
#[derive(Clone)]
pub struct HttpModuleInner {
    pub methods: Vec<Method>,
    pub web_path_fragment: WebPathFragment,
    pub query_string: Option<QueryString>,
    pub auth_type: AuthType,
    pub derive_debug: bool,
    pub return_types: ReturnTypes,
    pub mod_attrs: Vec<syn::Attribute>,
    pub mod_name: String,
    pub mod_ident: syn::Ident,
    pub mod_post_type: PostType,
    pub mod_request_fn: syn::ItemFn,
    pub mod_request_fn_params: self::request_params::RequestParams,
    pub mod_request_fn_return: RequestFunctionReturnType,
    pub mod_items_remaining: Vec<syn::Item>,
}

impl HttpModule {
    /// Returns the names of the capture arguments prefixed with `capture_``
    pub fn capture_arg_names(&self) -> Vec<syn::Ident> {
        self.inner
            .iter_path_captures()
            .map(|(_level, name, _capture)| {
                granite_compiler::ident_str!(format!("capture_{}", name))
            })
            .collect()
    }

    /// get the name of the document ident
    pub fn document_ident(&self) -> Option<syn::Ident> {
        for param in &self.inner.mod_request_fn_params.0 {
            if let self::request_params::RequestParamType::Document(ident_string) =
                &param.param_type
            {
                return Some(granite_compiler::ident_str!(ident_string));
            }
        }
        None
    }

    pub fn syn_path_to_wrap_fn(&self) -> syn::Path {
        granite_compiler::syn_path_push_str(self.syn_path.clone(), "wrap")
    }
}

impl std::fmt::Display for HttpModule {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}:{}", self.rel_path, self.line_number)
    }
}

impl HttpModuleInner {
    pub fn has_path_captures(&self) -> bool {
        self.web_path_fragment
            .iter()
            .any(|p| matches!(p, WebPathSegment::Capture { .. }))
    }
    pub fn iter_path_captures(&self) -> impl Iterator<Item = (usize, String, &WebPathCapture)> {
        self.web_path_fragment
            .iter()
            .enumerate()
            .filter_map(|(i, p)| match &p {
                WebPathSegment::Capture { name, capture } => Some((i + 1, name.clone(), capture)),
                _ => None,
            })
    }
}

#[derive(Debug, PartialEq, Clone)]
pub enum Method {
    GET,
    POST,
    PUT,
    DELETE,
    PATCH,
    ANY,
}

#[derive(Debug, Clone)]
#[allow(non_snake_case)]
pub enum AuthType {
    None,
    IdentityMethod(String),
}

#[derive(Debug, Default, PartialEq, Clone)]
#[allow(non_snake_case)]
pub struct ReturnTypes {
    pub Bytes: bool,
    pub Text: bool,
    pub Empty: bool,
    pub HTML: bool,
    pub JavaScript: bool,
    pub CSS: bool,
    pub JSON: bool,
    pub SVG: bool,
    pub NotFound: bool,
    pub Redirect: bool,
    pub WebSocketUpgrade: bool,
    pub Stream: bool,
}

#[derive(Debug, PartialEq, Clone)]
pub enum RequestFunctionReturnType {
    ResultResponse,
    Response,
}

#[derive(Clone)]
pub enum PostType {
    None,
    PostFormStruct(PostFormStruct),
    PostJsonStruct(syn::ItemStruct),
    PostJsonEnum(syn::ItemEnum),
    PostJsonType(syn::ItemType),
}

#[derive(Clone)]
pub struct PostFormStruct {
    token_stream: proc_macro2::TokenStream,
    query_string_parts: Vec<crate::QueryStringPart>,
}

impl std::fmt::Debug for PostType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PostType::None => write!(f, "None"),
            PostType::PostFormStruct(_) => write!(f, "struct PostForm(...)"),
            PostType::PostJsonStruct(_) => write!(f, "struct PostJson {{}}"),
            PostType::PostJsonEnum(_) => write!(f, "enum PostJson {{}}"),
            PostType::PostJsonType(_) => write!(f, "type PostJson = ..."),
        }
    }
}

impl PostType {
    pub fn is_filled(&self) -> bool {
        !matches!(self, PostType::None)
    }
}

/// Represents the request line of the attribute macro
/// request line comes first and ends with either a semicolon or the end of the attribute
///
/// Examples:
///   1.  #[approck::http]
///   2.  #[approck::http(GET .)]
///   3.  #[approck::http(GET|POST .)]
///   4.  #[approck::http(POST /index;)]
///
/// Note that #1 becomes #2
/// If the path is ommitted, it is assumed to be .

#[derive(Debug, Clone)]
struct RequestLine {
    methods: Vec<Method>,
    web_path_fragment: WebPathFragment,
    qs: Option<crate::QueryString>,
}

#[allow(unused_macros)]
macro_rules! make_ident {
    ($name:expr) => {
        syn::Ident::new($name, proc_macro2::Span::call_site())
    };
}

/// Example of macro
///
/// ```text
/// #[approck::http(
///    GET|POST /foo/bar/{id:i32}/?baz=String;
///    AUTH None;
///    return String;
/// )]
/// ```
pub fn parse_module_inner(
    input: proc_macro2::TokenStream,
    item_mod: syn::ItemMod,
) -> Result<crate::HttpModuleInner, TokenError> {
    // Convert TokenStream to an iterator
    let mut token_iter = TokenIter::new(input);

    // This parses the request line, somthing like this.
    //   GET|POST /foo/bar/{id:i32}/?baz=String;
    // The token iter will be moved past the semicolon
    let request_line = self::macro_request_line::parse(&mut token_iter)?;

    // Initialize the auth type
    let mut auth_type: Option<AuthType> = None;

    // Set defaults
    let mut derive_debug = false;
    let mut return_types = None;

    // parse additional instructions
    // TODO: let's make return the last thing a requirement
    loop {
        match token_iter.token() {
            Token::Ident(ident) => match ident.to_string().as_str() {
                "AUTH" => {
                    if auth_type.is_some() {
                        return Err(token_iter.error("duplicate `AUTH` instruction"));
                    }
                    token_iter.step();

                    match token_iter.get_ident_as_string()?.as_str() {
                        "None" => {
                            auth_type = Some(AuthType::None);
                        }
                        ident => {
                            auth_type = Some(AuthType::IdentityMethod(ident.to_string()));
                        }
                    }
                    token_iter.step();

                    token_iter.take_semicolon()?;
                }

                "derive_debug" => {
                    if derive_debug {
                        return Err(token_iter.error("duplicate `derive_debug` instruction"));
                    }
                    derive_debug = true;
                    token_iter.step();

                    token_iter.take_semicolon()?;
                }
                "return" => {
                    if return_types.is_some() {
                        return Err(token_iter.error("duplicate `return` instruction"));
                    }
                    return_types = Some(self::macro_return_types::parse(&mut token_iter)?);
                }
                _ => {
                    return Err(
                        token_iter.error(format!("invalid instruction: `{}`", ident).as_str())
                    );
                }
            },
            Token::End => {
                break;
            }
            _ => {
                return Err(token_iter.error("expected `derive_debug`, or `return`"));
            }
        }
    }

    // auth_type is required
    let auth_type = match auth_type {
        Some(auth_type) => auth_type,
        None => {
            return Err(token_iter.error("missing `AUTH` instruction"));
        }
    };

    // read the return section
    let return_types = match return_types {
        Some(return_types) => return_types,
        None => {
            return Err(token_iter.error("missing `return` instruction"));
        }
    };

    // read the end
    token_iter.get_end()?;

    // done with this for now
    drop(token_iter);

    // ---- Now move on to parsing elements of the module itself ----

    // Get the module ident, name, and span (under the #[approck::http])
    let mod_attrs = item_mod.attrs.clone();
    let mod_ident = item_mod.ident.clone();
    let mod_name = mod_ident.to_string();
    let mod_span = item_mod.span();

    // Steal the items out of the module, because that is what will be used to re-assemble it
    // along with other pre and post content later
    let mod_items = match item_mod.content {
        Some((_, items)) => items,
        _ => {
            return Err(TokenError::new(mod_span, "module has no content"));
        }
    };

    // Define optional vars for any well-known items that may be found
    let mut mod_request_fn = None;
    let mut mod_post_type = PostType::None;
    let mut mod_items_remaining = Vec::new();

    // Extract any well-named items
    for item in mod_items.into_iter() {
        match item {
            syn::Item::Fn(item_fn) if item_fn.sig.ident == "request" => {
                mod_request_fn = Some(item_fn);
            }

            // PostForm for struct
            syn::Item::Struct(item_struct) if item_struct.ident == "PostForm" => {
                if mod_post_type.is_filled() {
                    return Err(TokenError::new(
                        item_struct.span(),
                        format!("PostType already set to {:?}", mod_post_type).as_str(),
                    ));
                }
                mod_post_type =
                    PostType::PostFormStruct(self::post_form_struct::parse(item_struct)?);
            }

            // PostJson for enum
            syn::Item::Enum(item_enum) if item_enum.ident == "PostJson" => {
                if mod_post_type.is_filled() {
                    return Err(TokenError::new(
                        item_enum.span(),
                        format!("PostType already set to {:?}", mod_post_type).as_str(),
                    ));
                }
                mod_post_type = PostType::PostJsonEnum(item_enum);
            }

            // PostJson for struct
            syn::Item::Struct(item_struct) if item_struct.ident == "PostJson" => {
                if mod_post_type.is_filled() {
                    return Err(TokenError::new(
                        item_struct.span(),
                        format!("PostType already set to {:?}", mod_post_type).as_str(),
                    ));
                }
                mod_post_type = PostType::PostJsonStruct(item_struct);
            }

            // PostJson for type
            syn::Item::Type(item_type) if item_type.ident == "PostJson" => {
                if mod_post_type.is_filled() {
                    return Err(TokenError::new(
                        item_type.span(),
                        format!("PostType already set to {:?}", mod_post_type).as_str(),
                    ));
                }
                mod_post_type = PostType::PostJsonType(item_type);
            }

            // Any other public function is not allowed
            syn::Item::Fn(item_fn) if matches!(item_fn.vis, Public(_)) => {
                return Err(TokenError::new(
                    item_fn.span(),
                    "the only public functions allowed in this module are [request]",
                ));
            }

            // otherwise, just keep it
            item => {
                mod_items_remaining.push(item);
            }
        }
    }

    // Validate request function
    let mod_request_fn = match mod_request_fn {
        Some(mod_request_fn) => {
            if mod_request_fn.sig.asyncness.is_none() {
                return Err(TokenError::new(
                    mod_request_fn.span(),
                    "function must be async",
                ));
            }
            mod_request_fn
        }
        None => {
            return Err(TokenError::new(
                mod_span,
                "module has no `request` function",
            ));
        }
    };

    let mod_request_fn_params =
        self::request_params::parse(mod_request_fn.sig.inputs.to_token_stream())?;

    let mod_request_fn_return =
        self::request_function_return::parse(mod_request_fn.sig.output.to_token_stream())?;

    Ok(crate::HttpModuleInner {
        methods: request_line.methods,
        web_path_fragment: request_line.web_path_fragment,
        query_string: request_line.qs,
        auth_type,
        derive_debug,
        return_types,
        mod_attrs,
        mod_ident,
        mod_name,
        mod_post_type,
        mod_request_fn,
        mod_items_remaining,
        mod_request_fn_params,
        mod_request_fn_return,
    })
}
