
:root {
    /* site logo */
    --cliffy-pnav-logo-width: 175px;
    --cliffy-pnav-logo-height: auto;

    /* primary navigation */
    --cliffy-pnav-bg-color: #333;
    --cliffy-pnav-menu-item-margin: 0 0 0.5rem 0;

    --cliffy-pnav-a-bg-color: transparent;
    --cliffy-pnav-a-hover-bg-color: rgb(255, 255, 255);
    --cliffy-pnav-a-bg-image: none;
    --cliffy-pnav-a-hover-bg-image: none;
    --cliffy-pnav-a-color: rgb(255, 255, 255);
    --cliffy-pnav-a-hover-color: #333;
    --cliffy-pnav-a-weight: normal;
    --cliffy-pnav-a-lh: inherit;
    --cliffy-pnav-a-padding: 0.5rem;
    --cliffy-pnav-a-border: 1px solid #fff;
    --cliffy-pnav-a-br: 10px;

    /* hierarchical navigation */
    --pnav-a-color: #fff;
    --pnav-a-hover-color: #000;
    --pnav-a-selected-color: #fff;
    --pnav-group-name-top-border: #707070;
    --pnav-group-name-hover-top-border: #707070;
    --pnav-group-name-selected-top-border: #707070;
    --pnav-group-name-bottom-border: #707070;
    --pnav-group-name-hover-bottom-border: #707070;
    --pnav-group-name-selected-bottom-border: #707070;
    --pnav-a-hover-bg-color: #fff;
    --pnav-a-hover-bg-image: none;
    --pnav-a-selected-bg-color: #707070;
    --pnav-a-selected-bg-image: none;

    /* split button */
    --cliffy-splitbtn-a-color: #fff;
    --cliffy-splitbtn-a-bg-color: #707070;
    --cliffy-splitbtn-a-border-color: #707070;
    --cliffy-splitbtn-btn-color: #fff;
    --cliffy-splitbtn-btn-bg-color: #4e4e4e;
    --cliffy-splitbtn-btn-border-color: #4e4e4e;

    /* breadcrumb */
    --cliffy-breadcrumb-a-color: #fff;
    --cliffy-breadcrumb-separator-color: #fff;
}

/*********************************************************/
/* Adjust the layout to accomodate the sidebar */

layout-wrapper-outer.cliffy {

    layout-wrapper-inner {
        display: flex;
        flex-direction: column;

        > cliffy-content-wrapper {
            display: flex;
            flex-direction: column;
            flex: 1 0 auto;
            row-gap: 1rem;

            @media (min-width: 992px) {
                flex-direction: row;
                row-gap: 0;
            }
        }
    }
}

/*********************************************************/

layout-wrapper-outer.cliffy {

    layout-wrapper-inner {

        > cliffy-content-wrapper {

            /* Default padding for content container */
            content-container {
                padding-top: 1rem;
                padding-bottom: 1rem;
            }
        }
    }
}

/* Apply these styles if cliffy-content-wrapper has the fixed class */
layout-wrapper-outer.cliffy {
    
    layout-wrapper-inner:has( > cliffy-content-wrapper.fixed) {
        height: 100vh;

        > cliffy-content-wrapper.fixed {
            overflow-y: auto;
            overflow-x: auto;
            flex-shrink: 1;
        }
    }

    /* Hide the footer if cliffy-content-wrapper has the fixed class */
    &:has( > layout-wrapper-inner > cliffy-content-wrapper.fixed) {
        
        footer {
            display: none;
        }
    }
}

/*********************************************************/
/* Sidebar */

.cliffy sidebar {
    display: block;
    padding: 1rem;
    background-color: #f1f3f6;
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.125);
    margin: 0 1rem;

    @media (min-width: 992px) {
        width: 320px;
    }
}

/*********************************************************/
/* These styles control the nav-header display */

.cliffy nav-header#horizontal-nav-header {
    /* Hide at the large (992px) breakpoint */
    @media (min-width: 992px) {
        display: none;
    }
}

.cliffy nav-header#vertical-nav-header {
    @media (min-width: 992px) {
        display: block; 
    }
}

/*********************************************************/
/* Go Back Button */

.cliffy nav-wrapper {
    split-button {
        display: inline-flex;

        > a {
            display: inline-block;
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            border: 1px solid;
            color: var(--cliffy-splitbtn-a-color);
            background-color: var(--cliffy-splitbtn-a-bg-color);
            border-color: var(--cliffy-splitbtn-a-border-color);
            padding: .375rem .75rem;
            font-size: 1rem;
            border-radius: .375rem 0 0 .375rem;
            transition: background-color .15s ease-in-out, border-color .15s ease-in-out;
            white-space: nowrap;
            text-decoration: none;

            &:hover {
                color: #fff;
            }
        }

        div.dropdown-wrapper {
            position: relative;

            button.dropdown-menu-toggle {
                border-radius: 0 .375rem .375rem 0;
                color: var(--cliffy-splitbtn-btn-color);
                background-color: var(--cliffy-splitbtn-btn-bg-color);
                border-color: var(--cliffy-splitbtn-btn-border-color);
        
                &::after {
                    content: "\f078";
                    font-family: "Font Awesome 5 Free";
                    font-weight: 900; /* Font weight for solid Font Awesome icons */
                }

                &:focus {
                    box-shadow: none;
                    outline: revert;
                }
            }
        
            ul {
                display: none;
                background-color: #fff;
                border: 1px solid #00000026;
                border-radius: .25rem;
                padding: .5rem 0;
                text-align: left;
                list-style-type: none;
                position: absolute;
                z-index: 1000;
                left: 0;
                right: auto;
                top: 100%;
                min-width: 18rem;
        
                li {
        
                    a {
                        text-decoration: none;
                        padding: .5rem 1rem;
                        color: #212529;
                        font-size: 1rem;
                        font-weight: 500;
                        display: block;
        
                        &:hover {
                            color: #1e2125;
                            background-color: #e9ecef;
                        }
                    }
                }
        
                &.show {
                    display: block;
                }
            }
        }
    }
}

/*********************************************************/
/* Breadcrumbs */

.cliffy nav.breadcrumb {
    margin-bottom: 0;

    ol {
        padding-left: 0;
        margin-bottom: 0;
        display: flex;
        flex-wrap: wrap;
        list-style-type: none;
        column-gap: 0.5rem;
        /* Add baseline alignment */
        align-items: baseline;

        li {
            a {
                color: var(--cliffy-breadcrumb-a-color);
            }

            &.active {
                color: rgba(33, 37, 41, 0.75);
            }
            
            /* Style for last breadcrumb item */
            &:last-child a {
                font-weight: bold;
                font-size: 1.2em;
                font-weight: bold;
            }
        }

        li+li:before {
            content: "/";
            padding-right: 0.5rem;
            color: var(--cliffy-breadcrumb-separator-color);
        }
    }
}

/*********************************************************/
/* Base styles for the primary/secondary navigation */

.cliffy nav-wrapper {

    content-container {

        @media (min-width: 992px) {
            > nav#primary-navigation {
                /*
                Revert to default grid placement behavior in 
                order to accommodate the addition of the Go Back button 
                */
                grid-area: auto;
            }
        
            padding-left: 0;
        }
    }
}

/*********************************************************/
/* Styles specific to the vertical navigation */

@media (min-width: 992px){

    layout-wrapper-outer.cliffy {
        margin-left: 250px;
    }
    
    .cliffy nav#primary-navigation {

        menu-wrapper-outer {
            display: block;
            position: fixed;
            z-index: 1000;
            top: 0;
            bottom: 0;
            left: 0;

            menu-wrapper-inner {
                display: block;
                padding: 1rem 0;
                overflow-y: auto;
                height: 100%;
                width: 250px;
                background-color: var(--cliffy-pnav-bg-color);

                nav-header {
                    text-align: center;
                    margin: 1rem 0.5rem 2rem 0.5rem;

                    a {
                        img {
                            width: var(--cliffy-pnav-logo-width);
                            height: var(--cliffy-pnav-logo-height);
                        }
                    }
                }

                ul.nav-menu {
                    margin: 0 0.5rem;

                    li.menu-item,
                    li.menu-item-dropdown {
                        display: block;

                        a.menu-link,
                        a.dropdown-menu-toggle {
                            background-color: var(--cliffy-pnav-a-bg-color);
                            background-image: var(--cliffy-pnav-a-bg-image);
                            padding: var(--cliffy-pnav-a-padding);
                            border: var(--cliffy-pnav-a-border);
                            border-radius: var(--cliffy-pnav-a-br);
                            color: var(--cliffy-pnav-a-color);
                            font-weight: var(--cliffy-pnav-a-weight);
                            line-height: var(--cliffy-pnav-a-lh);

                            &:hover {
                                background-color: var(--cliffy-pnav-a-hover-bg-color);
                                background-image: var(--cliffy-pnav-a-hover-bg-image);
                                color: var(--cliffy-pnav-a-hover-color);
                            }
                        }

                        &:not(:last-child) {
                            margin: var(--cliffy-pnav-menu-item-margin);
                        }
                    }

                    li.menu-item-dropdown {

                        a.dropdown-menu-toggle {
                            display: flex;
                            align-items: center;
                            column-gap: 0.5rem;

                            &::after {
                                margin-left: auto;
                                transform: rotate(-90deg);
                                display: inline-block; /* Allows the transformation to work properly */
                            }
                        }
        
                        ul.dropdown-menu-list {
                            position: fixed;
                            left: 250px;
                            top: 0;
                            bottom: 0;
                            overflow-y: auto;
                            width: 400px;
                            background-color: #fff;
                            box-shadow: 4px 0 15px -2px rgba(0, 0, 0, 0.2);
                            border: 0;
                            border-radius: 0;
                            padding: 0 0 1rem 0;

                            li.dropdown-menu-item {
                                border-bottom: 1px solid #6c757d;

                                &:has(> dropdown-header) {
                                    display: list-item;
                                }
                                
                                dropdown-header {
                                    display: flex;
                                    align-items: center;
                                    column-gap: 0.5rem;
                                    padding: 1rem;
                                    background-color: #ececec;
                                    color: #212515;

                                    h5 {
                                        margin: 0;
                                        margin-right: auto;
                                    }

                                    > a {
                                        padding: 0;
                                        color: rgb(33, 37, 41);
                                        font-size: 1rem;
                                        display: block;
                                        text-decoration: none;
                                    } 
                                }

                                a.dropdown-menu-link, span {
                                    padding: 0.75rem 1rem;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/*********************************************************/
/* Base styles for the hierarchical navigation */

.cliffy nav#primary-navigation {
    ul.nav-menu {
        padding-left: 0;
        list-style-type: none;
        margin: 0 0.5rem;

        li.menu-group {

            a {
                color: var(--pnav-a-color);
                text-decoration: none;
                display: block;

                small {
                    display: block;
                    font-weight: normal;
                }

                &.group-name {
                    font-weight: bold;
                    margin-bottom: 0.5rem;
                    padding: .375rem .75rem;
                    border-top: 1px solid var(--pnav-group-name-top-border);
                    border-bottom: 1px solid var(--pnav-group-name-bottom-border);

                    &:hover {
                        border-top: 1px solid var(--pnav-group-name-hover-top-border);
                        border-bottom: 1px solid var(--pnav-group-name-hover-bottom-border);
                    }

                    &.selected {
                        border-top: 1px solid var(--pnav-group-name-selected-top-border);
                        border-bottom: 1px solid var(--pnav-group-name-selected-bottom-border);
                    }
                }

                &:hover {
                    background-color: var(--pnav-a-hover-bg-color);
                    background-image: var(--pnav-a-hover-bg-image);
                    border-radius: .375rem;
                    color: var(--pnav-a-hover-color);
                }

                &.selected {
                    color: var(--pnav-a-selected-color);
                    background-color: var(--pnav-a-selected-bg-color);
                    background-image: var(--pnav-a-selected-bg-image);
                    border-radius: .375rem;
                    font-weight: bold;
                }
            }

            ul {
                margin: 0 0 0.5rem 0;
                padding-left: 0;
                list-style-type: none;

                li {

                    a {
                        padding: .375rem .75rem;
                    }
                }
            }
        }
    }
}

/*********************************************************/
