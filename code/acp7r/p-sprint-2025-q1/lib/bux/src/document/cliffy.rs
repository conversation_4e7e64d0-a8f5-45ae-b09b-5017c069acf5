use maud::html;

#[derive(Default)]
pub struct CliffyData {
    owner: Option<String>,
}

pub trait Cliffy:
    super::Base + super::Nav1 + super::Nav2 + super::PageNav + super::FooterSocial + super::FooterLinks
{
    fn data(&self) -> &CliffyData;
    fn data_mut(&mut self) -> &mut CliffyData;
    fn set_owner(&mut self, owner: &str) {
        Cliffy::data_mut(self).owner = Some(owner.to_string());
    }

    fn render_content_wrapper(&self) -> maud::Markup {
        use super::{Base, BodyDisplay};
        use maud::html;

        match Base::data(self).body_display {
            BodyDisplay::Fluid => html! {
                cliffy-content-wrapper {
                    content-container.fluid {
                        (Base::render_body_inner(self))
                    }
                    @if !self.is_sidebar_empty() {
                        sidebar {
                            (Base::render_sidebar_inner(self))
                        }
                    }
                }
            },
            BodyDisplay::Container => html! {
                cliffy-content-wrapper {
                    content-container {
                        (Base::render_body_inner(self))
                    }
                    @if !self.is_sidebar_empty() {
                        sidebar {
                            (Base::render_sidebar_inner(self))
                        }
                    }
                }
            },
            BodyDisplay::Plain => html! {
                cliffy-content-wrapper.plain {
                    (Base::render_body_inner(self))
                }
            },
            BodyDisplay::Fixed => html! {
                cliffy-content-wrapper.fixed {
                    (Base::render_body_inner(self))
                }
            },
        }
    }

    fn render_breadcrumb(&self) -> maud::Markup {
        let menu_groups: Vec<_> = super::Base::menu(self).iter_for_render().collect();
        let first = 0;
        let last = menu_groups.len().saturating_sub(1);

        html! {
            nav.breadcrumb aria-label="breadcrumb" {
                ol {
                    @for (i, menu_group) in menu_groups.iter().enumerate() {
                        @if let Some(heading) = &menu_group.heading {
                            li {
                                // If the first item, give it the context of the name if it has one
                                @if i == first {
                                    a href=(heading.uri) {
                                        (heading.label)
                                        @if let Some(name) = &heading.name {
                                            " for " { (name) }
                                        }
                                    }
                                }
                                // If the last item, only give it the name if it has it, because
                                // context is provided by the previous item.
                                @else if i == last {
                                    a href=(heading.uri) {
                                        @if let Some(name) = &heading.name {
                                            (name)
                                        }
                                        @else {
                                            (heading.label)
                                        }
                                    }
                                }
                                // Otherwise, just give it the label
                                @else {
                                    a href=(heading.uri) { (heading.label) }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    fn render_go_back_button(&self) -> maud::Markup {
        html! {
            split-button {
                a #bux-back-button href="/" {
                    i.fas.fa-arrow-left {}
                    " Back"
                }
                div.dropdown-wrapper {
                    button.dropdown-menu-toggle data-toggle="dropdown" aria-expanded="false" {}
                    ul.dropdown-menu-list #bux-back-menu {
                    }
                }
            }
        }
    }

    fn render_body(&self) -> maud::Markup {
        use super::{Base, FooterLinks, FooterSocial, Nav1, Nav2, PageNav};
        use maud::{PreEscaped, html};
        let owner = &Cliffy::data(self).owner;
        html!(
            layout-wrapper-outer.cliffy {
                layout-wrapper-inner {
                    @if !self.is_headerbar_empty() {
                        header-bar id="header-bar" {
                            (self.render_headerbar_inner())
                        }
                    }
                    nav-wrapper {
                        content-container.fluid {
                            (self.render_go_back_button())
                            (self.render_breadcrumb())
                            nav-header id="horizontal-nav-header" {
                                a href=(Base::logo_href(self)) aria-label=(format!("Home - {}", Base::site_name(self))) {
                                    img src=(Base::logo_src(self)) alt=(format!("{} Logo", Base::site_name(self))) width="100" height="50" {}
                                }
                            }
                            (Nav1::render_nav1(self))
                            (Nav2::render(self))
                        }
                    }
                    (PageNav::render(self))
                    (self.render_content_wrapper())
                }
                footer {
                    @if !self.is_footer_social_empty() {
                        (FooterSocial::render(self))
                    }
                    @if !self.is_footer_links_empty() {
                        (FooterLinks::render(self))
                    }
                    @if let Some(owner)=owner {
                        p id="footer-copyright" {
                            small {
                                "Copyright"
                                (PreEscaped(" &copy; "))
                                (granite::current_year())
                                " "
                                (owner)
                                ". All rights reserved."
                            }
                        }
                    }
                }
            }
        )
    }
}
