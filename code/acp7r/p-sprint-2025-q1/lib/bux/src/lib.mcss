/*********************************************************/
/* Reboot CSS borrowed from Bootstrap */
/*********************************************************/

/* Change from `box-sizing: content-box` so that `width` is not affected by `padding` or `border`. */

*, *::before, *::after {
    box-sizing: border-box;
}

/* 
    Body

    1. Remove the margin in all browsers.
    2. As a best practice, apply a default `background-color`.
    3. Prevent adjustments of font size after orientation changes in iOS.
    4. Change the default tap highlight to be completely transparent in iOS.
*/

body {
    margin: 0; /* 1 */
    font-family: 
        system-ui,
        -apple-system,
        "Segoe UI",
        Roboto,
        "Helvetica Neue",
        "Noto Sans",
        "Liberation Sans",
        <PERSON><PERSON>,
        sans-serif,
        "Apple Color Emoji",
        "Segoe UI Emoji",
        "Segoe UI Symbol",
        "Noto Color Emoji";
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    /*text-align: ;*/
    background-color: #fff; /* 2 */
    -webkit-text-size-adjust: 100%; /* 3 */
    -webkit-tap-highlight-color: transparent; /* 4 */
}

/* 
    Content grouping

    1. Reset Firefox's gray color
*/

hr {
    margin: 1rem 0;
    color: inherit; /* 1 */
    border: 0;
    border-top: 1px solid rgb(33, 37, 41);
    opacity: .25;
}

/*
    Typography

    1. Remove top margins from headings
    By default, `<h1>`-`<h6>` all receive top and bottom margins. We nuke the top
    margin for easier control within type scales as it avoids margin collapsing.
*/

h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    margin-bottom: .5rem;
    font-weight: 500;
    line-height: 1.2;
    color: inherit;
}

h1 {
    font-size: calc(1.375rem + 1.5vw);

    @media (min-width: 1200px) {
        font-size: 2.5rem;
    }
}

h2 {
    font-size: calc(1.325rem + .9vw);

    @media (min-width: 1200px) {
        font-size: 2rem;
    }
}

h3 {
    font-size: calc(1.3rem + .6vw);

    @media (min-width: 1200px) {
        font-size: 1.75rem;
    }
}

h4 {
    font-size: calc(1.275rem + .3vw);

    @media (min-width: 1200px) {
        font-size: 1.5rem;
    }
}

h5 {
    font-size: 1.25rem;
}

h6 {
    font-size: 1rem;
}

/* 
    Reset margins on paragraphs

    Similarly, the top margin on `<p>`s get reset. However, we also reset the
    bottom margin to use `rem` units instead of `em`.
*/

p {
    margin-top: 0;
    margin-bottom: 1rem;
}

/*
    Abbreviations

    1. Add the correct text decoration in Chrome, Edge, Opera, and Safari.
    2. Add explicit cursor to indicate changed behavior.
    3. Prevent the text-decoration to be skipped.
*/

abbr[title] {
    text-decoration: underline dotted; /* 1 */
    cursor: help; /* 2 */
    text-decoration-skip-ink: none; /* 3 */
}

/* Address */

address {
    margin-bottom: 1rem;
    font-style: normal;
    line-height: inherit;
}

/* Lists */

ol, ul {
    padding-left: 2rem;
}

ol, ul, dl {
    margin-top: 0;
    margin-bottom: 1rem;
}

ol ol, ul ul, ol ul, ul ol {
    margin-bottom: 0;
}

dt {
    font-weight: 700;
}

/* 1. Undo browser default */

dd {
    margin-bottom: .5rem;
    margin-left: 0; /* 1 */
}

/* Blockquote */

blockquote {
    margin: 0 0 1rem;
}

/* 
    Strong

    Add the correct font weight in Chrome, Edge, and Safari
*/

b, strong {
    font-weight: bolder;
}

/* 
    Small

    Add the correct font size in all browsers
*/

small {
    font-size: .875em;
}

/* Mark */

mark {
    padding: .1875em;
    color: #000;
    background-color: #fff3cd;
}

/* 
    Sub and Sup

    Prevent `sub` and `sup` elements from affecting the line height in
    all browsers.
*/

sub, sup {
    position: relative;
    font-size: .75em;
    line-height: 0;
    vertical-align: baseline;
}

sub { 
    bottom: -.25em; 
}

sup { 
    top: -.5em; 
}

/* Links */

a {
    color: rgb(13, 110, 253);
    opacity: 1;
    text-decoration: underline;

    &:hover {
        color: rgb(10, 88, 202);
    }
}

/*
    And undo these styles for placeholder links/named anchors (without href).
    It would be more straightforward to just use a[href] in previous block, but that
    causes specificity issues in many other styles that are too complex to fix.
    See https://github.com/twbs/bootstrap/issues/19402
*/

a:not([href]):not([class]) {
    &,
    &:hover {
        color: inherit;
        text-decoration: none;
    }
}

/* Code */

pre, code, kbd, samp {
    font-family:
        SFMono-Regular,
        Menlo,
        Monaco,
        Consolas,
        "Liberation Mono",
        "Courier New",monospace;
    font-size: 1em; /* Correct the odd `em` font sizing in all browsers. */
}

/* 
    1. Remove browser default top margin
    2. Reset browser default of `1em` to use `rem`s
    3. Don't allow content to break outside
*/

pre {
    display: block;
    margin-top: 0; /* 1 */
    margin-bottom: 1rem; /* 2 */
    overflow: auto; /* 3 */
    font-size: .875em;

    /* Account for some code outputs that place code tags in pre tags */
    code {
        font-size: inherit;
        color: inherit;
        word-break: normal;
    }
}

code {
    font-size: .875em;
    color: #d63384;
    word-wrap: break-word;

    /* Streamline the style when inside anchors to avoid broken underline and more */
    a > & {
        color: inherit;
    }
}

kbd {
    padding: .1875rem .375rem;
    font-size: .875em;
    color: #fff;
    background-color: #212529;
    border-radius: .25rem;

    kbd {
        padding: 0;
        font-size: 1em;
    }
}

/* 
    Figures

    Apply a consistent margin strategy (matches our type styles).
*/

figure {
    margin: 0 0 1rem;
}

/* Images and content */

img, svg {
    vertical-align: middle;
}

/* 
    Tables

    Prevent double borders
*/

table {
    caption-side: bottom;
    border-collapse: collapse;
}

caption {
    padding-top: .5rem;
    padding-bottom: .5rem;
    color: rgba(33, 37, 41, 0.75);
    text-align: left;
}

/* 
    1. Matches default `<td>` alignment by inheriting `text-align`.
    2. Fix alignment for Safari
*/

th {
    text-align: inherit; /* 1 */
    text-align: -webkit-match-parent; /* 2 */
}

thead, tbody, tfoot, tr, td, th {
    border-color: inherit;
    border-style: solid;
    border-width: 0;
}

/* 
    Forms

    1. Allow labels to use `margin` for spacing.
*/

label {
    display: inline-block; /* 1 */
}

/* 
    Remove the default `border-radius` that macOS Chrome adds.
    See https://github.com/twbs/bootstrap/issues/24093
*/

button {
    border-radius: 0;
}

/* 
    Explicitly remove focus outline in Chromium when it shouldn't be
    visible (e.g. as result of mouse click or touch tap). It already
    should be doing this automatically, but seems to currently be
    confused and applies its very visible two-tone outline anyway.
*/

button:focus:not(:focus-visible) {
    outline: 0;
}

/* 1. Remove the margin in Firefox and Safari */

input, button, select, optgroup, textarea {
    margin: 0; /* 1 */
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

/* Remove the inheritance of text transform in Firefox */

button, select {
    text-transform: none;
}

/*
    Set the cursor for non-`<button>` buttons

    Details at https://github.com/twbs/bootstrap/pull/30562
*/

[role="button"] {
    cursor: pointer;
}

select {
    /* 
    Remove the inheritance of word-wrap in Safari.
    See https://github.com/twbs/bootstrap/issues/24990
    */
    word-wrap: normal;

    /* Undo the opacity change from Chrome */
    &:disabled {
        opacity: 1;
    }
}

/* 
    Remove the dropdown arrow only from text type inputs built with datalists in Chrome.
    See https://stackoverflow.com/a/54997118
*/

[list]:not([type="date"]):not([type="datetime-local"]):not([type="month"]):not([type="week"]):not([type="time"])::-webkit-calendar-picker-indicator {
    display: none !important;
}

/* 
    1. Prevent a WebKit bug where (2) destroys native `audio` and `video`
    controls in Android 4.
    2. Correct the inability to style clickable types in iOS and Safari.
    3. Opinionated: add "hand" cursor to non-disabled button elements.
*/

button,
    [type="button"], /* 1 */
    [type="reset"],
    [type="submit"] {
    appearance: button; /* 2 */
    -webkit-appearance: button; /* 2 */

    &:not(:disabled) {
        cursor: pointer; /* 3 */
    }
}

/* Remove inner border and padding from Firefox, but don't restore the outline like Normalize. */

::-moz-focus-inner {
    padding: 0;
    border-style: none;
}

/* 1. Textareas should really only resize vertically so they don't break their (horizontal) containers. */

textarea {
    resize: vertical; /* 1 */
}

/*
    1. Browsers set a default `min-width: min-content;` on fieldsets,
    unlike e.g. `<div>`s, which have `min-width: 0;` by default.
    So we reset that to ensure fieldsets behave more like a standard block element.
    See https://github.com/twbs/bootstrap/issues/12359
    and https://html.spec.whatwg.org/multipage/#the-fieldset-and-legend-elements
    2. Reset the default outline behavior of fieldsets so they don't affect page layout.
*/

fieldset {
    min-width: 0; /* 1 */
    padding: 0; /* 2 */
    margin: 0; /* 2 */
    border: 0; /* 2 */
}

/*
    1. By using `float: left`, the legend will behave like a block element.
    This way the border of a fieldset wraps around the legend if present.
    2. Fix wrapping bug.
    See https://github.com/twbs/bootstrap/issues/29712
*/

legend {
    float: left; /* 1 */
    width: 100%;
    padding: 0;
    margin-bottom: .5rem;
    line-height: inherit;
    font-size: calc(1.275rem + .3vw);

    + * {
        clear: left; /* 2 */
    }
}

/* 
    Fix height of inputs with a type of datetime-local, date, month, week, or time
    See https://github.com/twbs/bootstrap/issues/18842
*/

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
    padding: 0;
}

::-webkit-inner-spin-button {
    height: auto;
}

/*
    1. This overrides the extra rounded corners on search inputs in iOS so that our
    `.form-control` class can properly style them. Note that this cannot simply
    be added to `.form-control` as it's not specific enough. For details, see
    https://github.com/twbs/bootstrap/issues/11586.
    2. Correct the outline style in Safari.
*/

[type="search"] {
    appearance: textfield; /* 1 */
    -webkit-appearance: textfield; /* 1 */
    outline-offset: -2px; /* 2 */
}

/*
    1. A few input types should stay LTR
    See https://rtlstyling.com/posts/rtl-styling#form-inputs
    2. RTL only output
    See https://rtlcss.com/learn/usage-guide/control-directives/#raw
*/

/* rtl:raw:
[type="tel"],
[type="url"],
[type="email"],
[type="number"] {
  direction: ltr;
}
*/

/* Remove the inner padding in Chrome and Safari on macOS. */

::-webkit-search-decoration {
    -webkit-appearance: none;
}

/* Remove padding around color pickers in webkit browsers */

::-webkit-color-swatch-wrapper {
    padding: 0;
}

/* 
    1. Inherit font family and line height for file input buttons
    2. Correct the inability to style clickable types in iOS and Safari.
*/

::file-selector-button {
    font: inherit; /* 1 */
    appearance: button; /* 2 */
    -webkit-appearance: button; /* 2 */
}

/* Correct element displays */

output {
    display: inline-block;
}

/* Remove border from iframe */

iframe {
    border: 0;
}

/* 
    Summary

    1. Add the correct display in all browsers
*/

summary {
    display: list-item; /* 1 */
    cursor: pointer;
}

/* 
    Progress

    Add the correct vertical alignment in Chrome, Firefox, and Opera.
*/

progress {
    vertical-align: baseline;
}

/* 
    Hidden attribute

    Always hide an element with the `hidden` HTML attribute.
*/

[hidden] {
    display: none !important;
}

/*********************************************************/
/* Grid containers */
/*********************************************************/

/* 2 columns */
grid-2 {
    display: grid;
    gap: 1rem;

    @media (min-width: 992px) {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 3 columns */
grid-3 {
    display: grid;
    gap: 1rem;

    @media (min-width: 992px) {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* 4 columns */
grid-4 {
    display: grid;
    gap: 1rem;

    @media (min-width: 992px) {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* 12 columns */
grid-12 {
    display: grid;
    gap: 1rem;

    @media (min-width: 992px) {
        grid-template-columns: repeat(12, 1fr);
    }
}

grid-2, grid-3, grid-4, grid-12 {
    @media (min-width: 992px) {
        /* Grid child spans 2 columns */
        cell-2 {
            grid-column: span 2;
        }
        /* Grid child spans 3 columns */
        cell-3 {
            grid-column: span 3;
        }
        /* Grid child spans 4 columns */
        cell-4 {
            grid-column: span 4;
        }
        /* Grid child spans 5 columns */
        cell-5 {
            grid-column: span 5;
        }
        /* Grid child spans 6 columns */
        cell-6 {
            grid-column: span 6;
        }
        /* Grid child spans 7 columns */
        cell-7 {
            grid-column: span 7;
        }
        /* Grid child spans 8 columns */
        cell-8 {
            grid-column: span 8;
        }
        /* Grid child spans 9 columns */
        cell-9 {
            grid-column: span 9;
        }
        /* Grid child spans 10 columns */
        cell-10 {
            grid-column: span 10;
        }
    }
}

/*********************************************************/
/* Flex containers */
/*********************************************************/

hbox {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    justify-content: space-between;

    &.wrap {
        flex-wrap: wrap;
    }
}

vbox {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/*********************************************************/
/* Labels */
/*********************************************************/

label-tag {
    display: inline-block;
    white-space: nowrap;
    padding: .35em .65em;
    font-size: .75em;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    border-radius: .25rem;

    &.default {
        background-color: #6c757d;
    }

    &.primary {
        background-color: #0d6efd;
    }

    &.success {
        background-color: #198754;
    }

    &.info {
        background-color: #0dcaf0;
        color: #212529;
    }

    &.warning {
        background-color: #ffc107;
        color: #212529;
    }

    &.danger {
        background-color: #dc3545;
    }
}

/*********************************************************/
/* Alerts */
/*********************************************************/

alert {
    display: block;
    padding: 1rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: .25rem;

    &.primary {
        color: #084298;
        background-color: #cfe2ff;
        border-color: #b6d4fe;
        
        a {
            color: #084298;
            font-weight: 600;
            text-decoration: underline;
        }
    }

    &.success {
        color: #0f5132;
        background-color: #d1e7dd;
        border-color: #badbcc;

        a {
            color: #0f5132;
            font-weight: 600;
            text-decoration: underline;
        }
    }

    &.danger {
        color: #842029;
        background-color: #f8d7da;
        border-color: #f5c2c7;

        a {
            color: #842029;
            font-weight: 600;
            text-decoration: underline;
        }
    }

    &.warning {
        color: #664d03;
        background-color: #fff3cd;
        border-color: #ffecb5;

        a {
            color: #664d03;
            font-weight: 600;
            text-decoration: underline;
        }
    }

    &.info {
        color: #055160;
        background-color: #cff4fc;
        border-color: #b6effb;

        a {
            color: #055160;
            font-weight: 600;
            text-decoration: underline;
        }
    }

    &.dismissable {
        display: flex;
        justify-content: space-between;
        gap: 1rem;

        button {
            padding: 0;
            background: transparent;
            border: 0;
            align-self: flex-start;
        }
    }

    &.primary.dismissable {
        button {
            i {
                color: #084298;
            }
        }
    }

    &.success.dismissable {
        button {
            i {
                color: #0f5132;
            }
        }
    }

    &.danger.dismissable {
        button {
            i {
                color: #842029;
            }
        }
    }

    &.warning.dismissable {
        button {
            i {
                color: #664d03;
            }
        }
    }

    &.info.dismissable {
        button {
            i {
                color: #055160;
            }
        }
    }
}

/*********************************************************/
/* Buttons and Button Tags */
/*********************************************************/

button {
    display: inline-block;
    color: #333;
    text-align: center;
    vertical-align: middle;
    background-color: #fff;
    border: 1px solid #ccc;
    padding: .375rem .75rem;
    font-size: 1rem;
    border-radius: .375rem;
    transition: background-color .15s ease-in-out, border-color .15s ease-in-out;

    &:hover {
        background-color: #e6e6e6;
        border-color: #adadad;
    }

    &:disabled {
        cursor: not-allowed;
        opacity: .65;

        &:hover {
            background-color: #fff;
            border-color: #ccc;
        }
    }

    &:focus {
        outline: 0;
        color: #333;
        background-color: #fff;
        border-color: #565e64;
        box-shadow: 0 0 0 .25rem rgba(130, 138, 145, .5);
    }

    /* Optional Sizes */

    &.xs {
        padding: .075rem .3rem;
        font-size: .75rem;
    }
    
    &.sm {
        padding: .25rem .5rem;
        font-size: .875rem;
    }

    &.lg {
        padding: .5rem 1rem;
        font-size: 1.25rem;
    }

    &.block {
        display: block;
        width: 100%;
    }

    /* Contextual Classes */

    &.primary {
        color: #fff;
        background-color: #0d6efd;
        border-color: #0d6efd;

        &:hover {
            background-color: #0a58ca;
            border-color: #0a58ca;

            &:disabled {
                background-color: #0d6efd;
                border-color: #0d6efd; 
            }
        }

        &:focus {
            outline: 0;
            color: #fff;
            background-color: #0b5ed7;
            border-color: #07377f;
            box-shadow: 0 0 0 .25rem rgba(49, 132, 253, .5);
        }
    }

    &.secondary {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;

        &:hover {
            background-color: #565e64;
            border-color: #565e64;

            &:disabled {
                background-color: #6c757d;
                border-color: #6c757d; 
            }
        }

        &:focus {
            outline: 0;
            color: #fff;
            background-color: #5c636a;
            border-color: #363b3f;
            box-shadow: 0 0 0 .25rem rgba(130, 138, 145, .5);
        }
    }

    &.success {
        color: #fff;
        background-color: #198754;
        border-color: #198754;

        &:hover {
            background-color: #146c43;
            border-color: #146c43;

            &:disabled {
                background-color: #198754;
                border-color: #198754;
            }
        }

        &:focus {
            outline: 0;
            color: #fff;
            background-color: #157347;
            border-color: #0d442a;
            box-shadow: 0 0 0 .25rem rgba(60, 153, 110, .5);
        }
    }

    &.danger {
        color: #fff;
        background-color: #dc3545;
        border-color: #dc3545;

        &:hover {
            background-color: #b02a37;
            border-color: #b02a37;

            &:disabled {
                background-color: #dc3545;
                border-color: #dc3545;
            }
        }

        &:focus {
            outline: 0;
            color: #fff;
            background-color: #bb2d3b;
            border-color: #6e1b23;
            box-shadow: 0 0 0 .25rem rgba(225, 83, 97, .5);
        }
    }

    &.warning {
        color: #000;
        background-color: #ffc107;
        border-color: #ffc107;

        &:hover {
            background-color: #ffcd39;
            border-color: #ffcd39;

            &:disabled {
                background-color: #ffc107;
                border-color: #ffc107;
            }
        }

        &:focus {
            outline: 0;
            color: #000;
            background-color: #ffca2c;
            border-color: #806104;
            box-shadow: 0 0 0 .25rem rgba(217, 164, 6, .5);
        }
    }

    &.info {
        color: #000;
        background-color: #0dcaf0;
        border-color: #0dcaf0;

        &:hover {
            background-color: #3dd5f3;
            border-color: #3dd5f3;

            &:disabled {
                background-color: #0dcaf0;
                border-color: #0dcaf0;
            }
        }

        &:focus {
            outline: 0;
            color: #000;
            background-color: #31d2f2;
            border-color: #076578;
            box-shadow: 0 0 0 .25rem rgba(11, 172, 204, .5);
        }
    }
}

a.btn {
    display: inline-block;
    color: #333;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    background-color: #fff;
    border: 1px solid #ccc;
    padding: .375rem .75rem;
    font-size: 1rem;
    border-radius: .375rem;
    transition: background-color .15s ease-in-out, border-color .15s ease-in-out;
    /* Is wrapping on buttons innards ever appropriate? */
    white-space: nowrap;
    text-decoration: none;

    &:hover {
        background-color: #e6e6e6;
        border-color: #adadad;
    }

    &.disabled {
        cursor: not-allowed;
        opacity: .65;
        background-color: #fff;
        border-color: #ccc;

        &:hover {
            background-color: #fff;
            border-color: #ccc;
        }
    }

    &:focus {
        outline: 0;
        color: #333;
        background-color: #fff;
        border-color: #565e64;
        box-shadow: 0 0 0 .25rem rgba(130, 138, 145, .5);
    }

    /* Optional Sizes */

    &.xs {
        padding: .075rem .3rem;
        font-size: .75rem;
    }
    
    &.sm {
        padding: .25rem .5rem;
        font-size: .875rem;
    }

    &.lg {
        padding: .5rem 1rem;
        font-size: 1.25rem;
    }

    &.block {
        display: block;
        width: 100%;
    }

    /* Contextual Classes */

    &.primary {
        color: #fff;
        background-color: #0d6efd;
        border-color: #0d6efd;

        &:hover {
            background-color: #0a58ca;
            border-color: #0a58ca;

            &.disabled {
                background-color: #0d6efd;
                border-color: #0d6efd;
            }
        }

        &:focus {
            outline: 0;
            color: #fff;
            background-color: #0b5ed7;
            border-color: #07377f;
            box-shadow: 0 0 0 .25rem rgba(49, 132, 253, .5);
        }
    }

    &.secondary {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;

        &:hover {
            background-color: #565e64;
            border-color: #565e64;

            &:disabled {
                background-color: #6c757d;
                border-color: #6c757d;
            }
        }

        &:focus {
            outline: 0;
            color: #fff;
            background-color: #5c636a;
            border-color: #363b3f;
            box-shadow: 0 0 0 .25rem rgba(130, 138, 145, .5);
        }
    }

    &.success {
        color: #fff;
        background-color: #198754;
        border-color: #198754;

        &:hover {
            background-color: #146c43;
            border-color: #146c43;

            &:disabled {
                background-color: #198754;
                border-color: #198754;
            }
        }

        &:focus {
            outline: 0;
            color: #fff;
            background-color: #157347;
            border-color: #0d442a;
            box-shadow: 0 0 0 .25rem rgba(60, 153, 110, .5);
        }
    }

    &.danger {
        color: #fff;
        background-color: #dc3545;
        border-color: #dc3545;

        &:hover {
            background-color: #b02a37;
            border-color: #b02a37;

            &:disabled {
                background-color: #dc3545;
                border-color: #dc3545;
            }
        }

        &:focus {
            outline: 0;
            color: #fff;
            background-color: #bb2d3b;
            border-color: #6e1b23;
            box-shadow: 0 0 0 .25rem rgba(225, 83, 97, .5);
        }
    }

    &.warning {
        color: #000;
        background-color: #ffc107;
        border-color: #ffc107;

        &:hover {
            background-color: #ffcd39;
            border-color: #ffcd39;

            &:disabled {
                background-color: #ffc107;
                border-color: #ffc107;
            }
        }

        &:focus {
            outline: 0;
            color: #000;
            background-color: #ffca2c;
            border-color: #806104;
            box-shadow: 0 0 0 .25rem rgba(217, 164, 6, .5);
        }
    }

    &.info {
        color: #000;
        background-color: #0dcaf0;
        border-color: #0dcaf0;

        &:hover {
            background-color: #3dd5f3;
            border-color: #3dd5f3;

            &:disabled {
                background-color: #0dcaf0;
                border-color: #0dcaf0;
            }
        }

        &:focus {
            outline: 0;
            color: #000;
            background-color: #31d2f2;
            border-color: #076578;
            box-shadow: 0 0 0 .25rem rgba(11, 172, 204, .5);
        }
    }
}

/*********************************************************/
/* Panels */
/*********************************************************/

panel {
    display: block;
    background-color: #fff;
    border-radius: .25rem;
    border: 1px solid rgba(0, 0, 0, .125);
    margin-bottom: 1rem;

    > header {
        padding: .5rem 1rem;
        background-color: #f7f7f7;
        border-bottom: 1px solid rgba(0, 0, 0, .125);
        border-radius: .25rem .25rem 0 0;

        h5 {
            margin: 0;
            font-weight: 500;
            font-size: 1.25rem;
        }
    }

    > content {
        display: block;
        padding: 1rem;
    }

    > footer {
        padding: .5rem 1rem;
        background-color: #f7f7f7;
        border-top: 1px solid rgba(0, 0, 0, .125);
        border-radius: 0 0 .25rem .25rem;
        margin: 0;
        text-align: center;
        display: flex;
        justify-content: center;
        gap: .25rem;
    }
}

/*********************************************************/
/* Text alignment */
/*********************************************************/

.text-left {
    text-align: left;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

/*********************************************************/
/* Width utility classes */
/*********************************************************/

.w-25 {
    width: 25%;
}

.w-50 {
    width: 50%;
}

.w-75 {
    width: 75%;
}

.w-100 {
    width: 100%;
}

.mw-100 {
    max-width: 100%;
}

/*********************************************************/
/* Set width containers */
/*********************************************************/

.bux-narrow-25 {
    width: 100%;

    @media (min-width: 992px) {
        width: 25%;
        margin: 0 auto;
    }
}

.bux-narrow-50 {
    width: 100%;

    @media (min-width: 992px) {
        width: 50%;
        margin: 0 auto;
    }
}

.bux-narrow-75 {
    width: 100%;

    @media (min-width: 992px) {
        width: 75%;
        margin: 0 auto;
    }
}

/*********************************************************/
/* Margin utility classes */
/*********************************************************/

.ms-1 {
    margin-left: .25rem;
}

.ms-2 {
    margin-left: .5rem;
}

.ms-3 {
    margin-left: 1rem;
}

.ms-4 {
    margin-left: 1.5rem;
}

.ms-5 {
    margin-left: 3rem;
}

.me-1 {
    margin-right: .25rem;
}

.me-2 {
    margin-right: .5rem;
}

.me-3 {
    margin-right: 1rem;
}

.me-4 {
    margin-right: 1.5rem;
}

.me-5 {
    margin-right: 3rem;
}

.mt-1 {
    margin-top: .25rem;
}

.mt-2 {
    margin-top: .5rem;
}

.mt-3 {
    margin-top: 1rem;
}

.mt-4 {
    margin-top: 1.5rem;
}

.mt-5 {
    margin-top: 3rem;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-1 {
    margin-bottom: .25rem;
}

.mb-2 {
    margin-bottom: .5rem;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.mb-5 {
    margin-bottom: 3rem;
}

/*********************************************************/
/* Basic styled table */
/*********************************************************/

table {
    width: 100%;
    border-color: #dee2e6;
    margin-bottom: 1rem;
    caption-side: bottom;
    border-collapse: collapse;

    > thead {
        vertical-align: bottom;
    }

    > :not(:last-child) > :last-child > * {
        border-bottom-color: #212529;
    }

    tbody, td, tfoot, th, thead, tr {
        border-color: inherit;
        border-style: solid;
        border-width: 0;
    }

    > :not(caption) > * > * {
        padding: .5rem .5rem;
        border-bottom-width: 1px;
    }
}

/*********************************************************/
/* Styled tables for List */
/*********************************************************/

table-wrapper.data-list {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    display: block;

    table {
        width: 100%;
        min-width: 600px; /* Ensures table doesn't shrink too much */
        background-color: #fff;
        border-radius: .25rem;
        border: 1px solid #00000020;

        thead {
            tr {
                th {
                    padding: .5rem;
                    border-bottom: 1px solid rgb(33, 37, 41);
                    font-weight: bold;
                    background-color: #f7f7f7;
                }

                th:last-child {
                    text-align: right;
                }
            }
        }

        tbody {
            tr {
                th, td {
                    padding: .5rem;
                    border-bottom: 1px solid #dee2e6;
                    vertical-align: top;
                }
            }

            th {
                font-weight: bold;
            }

            td:last-child {
                text-align: right;
            }
        }
    }

    /* Ensure proper scrolling behavior */
    @media (max-width: 991.98px) {
        display: block;
        overflow-x: auto;
    }
}

/*********************************************************/
/* Styled tables for Details Screen */
/*********************************************************/
table-wrapper.detail-list {
    table {
        width: 100%;
        border: 1px solid #00000020;
        background-color: #fff;
        border-radius: .25rem;
        
        thead {
            tr {
                th {
                    padding: .5rem;
                    border-bottom: 1px solid rgb(33, 37, 41);
                    font-weight: bold;
                }
            }
        }

        tbody {
            tr {
                th, td {
                    padding: .5rem;
                    border-bottom: 1px solid #dee2e6;
                    vertical-align: top;
                }
            }

            th {
                font-weight: bold;
            }
        }
    }
}

/*********************************************************/
/* List group */
/*********************************************************/

list-group {

    ul {
        padding-left: 0;
        margin-bottom: 0;
        list-style-type: none;
        border-radius: .25rem;

        li {
            padding: .5rem 1rem;
            background-color: #fff;
            border: 1px solid rgba(0, 0, 0, .125);

            &:first-child {
                border-top-left-radius: .25rem;
                border-top-right-radius: .25rem;
            }

            &:last-child {
                border-bottom-right-radius: .25rem;
                border-bottom-left-radius: .25rem;
            }
        }

        li + li {
            border-top-width: 0;
        }
    }

    ol {
        padding-left: 0;
        margin-bottom: 0;
        border-radius: .25rem;

        li {
            padding: .5rem 1rem;
            background-color: #fff;
            border: 1px solid rgba(0, 0, 0, .125);
            list-style-position: inside;

            &:first-child {
                border-top-left-radius: .25rem;
                border-top-right-radius: .25rem;
            }

            &:last-child {
                border-bottom-right-radius: .25rem;
                border-bottom-left-radius: .25rem;
            }
        }

        li + li {
            border-top-width: 0;
        }
    }

    &.flush {

        ul {
            border-radius: 0;

            li {
                border-width: 0 0 1px;

                &:last-child {
                    border-bottom-width: 0;
                }
            }
        }

        ol {
            border-radius: 0;

            li {
                border-width: 0 0 1px;

                &:last-child {
                    border-bottom-width: 0;
                }
            }
        }
    }

    &.action {

        ul {

            li {
                padding: 0;

                a {
                    text-decoration: none;
                    display: block; 
                    padding: .5rem 1rem;
                    color: #212529;

                    &:hover, &:focus {
                        background-color: #f8f9fa;
                        color: #495057;
                    }

                    &.active {
                        color: #fff;
                        background-color: #0d6efd;
                        border-color: #0d6efd;
                    }

                    &.disabled {
                        color: #6c757d;
                        pointer-events: none;
                        background-color: #fff;
                    }
                }

                &:first-child {

                    a {
                        border-top-left-radius: .25rem;
                        border-top-right-radius: .25rem;
                    }
                }
    
                &:last-child {

                    a {
                        border-bottom-right-radius: .25rem;
                        border-bottom-left-radius: .25rem;
                    }
                }
            }
        }
    }
}

/*********************************************************/
/* Styled unordered list horizontally */
/*********************************************************/

/* REVIEW styles */

bux-hr-list {
    ul {
        margin: 0;
        display: flex;
        list-style: none;
        padding: 0;
        border: 1px solid #ccc;

        li {
            flex: 1;
            text-align: center;
            border-right: 1px solid #ccc;
            background: #fff;
            padding: .2rem;
            font-weight: bold;
        }
    }
}

/*********************************************************/
/* Styled to center a small panel */
/*********************************************************/

/* REVIEW styles */

bux-action-panel {
    width: 50% !important;
    margin: 0 auto;
    display: block;
}

@media (max-width: 992px) {
    bux-action-panel {
        width: 100% !important;
        max-width: 100%;
    }
}

/*********************************************************/
/* Styled to keyword search */
/*********************************************************/

/* REVIEW styles */

input.bux-search {
    width: 30%;
    margin-bottom: 1rem;
    padding: .5rem 1rem;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    font-size: 1rem;
}

/*********************************************************/
/* Styled to header with space between */
/*********************************************************/

/* REVIEW styles */

bux-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: .5rem 1rem;
    background-color: #f7f7f7;
    border: 1px solid #00000020;
    border-radius: .25rem .25rem 0 0;
}

/*********************************************************/
/* Styled to center a small panel with border */
/*********************************************************/

/* REVIEW styles */

bux-border-panel {
    width: 50% !important;
    margin: 0 auto;
    display: block;
    border: 1px solid #00000020;
    padding: 1rem;
}

@media (max-width: 992px) {
    bux-border-panel {
        width: 100% !important;
        max-width: 100%;
    }
}

/*********************************************************/

