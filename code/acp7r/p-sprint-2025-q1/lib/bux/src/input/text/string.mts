import { type Option, type Result } from "@granite/lib.mts";
import BuxInputText from "./mod.mts";
import "./string.mcss";

class BuxInputTextString extends BuxInputText<string | undefined> {
    /// return a valid value or throw a new Error
    protected override parse(v: string): Result<string | undefined, string> {
        let value = v;

        if (!this.attr_notrim) {
            value = value.trim();
        }

        if (value === "") {
            return { Ok: undefined };
        }

        return { Ok: value };
    }

    protected override format(value: string | undefined): string {
        return value ?? "";
    }

    static override get observedAttributes(): string[] {
        return BuxInputText.observedAttributes.concat(["notrim"]);
    }

    get attr_notrim(): boolean {
        return this.hasAttribute("notrim");
    }

    public get value_option(): Option<string> {
        const result = this.get();
        if ("Ok" in result && result.Ok !== undefined) {
            return { Some: result.Ok };
        }
        return { None: true };
    }

    public set value_option(value: Option<string>) {
        this.value = value;
    }
}

globalThis.customElements.define("bux-input-text-string", BuxInputTextString);
export default BuxInputTextString;
