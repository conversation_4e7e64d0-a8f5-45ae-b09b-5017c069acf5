use crate::{Mark<PERSON>, html};

pub fn cur_name_label(name: &str, label: &str) -> Markup {
    html! {
        bux-input-text-password name=(name) {
            label { (label) }
            input type="password" placeholder="Enter your password" name=(name) autocomplete="current-password" {}
            button type="button" data-toggle="password" tabindex="-1" { "Show" }
        }
    }
}

/// Renders a password input with help text
pub fn cur_name_label_help(name: &str, label: &str, help: &str) -> Markup {
    html! {
        bux-input-text-password name=(name) help=(help) {
            label { (label) }
            input type="password" placeholder="Enter your password" name=(name) autocomplete="current-password" {}
            button type="button" data-toggle="password" tabindex="-1" { "Show" }
        }
    }
}

pub fn new_name_label(name: &str, label: &str) -> Markup {
    html! {
        bux-input-text-password name=(name) {
            label { (label) }
            input type="password" placeholder="Create a new password" name=(name) autocomplete="new-password" {}
            button type="button" data-toggle="password" tabindex="-1" { "Show" }
        }
    }
}

pub fn new_name_label_help(name: &str, label: &str, help: &str) -> Markup {
    html! {
        bux-input-text-password name=(name) help=(help) {
            label { (label) }
            input type="password" placeholder="Create a new password" name=(name) autocomplete="new-password" {}
            button type="button" data-toggle="password" tabindex="-1" { "Show" }
        }
    }
}
