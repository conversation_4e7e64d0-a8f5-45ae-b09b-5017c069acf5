use granite::Decimal;

use crate::{Markup, html};

pub fn name_value(name: &str, value: Option<Decimal>) -> Markup {
    let string_value = match value {
        Some(v) => v.to_string(),
        None => "".to_string(),
    };
    html! {
        bux-input-text-percentage name=(name) {
            input-group {
                input type="text" name=(name) value=(string_value) {}
                span class="percentage-symbol" { "%" }
            }
        }
    }
}

// Renders a percentage input with a % symbol on the right side.
pub fn percentage_input(name: &str, label: &str, value: Option<Decimal>) -> Markup {
    html! {
        bux-input-text-percentage name=(name) {
            label { (label) }
            input-group {
                input type="text" name=(name) value=(value.map(crate::format_decimal_us_2).unwrap_or_default()) {}
                span class="percentage-symbol" { "%" }
            }
        }
    }
}

/// Renders a percentage input with help text and a % symbol on the right.
pub fn percentage_input_with_help(
    name: &str,
    label: &str,
    value: Option<Decimal>,
    help: &str,
) -> Markup {
    html! {
        bux-input-text-percentage name=(name) help=(help) {
            label { (label) }
            input-group {
                input type="text" name=(name) value=(value.map(crate::format_decimal_us_2).unwrap_or_default()) {}
                span class="percentage-symbol" { "%" }
            }
        }
    }
}
