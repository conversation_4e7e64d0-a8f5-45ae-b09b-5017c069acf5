bux-component-verification-code {
    margin: 0 0 1rem 0;
    display: block;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: .5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    > .x-error-wrapper {
        margin: 0 0 1rem 0;
        font-size: 0.95rem;
        color: #dc3545;
        background-color: #f8d7da;
        border: 1px solid #dc3545;
        border-radius: .25rem;
        padding: 0.75rem;
        text-align: center;
        line-height: 1.4;
    }

    > .x-title {
        margin: 0 0 1rem 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #212529;
        text-align: center;
    }

    > .x-description {
        margin: 0 0 0.5rem 0;
        font-size: 0.95rem;
        color: #6c757d;
        text-align: center;
        line-height: 1.4;
    }

    > .x-target {
        margin: 0 0 1.5rem 0;
        font-size: 1rem;
        font-weight: 500;
        color: #495057;
        text-align: center;
        padding: 0.5rem;
        background-color: #e9ecef;
        border-radius: .25rem;
    }

    > .x-input-wrapper {
        margin: 0 0 1rem 0;
        text-align: center;

        > .x-code {
            padding: .75rem 1rem;
            width: 180px;
            text-align: center;
            display: inline-block;
            font-size: 1.5rem;
            font-weight: 600;
            color: #212529;
            background-color: #ffffff;
            border: 2px solid #ced4da;
            border-radius: .375rem;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        > .x-code:not(:placeholder-shown) {
            letter-spacing: 0.5rem;
        }

        > .x-code:focus {
            outline: 0;
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        > .x-code:invalid {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
    }

    > .x-success-wrapper {
        text-align: center;
        padding: 1rem;
        background-color: #d1edff;
        border: 1px solid #b6d7ff;
        border-radius: .375rem;
        margin: 0 0 1rem 0;

        > div {
            margin-bottom: 0.5rem;

            > .verified-checkmark {
                font-size: 2rem;
                color: #198754;
            }
        }

        > p {
            margin: 0;
            font-weight: 600;
            color: #198754;
            font-size: 1.1rem;
        }
    }

    > .x-resend-wrapper {
        margin: 0;
        text-align: center;
        font-size: 0.9rem;
        color: #6c757d;

        > a {
            color: #0d6efd;
            text-decoration: none;
            font-weight: 500;
        }

        > a:hover {
            color: #0a58ca;
            text-decoration: underline;
        }
    }
}
