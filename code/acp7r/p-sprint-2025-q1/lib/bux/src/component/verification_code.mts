import "./verification_code.mcss";
import { SE_nullable } from "@granite/lib.mts";

export type VerificationCodeEvent = {
    code: string;
    type: "Phone" | "Email";
};

export class BuxComponentVerificationCode extends HTMLElement {
    // Can function with input rendered or not rendered
    protected $input: HTMLInputElement | null = null;

    protected code_length: number;
    protected contact_type: "Phone" | "Email";

    constructor() {
        super();
        this.$input = SE_nullable(this, ".x-code");

        {
            const code_length = this.getAttribute("code-length");
            if (code_length === null) {
                throw new Error("Missing code-length attribute");
            }
            this.code_length = parseInt(code_length);
        }

        switch (this.getAttribute("contact-type")) {
            case "Phone":
                this.contact_type = "Phone";
                break;
            case "Email":
                this.contact_type = "Email";
                break;
            default:
                throw new Error(`Invalid contact type: ${this.getAttribute("contact-type")}`);
        }
    }

    connectedCallback(): void {
        if (this.$input) {
            this.$input.addEventListener("input", this.on_input.bind(this));
            this.$input.focus();
        }
    }

    disconnectedCallback(): void {
        if (this.$input) {
            this.$input.removeEventListener("input", this.on_input.bind(this));
        }
    }

    private on_input(): void {
        if (this.$input) {
            this.$input.value = this.$input.value.replace(/\D/g, "");

            if (this.$input.value.length === this.code_length) {
                // Dispatch custom event that bubbles up
                const event = new CustomEvent("on_code_entered", {
                    detail: {
                        code: this.$input.value,
                        type: this.contact_type,
                    },
                    bubbles: true,
                });
                this.dispatchEvent(event);
            }
        }
    }
}

globalThis.customElements.define("bux-component-verification-code", BuxComponentVerificationCode);
