import "./password_creator.mcss";
import "@crate/input/text/password.mts";
import { SEC } from "@granite/lib.mts";
import { BuxInputTextPassword } from "@crate/input/text/password.mts";
import { password_analysis } from "@granite/password.mts";

class BuxComponentPasswordCreator extends HTMLElement {
    private $password = SEC(BuxInputTextPassword, this, "bux-input-text-password[name=password]");
    private $confirm = SEC(
        BuxInputTextPassword,
        this,
        "bux-input-text-password[name=confirm_password]",
    );
    private $meter = SEC(HTMLMeterElement, this, "meter");
    private $status = SEC(HTMLOutputElement, this, "output");

    constructor() {
        super();
        this.refresh();
    }

    connectedCallback() {
        this.$password.addEventListener("input", () => this.refresh());
        this.$confirm.addEventListener("input", () => this.refresh());
        this.$confirm.addEventListener("change", () => this.validate_passwords());
    }

    disconnectedCallback() {
        this.$password.removeEventListener("input", () => this.refresh());
        this.$confirm.removeEventListener("input", () => this.refresh());
        this.$confirm.removeEventListener("change", () => this.validate_passwords());
    }

    // Update the meter and status
    private refresh() {
        const analysis = password_analysis(this.value ?? "");
        this.$meter.value = analysis.meter_value;
        this.$status.textContent = analysis.meter_text;

        // Clear any previous errors when user types
        this.$confirm.set_e(undefined);
    }

    // Public API
    get value(): string | undefined {
        return this.$password.value || undefined;
    }

    get confirm_value(): string | undefined {
        return this.$confirm.value || undefined;
    }

    get both_filled(): boolean {
        return !!(this.value && this.confirm_value);
    }

    get passwords_match(): boolean {
        if (!this.value || !this.confirm_value) {
            return false;
        }
        return this.value === this.confirm_value;
    }

    set_e(value: string | undefined) {
        this.$password.set_e(value);
    }

    set_confirm_e(value: string | undefined) {
        this.$confirm.set_e(value);
    }

    get acceptable(): boolean {
        const analysis = password_analysis(this.value ?? "");
        return analysis.acceptable && this.both_filled && this.passwords_match;
    }

    // Validate passwords match and show error if they don't
    validate_passwords(): boolean {
        if (!this.both_filled) {
            if (!this.value) {
                this.set_e("Password is required");
            }
            if (!this.confirm_value) {
                this.set_confirm_e("Please confirm your password");
            }
            return false;
        }

        if (!this.passwords_match) {
            this.set_confirm_e("Passwords do not match");
            return false;
        }

        // Clear any previous errors if validation passes
        this.set_confirm_e(undefined);
        return true;
    }
}

customElements.define("bux-component-password-creator", BuxComponentPasswordCreator);
export default BuxComponentPasswordCreator;
