use maud::{<PERSON><PERSON>, <PERSON><PERSON>, html};
use std::borrow::Cow;

pub struct InsightDeck<'a> {
    pub heading: &'a str,
    pub description: Option<&'a str>,
    pub buttons: Vec<Markup>,
    pub tiles: Vec<InsightTile<'a>>,
    pub rows: Vec<InsightRow<'a>>,
    pub css_classes: Vec<&'a str>,
}

pub struct InsightTile<'a> {
    pub fa_icon_class: &'a str,
    pub title: &'a str,
    pub value: Markup,
    pub edit_href: Option<Cow<'a, str>>,
    pub view_href: Option<&'a str>,
}

pub struct InsightRow<'a> {
    pub fa_icon_class: &'a str,
    pub label: &'a str,
    pub value: Markup,
    pub edit_href: Option<Cow<'a, str>>,
    pub view_href: Option<&'a str>,
}

impl<'a> InsightDeck<'a> {
    pub fn new(heading: &'a str) -> Self {
        InsightDeck {
            heading,
            description: None,
            buttons: Vec::new(),
            tiles: Vec::new(),
            rows: Vec::new(),
            css_classes: Vec::new(),
        }
    }

    pub fn description(&mut self, description: &'a str) {
        self.description = Some(description);
    }

    pub fn add_css_class(&mut self, css_class: &'a str) {
        self.css_classes.push(css_class);
    }

    pub fn add_button(&mut self, button: Markup) {
        self.buttons.push(button);
    }

    pub fn add_basic_tile(
        &mut self,
        fa_icon_class: &'a str,
        title: &'a str,
        text: impl Into<Markup>,
    ) {
        self.tiles.push(InsightTile {
            fa_icon_class,
            title,
            value: text.into(),
            edit_href: None,
            view_href: None,
        });
    }

    pub fn add_edit_tile(
        &mut self,
        fa_icon_class: &'a str,
        title: &'a str,
        text: impl Into<Markup>,
        edit_href: impl Into<Cow<'a, str>>,
    ) {
        self.tiles.push(InsightTile {
            fa_icon_class,
            title,
            value: text.into(),
            edit_href: Some(edit_href.into()),
            view_href: None,
        });
    }

    pub fn add_view_tile(
        &mut self,
        fa_icon_class: &'a str,
        title: &'a str,
        text: impl Into<Markup>,
        view_href: &'a str,
    ) {
        self.tiles.push(InsightTile {
            fa_icon_class,
            title,
            value: text.into(),
            edit_href: None,
            view_href: Some(view_href),
        });
    }

    pub fn add_edit_view_tile(
        &mut self,
        fa_icon_class: &'a str,
        title: &'a str,
        text: impl Into<Markup>,
        edit_href: impl Into<Cow<'a, str>>,
        view_href: &'a str,
    ) {
        self.tiles.push(InsightTile {
            fa_icon_class,
            title,
            value: text.into(),
            edit_href: Some(edit_href.into()),
            view_href: Some(view_href),
        });
    }

    pub fn add_basic_row(&mut self, fa_icon_class: &'a str, label: &'a str, value: Markup) {
        self.rows.push(InsightRow {
            fa_icon_class,
            label,
            value,
            edit_href: None,
            view_href: None,
        });
    }

    pub fn add_edit_row(
        &mut self,
        fa_icon_class: &'a str,
        label: &'a str,
        value: impl Into<Markup>,
        edit_href: impl Into<Cow<'a, str>>,
    ) {
        self.rows.push(InsightRow {
            fa_icon_class,
            label,
            value: value.into(),
            edit_href: Some(edit_href.into()),
            view_href: None,
        });
    }

    pub fn add_view_row(
        &mut self,
        fa_icon_class: &'a str,
        label: &'a str,
        value: impl Into<Markup>,
        view_href: &'a str,
    ) {
        self.rows.push(InsightRow {
            fa_icon_class,
            label,
            value: value.into(),
            edit_href: None,
            view_href: Some(view_href),
        });
    }

    pub fn add_edit_view_row(
        &mut self,
        fa_icon_class: &'a str,
        label: &'a str,
        value: impl Into<Markup>,
        edit_href: impl Into<Cow<'a, str>>,
        view_href: &'a str,
    ) {
        self.rows.push(InsightRow {
            fa_icon_class,
            label,
            value: value.into(),
            edit_href: Some(edit_href.into()),
            view_href: Some(view_href),
        });
    }

    // Shortcut methods for common edit row patterns

    /// Add an email edit row with standard icon and label.
    /// Uses "fas fa-envelope-open-text" icon and "Email" label.
    /// Displays "None" if email is None.
    pub fn add_edit_row_email(&mut self, email: Option<&str>, edit_href: impl Into<Cow<'a, str>>) {
        use maud::html;
        let value = match email {
            Some(email) => html!((email)),
            None => html!("None"),
        };
        self.add_edit_row("fas fa-envelope-open-text", "Email", value, edit_href);
    }

    /// Add a phone edit row with standard icon and label.
    /// Uses "fas fa-phone" icon and "Phone" label.
    /// Displays "None" if phone is None.
    pub fn add_edit_row_phone(&mut self, phone: Option<&str>, edit_href: impl Into<Cow<'a, str>>) {
        use maud::html;
        let value = match phone {
            Some(phone) => html!((phone)),
            None => html!("None"),
        };
        self.add_edit_row("fas fa-phone", "Phone", value, edit_href);
    }

    /// Add a name edit row with standard icon and label.
    /// Uses "fas fa-user" icon and "Name" label.
    /// Displays "None" if name is None.
    pub fn add_edit_row_name(&mut self, name: Option<&str>, edit_href: impl Into<Cow<'a, str>>) {
        use maud::html;
        let value = match name {
            Some(name) => html!((name)),
            None => html!("None"),
        };
        self.add_edit_row("fas fa-user", "Name", value, edit_href);
    }

    /// Add an address edit row with standard icon and label.
    /// Uses "fas fa-map-marker-alt" icon and "Address" label.
    /// Displays "None" if address is None.
    pub fn add_edit_row_address(
        &mut self,
        address: Option<&str>,
        edit_href: impl Into<Cow<'a, str>>,
    ) {
        use maud::html;
        let value = match address {
            Some(address) => html!((address)),
            None => html!("None"),
        };
        self.add_edit_row("fas fa-map-marker-alt", "Address", value, edit_href);
    }
}

impl<'a> Render for InsightDeck<'a> {
    fn render(&self) -> Markup {
        let classes = if self.css_classes.is_empty() {
            "insight-deck".to_string()
        } else {
            format!("insight-deck {}", self.css_classes.join(" "))
        };

        html! {
            panel class=(classes) {
                header {
                    div {
                        h5 { (self.heading) }
                        @if let Some(desc) = self.description {
                            p { (desc) }
                        }
                    }
                    @if !self.buttons.is_empty() {
                        div.actions {
                            @for button in &self.buttons {
                                (button)
                            }
                        }
                    }
                }
                content {
                    @if !self.tiles.is_empty() {
                        grid-2 {
                            @for tile in &self.tiles {
                                (tile)
                            }
                        }
                    }

                    @if !self.rows.is_empty() {
                        ul.data-list {
                            @for row in &self.rows {
                                (row)
                            }
                        }
                    }
                }
            }
        }
    }
}

impl<'a> Render for InsightTile<'a> {
    fn render(&self) -> Markup {
        html! {
            panel.gray-tile {
                content {
                    hbox {
                        i class=(self.fa_icon_class) aria-hidden="true" {}
                        dl {
                            dt { (self.title) }
                            dd { (self.value) }
                        }
                        @if let Some(edit_href) = &self.edit_href {
                            a.btn.primary.edit-btn href=(edit_href) {
                                i.fas.fa-pen aria-hidden="true" {}
                            }
                        }
                        @if let Some(view_href) = &self.view_href {
                            a.btn.secondary.view-btn href=(view_href) {
                                i.fas.fa-eye aria-hidden="true" {}
                            }
                        }
                    }
                }
            }
        }
    }
}

impl<'a> Render for InsightRow<'a> {
    fn render(&self) -> Markup {
        html! {
            li {
                hbox {
                    i class=(self.fa_icon_class) aria-hidden="true" {}
                    dl {
                        dt { (self.label) }
                        dd { (self.value) }
                    }
                    @if let Some(edit_href) = &self.edit_href {
                        span {
                            " ("
                            a.edit-link href=(edit_href) { "Edit" }
                            ")"
                        }
                    }
                    @if let Some(view_href) = &self.view_href {
                        span {
                            " ("
                            a.view-link href=(view_href) { "View" }
                            ")"
                        }
                    }
                }
            }
        }
    }
}
