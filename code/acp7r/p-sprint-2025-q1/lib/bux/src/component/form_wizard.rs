use maud::{<PERSON><PERSON>, <PERSON><PERSON>, html};

pub fn new<T: FormWizardImpl>(current_step: T, context: T::Context) -> FormWizard<T> {
    FormWizard {
        current_step,
        context,
        id: None,
        method: None,
        hidden_list: Vec::new(),
        next_label: "Continue".to_string(),
        next_icon: "fas fa-arrow-right".to_string(),
        next_class: "primary".to_string(),
        back_label: "Back".to_string(),
        back_icon: "fas fa-arrow-left".to_string(),
        back_class: "secondary".to_string(),
        body: Vec::new(),
        heading: None,
    }
}

pub trait FormWizardImpl: PartialEq
where
    Self: Sized,
{
    type Context;
    fn all_variants() -> Vec<Self>;

    fn label(&self, ctx: &Self::Context) -> String;
    fn enabled(&self, _ctx: &Self::Context) -> bool {
        true
    }
    fn complete(&self, _ctx: &Self::Context) -> bool {
        false
    }
    fn href(&self, ctx: &Self::Context) -> String;

    /// Navigate to the next step in the wizard.
    fn next(&self) -> Option<Self> {
        let mut iter = Self::all_variants().into_iter();
        while let Some(step) = iter.next() {
            if step == *self {
                return iter.next();
            }
        }
        None
    }

    /// Navigate to the previous step in the wizard.
    fn prev(&self) -> Option<Self> {
        let mut iter = Self::all_variants().into_iter().rev();
        while let Some(step) = iter.next() {
            if step == *self {
                return iter.next();
            }
        }
        None
    }
}

pub struct FormWizard<T: FormWizardImpl> {
    pub current_step: T,
    pub context: T::Context,
    pub id: Option<String>,
    pub method: Option<String>,
    pub hidden_list: Vec<(String, String)>,
    pub next_label: String,
    pub next_icon: String,
    pub next_class: String,
    pub back_label: String,
    pub back_icon: String,
    pub back_class: String,
    pub body: Vec<Markup>,
    pub heading: Option<String>,
}

impl<T: FormWizardImpl> FormWizard<T> {
    pub fn add_body(&mut self, body: Markup) {
        self.body.push(body);
    }

    pub fn set_id(&mut self, id: &str) {
        self.id = Some(id.to_string());
    }

    pub fn set_hidden(&mut self, key: &str, value: impl Into<String>) {
        self.hidden_list.push((key.to_string(), value.into()));
    }

    pub fn set_heading(&mut self, heading: &str) {
        self.heading = Some(heading.to_string());
    }
}

impl<T: FormWizardImpl> Render for FormWizard<T> {
    fn render(&self) -> Markup {
        let all_variants = T::all_variants();
        //let current_index = all_variants.iter().position(|step| step == &self.current_step);

        html!(
            form.bux-form-wizard id=[&self.id] method=[&self.method] {
                @for (key, value) in &self.hidden_list {
                    input type="hidden" name=(key) value=(value);
                }
                header {
                    ul {
                        @for (_index, variant) in all_variants.iter().enumerate() {
                            @let enabled_class = if variant.enabled(&self.context) { "x-enabled" } else { "x-disabled" };
                            @let complete_class = if variant.complete(&self.context) { "x-complete" } else { "x-incomplete" };
                            @let selected_class = if variant == &self.current_step { "x-selected" } else { "" };
                            li class=(format!("{} {} {}", enabled_class, complete_class, selected_class)) {
                                a href=(variant.href(&self.context)) { (variant.label(&self.context)) }
                            }
                        }
                    }
                    h1 {
                        @if let Some(heading) = &self.heading {
                            (heading)
                        } @else {
                            (self.current_step.label(&self.context))
                        }
                    }
                }
                error {

                }
                content {
                    @for chunk in &self.body {
                        (chunk)
                    }
                }

                footer {
                    @let current_variant = &self.current_step;
                    @if let Some(prev_variant) = current_variant.prev() {
                        (crate::button::link::label_icon_class(&self.back_label, &self.back_icon, &prev_variant.href(&self.context), &self.back_class))
                        " "
                    }

                    @if let Some(next_variant) = current_variant.next() {
                        (crate::button::link::label_icon_class(&self.next_label, &self.next_icon, &next_variant.href(&self.context), &self.next_class))
                        " "
                    }
                }
            }
        )
    }
}
