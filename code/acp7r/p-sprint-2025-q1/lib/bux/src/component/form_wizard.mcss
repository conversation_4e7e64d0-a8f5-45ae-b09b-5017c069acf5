/* FormWizard Component CSS - Root selector: form.bux-form-wizard */
/* Modern rectangular step navigation with clean, contemporary styling */
/* Follows bux framework patterns with semantic nested selectors */

form.bux-form-wizard {
    /* Header contains step navigation */
    header {
        margin-bottom: 1rem;

        h1 {
            margin: 0;
            text-align: center;
            font-size: 20pt;
        }

        /* Step navigation list */
        ul {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            list-style: none;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 0.75rem;

            /* Individual step items */
            li {
                flex: 1;
                position: relative;

                /* Step link styling */
                a {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 0.875rem 1rem;
                    text-decoration: none;
                    border-radius: 8px;
                    font-weight: 500;
                    font-size: 0.875rem;
                    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    overflow: hidden;
                    min-height: 44px;

                    /* Icon/indicator before text */
                    &::before {
                        content: counter(step-counter);
                        counter-increment: step-counter;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 24px;
                        height: 24px;
                        border-radius: 6px;
                        font-weight: 600;
                        font-size: 0.75rem;
                        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                        flex-shrink: 0;
                    }

                }

                /* Disabled step styling (default) */
                a {
                    background: #e9ecef;
                    color: #696969;
                    cursor: not-allowed;

                    &::before {
                        background: #dee2e6;
                        color: #5C636A;
                    }

                }

                /* Enabled step styling */
                &.x-enabled a {
                    background: #fff;
                    color: #084298;
                    cursor: pointer;
                    border: 1px solid #cfe2ff;

                    &::before {
                        background: #cfe2ff;
                        color: #084298;
                    }

                }

                /* Complete step styling */
                &.x-complete a {
                    background: #f8f9fa;
                    color: #495057;
                    cursor: pointer;
                    border: 1px solid #dee2e6;

                    &::before {
                        content: "\f14a";
                        font-family: "Font Awesome 5 Free";
                        background: #e9ecef;
                        color: #28a745;
                        font-weight: 400;
                    }
                }

                /* Selected step styling */
                &.x-selected a {
                    background: linear-gradient(0deg, rgba(8, 66, 152, 1) 0%, rgba(33, 85, 162, 1) 50%, rgba(57, 104, 173, 1) 100%);
                    color: white;
                    border: 1px solid #084298;
                }

                /* Error state styling */
                &.x-error a {
                    background: #f8f9fa;
                    color: #495057;
                    cursor: pointer;
                    border: 1px solid #dc3545;

                    &::before {
                        content: "!";
                        background: #e9ecef;
                        color: #dc3545;
                        font-weight: 700;
                        font-size: 1rem;
                    }

                }
            }
        }

        /* Reset counter for step numbering */
        counter-reset: step-counter;

        /* Horizontal rule separator */
        hr {
            margin-top: 1.5rem;
            border: none;
            border-top: 1px solid #e9ecef;
        }
    }

    /* Error message container */
    error {
        display: block;
        color: #dc3545;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin-bottom: 1rem;

        &:empty {
            display: none;
        }
    }

    /* Main content area */
    content {
        display: block;
        margin-bottom: 2rem;

        /* Data list styling for key-value pairs */
        dl {
            display: grid;
            grid-template-columns: auto 1fr;
            gap: 0.5rem 1rem;
            margin-bottom: 1.5rem;

            dt {
                font-weight: 600;
                color: #495057;
            }

            dd {
                margin: 0;
                color: #6c757d;
            }
        }
    }

    /* Footer with form buttons */
    footer {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;

        /* Ensure buttons are properly spaced */
        > * {
            flex-shrink: 0;
        }
    }
}
