[package]
name = "df4l-advisor"
version = "0.1.0"
edition = "2024"

[package.metadata.acp]
module = {}
extends = ["df4l-zero", "df4l-crs", "approck", "auth-fence", "bux", "granite", "addr-iso"]


[dependencies]
approck = {workspace = true}
auth-fence = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-postgres = { workspace = true }
chrono = { version = "0.4", features = ["serde"] }
num-traits = "0.2"

maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }

df4l-zero = { path = "../df4l-zero" }
df4l-crs = { path = "../df4l-crs" }
addr-iso = { workspace = true }