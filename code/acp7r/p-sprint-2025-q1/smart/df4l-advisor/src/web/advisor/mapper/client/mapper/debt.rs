#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/debt; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        use super::lapi_tbody;
        use crate::api::client::wizard::client_wizard;

        let wizard_data = client_wizard::call(
            app,
            identity,
            client_wizard::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let tbody_inner_html = lapi_tbody::call(
            app,
            identity,
            lapi_tbody::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?
        .tbody_inner_html;

        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::Debt, wizard_data);
            wizard.set_id("debt-editor");
            wizard.set_heading("Review Your Client's Debts");
            wizard.set_hidden("client_uuid", path.client_uuid);
            wizard.add_body(html!(
                p { "This is a combined list of any debts pulled from the credit report and any debts you have manually entered.  Any debt may be marked as inactive by unchecking the checkbox." }

                table {
                    thead {
                        tr {
                            th.x-active { "Included" }
                            th.x-name { "Debt Name" }
                            th.x-balance { "Balance" }
                            th.x-balance-date { "As Of" }
                            th.x-interest { "Interest Rate" }
                            th.x-payment { "Monthly Payment" }
                        }
                    }
                    tbody {
                        (maud::PreEscaped(tbody_inner_html))
                    }
                }

                div.text-center {
                    a href="#" class="x-add-debt" { "Add New Debt" }
                }
            ));
            wizard
        };

        doc.set_title("Client Debts");
        doc.add_body(html!((wizard)));
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod lapi_tbody {
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub tbody_inner_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        use crate::api::client::debt::client_debt_merged;
        use approck::html;

        let debt_data = client_debt_merged::call(
            app,
            identity,
            client_debt_merged::Input {
                client_uuid: input.client_uuid,
            },
        )
        .await?;

        let tbody = html! {
            @for debt in &debt_data.debt_list {
                (super::make_row(debt))
            }
        };

        Ok(Output {
            tbody_inner_html: tbody.into_string(),
        })
    }
}

#[approck::api]
pub mod lapi_add {
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub tr_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        use crate::api::client::debt::client_debt_add;

        let output = client_debt_add::call(
            app,
            identity,
            client_debt_add::Input {
                client_uuid: input.client_uuid,
            },
        )
        .await?;

        Ok(Output {
            tr_html: super::make_row(&output.debt).into_string(),
        })
    }
}

fn make_row(debt: &crate::api::client::debt::client_debt_merged::Debt) -> approck::Markup {
    use approck::html;

    if debt.is_external {
        html! {
            tr client_debt_uuid=(debt.client_debt_uuid.to_string()) {
                td.x-active {
                    (bux::input::checkbox::name_value("active", debt.active))
                }
                td.x-name {
                    span {
                        (debt.name.as_ref().unwrap_or(&"-".to_string()))
                    }
                }
                td.x-balance {
                    span {
                        (bux::format_currency_us_0_option(debt.balance))
                    }
                }
                td.x-balance-date {
                    span {
                        (bux::format_date_us_option(debt.balance_date))
                    }
                }
                td.x-interest {
                    span {
                        (bux::format_percentage_us_2_option(debt.interest))
                    }
                }
                td.x-payment {
                    span {
                        (bux::format_currency_us_0_option(debt.payment))
                    }
                }
            }
        }
    }
    // Editable debts get an editor
    else {
        html!(
            tr client_debt_uuid=(debt.client_debt_uuid.to_string()) editable {
                td.x-active {
                    (bux::input::checkbox::name_value("active", debt.active))
                    button.x-delete type="button" title="Delete this debt" {
                        "🗑"
                    }
                }
                td.x-name {
                    (bux::input::text::string::name_value("name", debt.name.as_deref()))
                }
                td.x-balance {
                    (bux::input::text::currency::name_value("balance", debt.balance))
                }
                td.x-balance-date {
                    (bux::input::date::name_value("balance_date", debt.balance_date))
                }
                td.x-interest {
                    (bux::input::text::percentage::name_value("interest_rate", debt.interest))
                }
                td.x-payment {
                    (bux::input::text::currency::name_value("payment", debt.payment))
                }
            }
        )
    }
}
