#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/report; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        doc.set_title("Debt Report");

        use crate::api::client::report::client_report;
        use crate::api::client::wizard::client_wizard;

        let wizard_data = client_wizard::call(
            app,
            identity,
            client_wizard::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let _output = client_report::call(
            app,
            identity,
            client_report::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::Report, wizard_data);
            wizard.set_hidden("client_uuid", path.client_uuid);
            wizard.set_heading("Review Your Client's Debt Report");
            wizard.add_body(html!(
                div.df4l-advisor-onboarding-report {
                    h2 {"Debt Report"}
                    p {"TODO:DF4L: We need to generate a report"}
                    p {"Your client onboarding is now complete. Click Continue to proceed to the client dashboard."}
                }
            ));
            wizard
        };

        doc.add_body(html!(
            div.constrain-width-md {
                (wizard)
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
