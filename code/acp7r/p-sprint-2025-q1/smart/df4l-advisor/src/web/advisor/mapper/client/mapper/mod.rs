pub mod budget;
pub mod crs;
pub mod debt;
pub mod edit;
pub mod index;
pub mod messages;
pub mod report;

#[approck::prefix(/advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/)]
pub mod prefix {
    pub fn menu(app: App, menu: Menu, advisor_uuid: Uuid, client_uuid: Uuid) {
        menu.set_label_name_uri(
            "Client Details",
            app.uuid_to_label(client_uuid),
            &crate::ml_advisor_client_details(advisor_uuid, client_uuid),
        );

        menu.add_link(
            "Edit Client",
            &crate::ml_advisor_client_edit(advisor_uuid, client_uuid),
        );

        menu.add_link(
            "Credit Report",
            &crate::ml_advisor_client_crs(advisor_uuid, client_uuid),
        );
        menu.add_link(
            "Client Debts",
            &crate::ml_advisor_client_debt(advisor_uuid, client_uuid),
        );
        menu.add_link(
            "Client Budget",
            &crate::ml_advisor_client_budget(advisor_uuid, client_uuid),
        );
        menu.add_link(
            "Debt Report",
            &crate::ml_advisor_client_report(advisor_uuid, client_uuid),
        );
        menu.add_link(
            "Messages",
            &crate::ml_advisor_client_messages(advisor_uuid, client_uuid),
        );
    }
    pub fn auth() {}
}

use bux::component::form_wizard::FormWizardImpl;

pub type WizardData = crate::api::client::wizard::client_wizard::Output;

#[derive(PartialEq)]
pub enum WizardStep {
    Edit,
    Crs,
    Debt,
    Budget,
    Report,
}

#[allow(refining_impl_trait)]
impl FormWizardImpl for WizardStep {
    type Context = WizardData;
    fn all_variants() -> Vec<Self> {
        vec![
            WizardStep::Edit,
            WizardStep::Crs,
            WizardStep::Debt,
            WizardStep::Budget,
            WizardStep::Report,
        ]
    }

    fn label(&self, _ctx: &WizardData) -> String {
        match self {
            WizardStep::Edit => "Client Info".to_string(),
            WizardStep::Crs => "Credit Report".to_string(),
            WizardStep::Debt => "List Debts".to_string(),
            WizardStep::Budget => "Make Budget".to_string(),
            WizardStep::Report => "View Report".to_string(),
        }
    }

    fn complete(&self, ctx: &WizardData) -> bool {
        match self {
            WizardStep::Edit => ctx.edit_complete,
            WizardStep::Crs => ctx.crs_complete,
            WizardStep::Debt => ctx.debt_complete,
            WizardStep::Budget => ctx.budget_complete,
            WizardStep::Report => ctx.report_complete,
        }
    }

    fn href(&self, ctx: &WizardData) -> String {
        match self {
            WizardStep::Edit => crate::ml_advisor_client_edit(ctx.advisor_uuid, ctx.client_uuid),
            WizardStep::Crs => crate::ml_advisor_client_crs(ctx.advisor_uuid, ctx.client_uuid),
            WizardStep::Debt => crate::ml_advisor_client_debt(ctx.advisor_uuid, ctx.client_uuid),
            WizardStep::Budget => {
                crate::ml_advisor_client_budget(ctx.advisor_uuid, ctx.client_uuid)
            }
            WizardStep::Report => {
                crate::ml_advisor_client_report(ctx.advisor_uuid, ctx.client_uuid)
            }
        }
    }
}
