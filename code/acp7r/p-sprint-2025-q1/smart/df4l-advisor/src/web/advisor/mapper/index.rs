#[approck::http(GET /advisor/{advisor_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::advisor::dashboard::dashboard;
        use crate::api::advisor::setup::advisor_setup_checklist;
        use approck::html;

        let advisor_dashboard = dashboard::call(
            app,
            identity,
            dashboard::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        let checklist = advisor_setup_checklist::call(
            app,
            identity,
            advisor_setup_checklist::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        doc.set_title("Advisor Details");

        let mut quick_info_box = bux::ui::quick_info_box::new();
        quick_info_box.icon_name_href_count(
            "🧑‍💼",
            "Active Clients",
            &crate::ml_advisor_client_list(path.advisor_uuid),
            advisor_dashboard.active_clients,
        );
        quick_info_box.icon_name_href_count(
            "🧑‍💼",
            "Active Legacy Clients",
            &crate::ml_advisor_client0_list(path.advisor_uuid),
            advisor_dashboard.active_legacy_clients,
        );

        // Only show setup section if there are incomplete items
        let setup_section = if !checklist.setup_items.is_empty() {
            let mut table = bux::component::info_table(&checklist);
            table.set_heading("Finish Your Setup");
            for item in &checklist.setup_items {
                table.add_row(&item.name, |_| {
                    html! {
                        "Incomplete — "
                        a href=(item.action_url) { "Continue" }
                    }
                });
            }
            html! { (table) }
        } else {
            html! {
                div class="alert alert-success" {
                    h4 { "✅ Setup Complete!" }
                    p { "All required setup items have been completed." }
                }
            }
        };

        doc.add_body(html!(
            grid-2 {
                div {
                    h2 { "Welcome, " (advisor_dashboard.name) "!" }
                    (quick_info_box)
                }

                div {
                    (setup_section)
                    hr;
                    h3 {
                        "GBU Agent ID "
                        @if advisor_dashboard.gbu_agent_id.is_some() {
                            a href=(crate::ml_myaccount_advisor_gbu(advisor_dashboard.identity_uuid, path.advisor_uuid)) { "Edit" }
                        }
                    }
                    @if let Some(gbu_id) = &advisor_dashboard.gbu_agent_id {
                        (gbu_id)
                    } @else {
                        "Not Set"
                    }

                    h3 {
                        "Licensed States "
                        @if !advisor_dashboard.statelic.is_empty() {
                            a href=(crate::ml_myaccount_advisor_statelic(advisor_dashboard.identity_uuid, path.advisor_uuid)) { "Edit" }
                        }
                    }
                    @if advisor_dashboard.statelic.is_empty() {
                        "Not Set"
                    } @else {
                        (advisor_dashboard.statelic.join(", "))
                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
