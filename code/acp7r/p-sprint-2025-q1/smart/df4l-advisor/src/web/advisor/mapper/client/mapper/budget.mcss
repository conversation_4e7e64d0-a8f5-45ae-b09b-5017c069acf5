@import "./mod.mcss";

/* Minimal styling - let bux framework handle component styling */
/* Use semantic nested selectors instead of utility classes */

#onboarding {
    content {
        /* Question and answer structure */
        .x-question {
            margin-bottom: 1.5rem;

            /* Question text styling */
            > div:first-child {
                margin-bottom: 0.75rem;
                font-weight: bold;
            }

            /* Answer controls container */
            > div:last-child {

                bux-input-radio-boolean {

                    ul {
                        padding: 0;
                    }
                }

                bux-input-text-currency {
                    width: 25rem;

                    label {
                        font-weight: normal;
                    }
                }
            }

            /* Selection state styling for radio buttons */
            bux-input-radio-boolean {
                &.x-selected {
                    .x-radio-option {
                        background-color: #e6f0ff;
                        border-color: #007bff;

                        input[type="radio"]:checked + label {
                            color: #007bff;
                            font-weight: 600;
                        }
                    }
                }
            }

            /* Selection state styling for currency inputs */
            bux-input-text-currency {
                &.x-selected {
                    input {
                        border-color: #007bff;
                        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
                    }
                }
            }
        }

        /* Total section styling */
        .x-total {
            margin-top: 2rem;
            padding: 1rem;
            text-align: center;
            font-size: 1.1rem;
            font-weight: 600;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            border: 1px solid #dee2e6;

            span {
                color: #28a745;
                font-weight: 700;
            }
        }
    }

    /* Footer navigation button styling */
    footer {
        /* Ensure proper spacing between back and next buttons */
        gap: 1rem;
    }
}
