#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/df4l/activate; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        use crate::api::client0::detail::detail;
        let client = detail::call(
            app,
            identity,
            detail::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        doc.set_title(&format!("Activate Debt Free Program for {}", client.name));

        let mut panel = bux::component::save_cancel_form_panel(
            &format!("Activate Debt Free Program for {}", client.name),
            &crate::ml_advisor_client0_details(path.advisor_uuid, path.client_uuid),
        );

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            (bux::input::text::string::name_label_readonly("name", "Name:", "<PERSON>"))
            (bux::input::text::string::name_label_value_help("phone_number", "Send Text Messages To Phone Number:", None, "Do not include country code."))
            (bux::input::text::string::name_label_value_help("additional_phone_number", "Send Text Messages To Additional Phone Number:", None, "Do not include country code."))
            (bux::input::date::bux_input_date("startDate", "Policy Effective Date", None))
            div style="background-color: #fff3cd;" {
                p { b { "A text message will be sent to the number above welcoming them to the Debt Free program." } }
            }
        ));
        doc.add_body(html! {
            bux-action-panel {
                (panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}
