#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/edit; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        use crate::api::client::edit::client_edit_load;
        use crate::api::client::wizard::client_wizard;

        let wizard_data = client_wizard::call(
            app,
            identity,
            client_wizard::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let client = client_edit_load::call(
            app,
            identity,
            client_edit_load::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;
        doc.set_title("Edit Client");

        let mut form_wizard = bux::component::form_wizard::new(WizardStep::Edit, wizard_data);
        form_wizard.set_heading("Edit Client Info");
        form_wizard.set_id("client-editor");
        form_wizard.set_hidden("advisor_uuid", path.advisor_uuid);
        form_wizard.set_hidden("client_uuid", path.client_uuid);

        #[rustfmt::skip]
        form_wizard.add_body(maud::html!(
            grid-2 {
                div {
                    grid-2 {
                        (bux::input::text::string::name_label_value("first_name", "First Name", Some(&client.first_name)))
                        (bux::input::text::string::name_label_value("last_name", "Last Name", Some(&client.last_name)))
                        (bux::input::text::string::name_label_value("email", "Email", client.email.as_deref()))
                        (bux::input::text::string::name_label_value("phone", "Phone", client.phone.as_deref()))
                    }
                    (bux::input::textarea::string::name_label_value("note", "Note", client.note.as_deref()))
                }
                div {
                    (bux::input::text::string::name_label_value("address1", "Address Line 1", client.address1.as_deref()))
                    (bux::input::text::string::name_label_value("address2", "Address Line 2", client.address2.as_deref()))
                    grid-3 {
                        (bux::input::text::string::name_label_value("city", "City", client.city.as_deref()))
                        (addr_iso::input::address_us_select::us_state_select_with_help(app, "state", "State", client.state.as_deref(), "").await?)
                        (bux::input::text::string::name_label_value("zip", "ZIP", client.zip.as_deref()))
                    }
                }
            }
        ));
        doc.add_body(html! {
            div.constrain-width-md {
                (form_wizard)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}
