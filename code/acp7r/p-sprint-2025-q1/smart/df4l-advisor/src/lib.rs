pub mod api;
pub mod web;

// Test comment to trigger build and test incremental compilation behavior

pub trait App:
    approck::App
    + approck_postgres::App
    + df4l_zero::App
    + df4l_crs::App
    + df4l_icover::App
    + addr_iso::App
{
}

#[allow(async_fn_in_trait)]
pub trait Identity: approck::Identity + auth_fence::Identity + df4l_zero::Identity {
    fn web_usage(&self) -> bool;
    fn api_usage(&self) -> bool;
    fn advisor_read(&self, advisor_uuid: granite::Uuid) -> bool;
    fn advisor_write(&self, advisor_uuid: granite::Uuid) -> bool;
    fn client_list(&self, advisor_uuid: granite::Uuid) -> bool;
    fn client_add(&self, advisor_uuid: granite::Uuid) -> bool;
    async fn client_write(
        &self,
        dbcx: &impl approck_postgres::DB,
        client_uuid: granite::Uuid,
    ) -> bool;
    async fn client_read(
        &self,
        dbcx: &impl approck_postgres::DB,
        client_uuid: granite::Uuid,
    ) -> bool;
}

pub trait Document: bux::document::Cliffy {}
pub trait DocumentPublic: bux::document::Base {}

pub fn ml_advisor(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/", advisor_uuid)
}

pub fn ml_advisor_client_list(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/client/", advisor_uuid)
}

pub fn ml_advisor_client_add(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/client/add", advisor_uuid)
}

pub fn ml_advisor_client_details(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{}/client/{}/", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client_edit(advisor_uuid: granite::Uuid, client_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/client/{}/edit", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client_crs(advisor_uuid: granite::Uuid, client_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/client/{}/crs", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client_debt(advisor_uuid: granite::Uuid, client_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/client/{}/debt", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client_budget(advisor_uuid: granite::Uuid, client_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/client/{}/budget", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client_icover(advisor_uuid: granite::Uuid, client_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/client/{}/icover", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client_report(advisor_uuid: granite::Uuid, client_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/client/{}/report", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client_messages(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{}/client/{}/messages", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client0_list(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/client0/", advisor_uuid)
}

pub fn ml_advisor_client0_add(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/client0/add", advisor_uuid)
}

pub fn ml_advisor_client0_details(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{}/client0/{}/", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client0_edit(advisor_uuid: granite::Uuid, client_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/client0/{}/edit", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client0_debt_add(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{}/client0/{}/debt/add", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client0_debt_edit(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
    client_debt_uuid: granite::Uuid,
) -> String {
    format!(
        "/advisor/{}/client0/{}/debt/{}/edit",
        advisor_uuid, client_uuid, client_debt_uuid
    )
}

pub fn ml_advisor_client0_debt_details(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
    client_debt_uuid: granite::Uuid,
) -> String {
    format!(
        "/advisor/{}/client0/{}/debt/{}/",
        advisor_uuid, client_uuid, client_debt_uuid
    )
}
pub fn ml_advisor_client0_df4l_edit(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!(
        "/advisor/{}/client0/{}/df4l/edit",
        advisor_uuid, client_uuid
    )
}

pub fn ml_advisor_client0_debt_list(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{}/client0/{}/debt/", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client0_note_list(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{}/client0/{}/note/", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client0_note_add(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{}/client0/{}/note/add", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client0_note_view(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
    client_note_uuid: granite::Uuid,
) -> String {
    format!(
        "/advisor/{}/client0/{}/note/{}/",
        advisor_uuid, client_uuid, client_note_uuid
    )
}

pub fn ml_advisor_client0_life_insurance(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!(
        "/advisor/{}/client0/{}/life-insurance/",
        advisor_uuid, client_uuid
    )
}

pub fn ml_advisor_client0_df4l_dump(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!(
        "/advisor/{}/client0/{}/df4l/dump",
        advisor_uuid, client_uuid
    )
}

pub fn ml_advisor_client0_df4l_report(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!(
        "/advisor/{}/client0/{}/df4l/report",
        advisor_uuid, client_uuid
    )
}

pub fn ml_advisor_client0_df4l_activate(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!(
        "/advisor/{}/client0/{}/df4l/activate",
        advisor_uuid, client_uuid
    )
}

// NOTE: new client ml functions
pub fn ml_advisor_client_debt_list(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{}/client/{}/debt/", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client_debt_add(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{}/client/{}/debt/add", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client_debt_edit(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
    client_debt_uuid: granite::Uuid,
) -> String {
    format!(
        "/advisor/{}/client/{}/debt/{}/edit",
        advisor_uuid, client_uuid, client_debt_uuid
    )
}

pub fn ml_advisor_client_debt_details(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
    client_debt_uuid: granite::Uuid,
) -> String {
    format!(
        "/advisor/{}/client/{}/debt/{}/",
        advisor_uuid, client_uuid, client_debt_uuid
    )
}

pub fn ml_advisor_client_debt_delete(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
    client_debt_uuid: granite::Uuid,
) -> String {
    format!(
        "/advisor/{}/client/{}/debt/{}/delete",
        advisor_uuid, client_uuid, client_debt_uuid
    )
}

pub fn ml_advisor_onboarding_checklist(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/onboarding_checklist/", advisor_uuid)
}

pub fn ml_gbu_agent_add(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/onboarding/gbu-agent", advisor_uuid)
}

pub fn ml_state_license_add(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{}/onboarding/state-license", advisor_uuid)
}

pub fn ml_myaccount_advisor_gbu(
    identity_uuid: granite::Uuid,
    advisor_uuid: granite::Uuid,
) -> String {
    format!("/myaccount/{}/advisor/{}/gbu", identity_uuid, advisor_uuid)
}

pub fn ml_myaccount_advisor_statelic(
    identity_uuid: granite::Uuid,
    advisor_uuid: granite::Uuid,
) -> String {
    format!(
        "/myaccount/{}/advisor/{}/statelic",
        identity_uuid, advisor_uuid
    )
}

pub fn ml_myaccount_advisor_contact(
    identity_uuid: granite::Uuid,
    advisor_uuid: granite::Uuid,
) -> String {
    format!(
        "/myaccount/{}/advisor/{}/contact",
        identity_uuid, advisor_uuid
    )
}

pub fn ml_advisor_client_onboarding(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{}/client/{}/edit", advisor_uuid, client_uuid)
}

pub fn ml_advisor_client_onboarding_report(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{}/client/{}/report", advisor_uuid, client_uuid)
}
