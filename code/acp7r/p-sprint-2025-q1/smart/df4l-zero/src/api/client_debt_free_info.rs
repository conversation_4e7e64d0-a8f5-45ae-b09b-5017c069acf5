#[approck::api]
pub mod client_debt_free_info {
    use granite::return_authorization_error;

    use chrono::{Datelike, Local, NaiveDate};
    //use rust_decimal::Decimal;
    use bux::format_currency_us_0;
    use granite::Decimal;
    use std::collections::HashMap;
    use std::str::FromStr;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
        pub generate_plan: bool,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Client {
        pub client_uuid: Uuid,
        pub name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub phone2: Option<String>,
        pub monthly_budget: Option<Decimal>,
        pub annual_insurance_premium: Option<Decimal>,
        pub annual_insurance_base: Option<Decimal>,
        pub annual_insurance_pua: Option<Decimal>,
        pub net_cash_at_end: Option<Decimal>,
        pub debt_free_start_date: Option<DateUtc>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct ClientDebtItem {
        pub client_debt_uuid: Uuid,
        pub name: String,
        pub balance: Option<Decimal>,
        pub interest_rate: Option<Decimal>,
        pub monthly_payment: Option<Decimal>,
        pub active: bool,
        pub note: Option<String>,
    }

    // ABID: VERIFIED
    #[granite::gtype(ApiOutput)]
    pub struct DebtInfo {
        pub start_year: u32,
        pub start_month: u32,

        pub debt_count: Option<i32>,
        pub debt_balance: Option<Decimal>,
        pub monthly_budget: Option<Decimal>,
        pub annual_budget: Option<Decimal>,
        pub minimum_monthly_payment: Option<Decimal>,
        pub average_interest_rate: Option<Decimal>,
        pub total_monthly_interest: Option<Decimal>,
        pub total_monthly_interest_percent: Option<Decimal>,

        pub annual_insurance_premium: Option<Decimal>,
        pub annual_insurance_pua: Option<Decimal>,
        pub monthly_insurance_premium: Option<Decimal>,
        pub monthly_insurance_pua: Option<Decimal>,

        pub annual_insurance_base: Option<Decimal>,
        pub monthly_insurance_base: Option<Decimal>,

        pub extra_pua_map: HashMap<i32, Decimal>,

        pub net_cash_at_end: Option<Decimal>,
        pub percent_to_paid_up_additions: Option<Decimal>,

        pub upcoming_changes: UpcomingChanges,
        pub debt_list: Vec<DebtInfoDebtItem>,
        //pub error_list: Vec<String>, // TODO: remove this, it's used in the legacy system
        pub plan: Vec<MonthPlan>,

        pub total_paid: Decimal,
        pub total_unpaid: Decimal,
        pub esimated_cash_value: Decimal,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client: Client,
        pub debt_info: DebtInfo,
    }

    // ABID: VERIFIED
    #[granite::gtype(ApiOutput)]
    pub struct DebtInfoDebtItem {
        pub client_debt_uuid: Uuid,
        pub debt_name: String,
        pub full_name: String,
        pub balance: Decimal,
        pub interest_rate: Decimal,
        pub monthly_payment: Decimal,
        pub monthly_interest_percent: Option<Decimal>,
        pub monthly_interest: Option<Decimal>,
        pub active: bool,

        pub payoff_month: Option<u32>,
        pub payoff_date: Option<String>,
        pub payoff_date_formatted: Option<String>, // '23, May, NEW
        pub payoff_interest: Decimal,
        pub standard_payoff_month: u32,
        pub standard_payoff_interest: Decimal,

        // NEW FIELDS ADDED TO POWER RUST UI
        pub effective_interest_cost_percent: Decimal,
        pub is_paid_off_as_of_today: bool,
    }

    // NEW STRUCTURE. No verification beeded
    #[granite::gtype(ApiOutput)]
    pub struct UpcomingChanges {
        pub current_month: Option<MonthChange>,
        pub next_month: Option<MonthChange>,
        pub following_month: Option<MonthChange>,
    }

    //ABID: VERIFIED
    #[granite::gtype(ApiOutput)]
    pub struct MonthChange {
        pub starting_balance: Decimal,
        pub ending_balance: Decimal,
        pub interest_charge: Decimal,
        pub minimum_payment: Decimal,
        pub this_month_extra: Vec<String>, // SMS: Snowball Shift. last_moth_extra, this_month_extra, this_month_total_plus_extra are used for displaying the message, not for calculations.
        pub payoff: Vec<String>,
    }

    // NEW STRUCTURE. No verification beeded
    #[granite::gtype(ApiOutput)]
    pub struct MonthYear {
        pub month: u32,
        pub year: u32,
    }

    // ABID: STRUCTURE VERIED
    #[granite::gtype(ApiOutput)]
    pub struct MonthPlan {
        pub month: u32,
        pub month_text: String,
        pub month_year_tuple: MonthYear, // TODO: rename this to month_year once conversion is complete and verified
        pub debt_list: Vec<MonthDebt>,   // TOOD: VERIFY STRUCTURE
        pub premium_total: Decimal,
        pub premium_pua: Decimal,
        pub premium_base: Decimal,

        pub pua_added: Decimal,
        pub pua_extra: Decimal,
        pub pua_balance: Decimal,

        pub loan_starting_balance: Decimal,
        //pub loan_ending_balance: Decimal,
        pub loan_draw: Decimal,
        pub loan_payed: Decimal,

        pub budget_total: Decimal,
        pub budget_premium: Decimal,
        pub budget_minimum_payments: Decimal,
        pub budget_extra_payments: Decimal,
        pub budget_repayment: Decimal,
        pub budget_remaining: Decimal,

        pub debt_starting_balance: Decimal,
        pub debt_interest_charge: Decimal,
        pub debt_paid_down: Decimal,
        pub debt_ending_balance: Decimal,

        pub cash_value: Decimal,
        pub cash_net: Decimal,

        pub sms_map: HashMap<String, String>,
    }

    // ABID: STRUCTURE VERIFIED
    // This debt is used by MonthPlan
    #[granite::gtype(ApiOutput)]
    pub struct MonthDebt {
        pub full_name: String,
        pub starting_balance: Decimal,
        pub interest_charge: Decimal,
        pub minimum_payment: Decimal,
        pub extra_payment: Decimal,
        pub payoff_payment: Decimal,
        pub ending_balance: Decimal,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .df4l_zero_client_read(&dbcx, input.client_uuid)
            .await
        {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_uuid: Uuid,
                advisor_uuid: Option<Uuid>,
                name: String,
                email: Option<String>,
                phone: Option<String>,
                phone2: Option<String>,
                monthly_budget: Option<Decimal>,
                annual_insurance_premium: Option<Decimal>,
                annual_insurance_pua: Option<Decimal>,
                net_cash_at_end: Option<Decimal>,
                debt_free_start_date: Option<DateUtc>,
            };
            SELECT
                client_uuid,
                advisor_uuid,
                first_name || " " || last_name AS name,
                email,
                phone,
                phone2,
                monthly_budget,
                annual_insurance_premium,
                annual_insurance_pua,
                net_cash_at_end,
                debt_free_start_date
            FROM
                df4l.client0
            WHERE true
            AND advisor_uuid = $advisor_uuid::uuid
            AND client_uuid = $client_uuid::uuid
        )
        .await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_debt_uuid: Uuid,
                name: String,
                balance: Option<Decimal>,
                interest_rate: Option<Decimal>,
                monthly_payment: Option<Decimal>,
                active: bool,
                note: Option<String>,
            };
            SELECT
                cd.client_debt_uuid,
                cd.name,
                cd.balance,
                cd.interest_rate,
                cd.monthly_payment,
                cd.active,
                cd.note
            FROM
                df4l.client0_debt AS cd
            WHERE
                cd.client_uuid = $client_uuid::uuid
        )
        .await?;

        // Create Client struct
        let client = Client {
            client_uuid: row.client_uuid,
            name: row.name,
            email: row.email,
            phone: row.phone,
            phone2: row.phone2,
            monthly_budget: row.monthly_budget,
            net_cash_at_end: row.net_cash_at_end,
            annual_insurance_premium: row.annual_insurance_premium,
            annual_insurance_pua: row.annual_insurance_pua,
            annual_insurance_base: if let (Some(premium), Some(pua)) =
                (row.annual_insurance_premium, row.annual_insurance_pua)
            {
                Some(premium - pua)
            } else {
                None
            },
            debt_free_start_date: row.debt_free_start_date,
        };

        // Create ClientDebtInfo struct
        let debt_list: Vec<ClientDebtItem> = rows
            .into_iter()
            .map(|r| ClientDebtItem {
                client_debt_uuid: r.client_debt_uuid,
                name: r.name,
                balance: r.balance,
                interest_rate: r.interest_rate,
                monthly_payment: r.monthly_payment,
                active: r.active,
                note: r.note,
            })
            .collect();

        // Create DebtInfo struct
        ////////////////////////////////////////////////////////////////////////////
        // loop through client_report.debt_list and initialize debt_items

        let mut debt_items: Vec<DebtInfoDebtItem> = vec![];
        let mut error_list = Vec::new();
        for client0_debt in debt_list {
            if client0_debt.balance.is_none() {
                error_list.push(format!("Debt {} has no balance", client0_debt.name));
                continue;
            }
            if client0_debt.interest_rate.is_none() {
                error_list.push(format!("Debt {} has no interest rate", client0_debt.name));
                continue;
            }
            /*
            def IsPaymentEnough(self):
            if self.MonthlyPayment is None or self.InterestRate is None or self.Balance is None:
              return None
            return self.MonthlyPayment > (self.Balance * self.InterestRate * Decimal('0.01') / 12)


            if not debt.IsPaymentEnough:
                RVAL.EL.Add(f'Monthly payment on {debt.FullName} is not enough to cover monthly interest charges.', Key=debt.AdvisorClient_Debt_ZNID)
            */
            fn is_payment_enough(
                monthly_payment: &Option<Decimal>,
                interest_rate: &Option<Decimal>,
                balance: &Option<Decimal>,
            ) -> Option<bool> {
                let monthly_payment = match monthly_payment {
                    Some(v) => v,
                    None => return None,
                };
                let interest_rate = match interest_rate {
                    Some(v) => v,
                    None => return None,
                };
                let balance = match balance {
                    Some(v) => v,
                    None => return None,
                };
                Some(
                    monthly_payment
                        > &(balance * interest_rate * Decimal::from_str("0.01").unwrap()
                            / Decimal::from(12)),
                )
            }

            if let Some(false) = is_payment_enough(
                &client0_debt.monthly_payment,
                &client0_debt.interest_rate,
                &client0_debt.balance,
            ) {
                error_list.push(format!(
                    "Monthly payment on {} is not enough to cover monthly interest charges.",
                    client0_debt.name
                ));
                continue;
            }

            /*
            def MonthlyInterest(self):
                if not self.Balance or not self.MonthlyPayment or not self.InterestRate:
                    return None
                return round((float(self.Balance) * (float(self.InterestRate)*0.01) / 12),2)
            */
            fn monthly_interest(
                balance: &Option<Decimal>,
                interest_rate: &Option<Decimal>,
                monthly_payment: &Option<Decimal>,
            ) -> Option<Decimal> {
                let balance = match balance {
                    Some(v) => v,
                    None => return None,
                };
                let interest_rate = match interest_rate {
                    Some(v) => v,
                    None => return None,
                };
                let _monthly_payment = match monthly_payment {
                    Some(v) => v,
                    None => return None,
                };
                Some(
                    (balance * interest_rate * Decimal::from_str("0.01").unwrap()
                        / Decimal::from(12))
                    .round_dp(2),
                )
            }

            debt_items.push(DebtInfoDebtItem {
                client_debt_uuid: client0_debt.client_debt_uuid,
                debt_name: client0_debt.name.clone(),
                full_name: client0_debt.name,
                balance: client0_debt.balance.unwrap_or(Decimal::ZERO),
                interest_rate: client0_debt.interest_rate.unwrap_or(Decimal::ZERO),
                monthly_payment: client0_debt.monthly_payment.unwrap_or(Decimal::ZERO),
                monthly_interest: monthly_interest(
                    // NOTE: legacy system is using monthly_interest DB value. New system doesn'y have it.
                    &client0_debt.balance,
                    &client0_debt.interest_rate,
                    &client0_debt.monthly_payment,
                ),
                monthly_interest_percent: None, // TODO: Check it. legacy system used MonthlyInterestPercent value from the DB.
                active: client0_debt.active,
                payoff_month: None,
                payoff_date: None,
                payoff_date_formatted: None,
                payoff_interest: Decimal::ZERO,
                standard_payoff_month: 0,
                standard_payoff_interest: Decimal::ZERO,

                // NEW FIELDS ADDED TO POWER RUST UI
                effective_interest_cost_percent: Decimal::ZERO,
                is_paid_off_as_of_today: false,
            });
        }

        // Return error response if no debts are provided
        //if debt_list.is_empty() {
        if true {
            error_list.push("You must have at least one debt entered.".to_string());
        }

        /*
        if !error_list.is_empty() {
            return Self {
                error_list,
                ..Default::default()
            };
        }
        */

        // Set calculated data
        let today = Local::now().date_naive();

        // Program start date. It is used to calculate monthly offsets
        let start_date = client.debt_free_start_date;
        let start_year = start_date.map_or(today.year(), |d| d.year()) as u32;
        let start_month = start_date.map_or(today.month(), |d| d.month());

        // Set monthly budget and insurance values
        let monthly_budget = client.monthly_budget; //Some(Decimal::new(2000, 0));
        let annual_insurance_premium = client.annual_insurance_premium; // Some(Decimal::new(6000, 0));
        let annual_insurance_pua = client.annual_insurance_pua; // Some(Decimal::new(2000, 0));

        let mut info = DebtInfo {
            start_year,  // VERIFIED
            start_month, // VERIFIED

            debt_count: Some(debt_items.len() as i32), // VERIFIED
            debt_balance: Some(debt_items.iter().map(|d| d.balance).sum()), // VERIFIED
            monthly_budget,                            // VERIFIED
            annual_budget: monthly_budget.map(|mb| mb * Decimal::new(12, 0)), // VERIFIED

            minimum_monthly_payment: Some(debt_items.iter().map(|d| d.monthly_payment).sum()), // VERIFIED
            average_interest_rate: None,  // CHECK IT BELOW - VERIFIED
            total_monthly_interest: None, // CHECK IT BELOW - VERIFIED
            total_monthly_interest_percent: None, // CHECK IT BELOW - VERIFIED

            annual_insurance_premium, // VERIFIED
            annual_insurance_pua,     // VERIFIED
            monthly_insurance_premium: annual_insurance_premium.map(|p| p / Decimal::new(12, 0)), // VERIFIED
            monthly_insurance_pua: annual_insurance_pua.map(|p| p / Decimal::new(12, 0)), // VERIFIED

            annual_insurance_base: None,  // CHECK IT BELOW - VERIFIED
            monthly_insurance_base: None, // CHECK IT BELOW - VERIFIED

            percent_to_paid_up_additions: None, // CHECK IT BELOW - VERIFIED

            extra_pua_map: HashMap::new(), // CHECK IT BELOW - TODO - IMPLEMENT

            net_cash_at_end: client.net_cash_at_end, // VERIFIED

            total_paid: Decimal::ZERO,   // NEW FIELD TO POWER RUST SCREEN
            total_unpaid: Decimal::ZERO, // NEW FIELD TO POWER RUST SCREEN
            esimated_cash_value: Decimal::ZERO, // NEW FIELD TO POWER RUST SCREEN

            upcoming_changes: UpcomingChanges {
                current_month: None,
                next_month: None,
                following_month: None,
            },
            debt_list: debt_items,
            plan: Vec::new(),
        };

        // Calculate insurance base values
        if let (Some(premium), Some(pua)) =
            (info.annual_insurance_premium, info.annual_insurance_pua)
        {
            info.annual_insurance_base = Some(premium - pua);
        }

        if let (Some(premium), Some(pua)) =
            (info.monthly_insurance_premium, info.monthly_insurance_pua)
        {
            info.monthly_insurance_base = Some(premium - pua);
        }

        // Calculate average interest rate
        if let Some(debt_balance) = info.debt_balance {
            if debt_balance > Decimal::ZERO {
                let weighted_sum: Decimal = info
                    .debt_list
                    .iter()
                    .map(|d| d.interest_rate * d.balance)
                    .sum();
                info.average_interest_rate =
                    Some((weighted_sum / debt_balance).round() / Decimal::new(100, 0));
            }
        }

        // Calculate monthly interest totals
        let total_monthly_interest = info
            .debt_list
            .iter()
            .filter_map(|d| d.monthly_interest)
            .sum::<Decimal>();

        info.total_monthly_interest = Some(total_monthly_interest);

        if let Some(min_payment) = info.minimum_monthly_payment {
            if min_payment > Decimal::ZERO {
                info.total_monthly_interest_percent = Some(
                    ((total_monthly_interest / min_payment) * Decimal::new(100, 0)).round_dp(2),
                );
            }
        }

        // Calculate percent to paid up additions
        info.percent_to_paid_up_additions = Some(
            (info.monthly_insurance_pua.unwrap_or(Decimal::ZERO)
                / info.monthly_budget.unwrap_or(Decimal::ZERO))
                * Decimal::new(100, 0),
        );

        // calculate extra_pua_map
        /* TODO calculate extra_pua_map. it's fetched from client.debt_free_extra_pua_list
        RVAL.ExtraPUAMap = {}
        for row in self.DebtFree_ExtraPUA_List:
          if row.Month not in RVAL.ExtraPUAMap:
            RVAL.ExtraPUAMap[row.Month] = Decimal(0)
          RVAL.ExtraPUAMap[row.Month] += row.Amount

        // Populate extra PUA map
        for row in client.debt_free_extra_pua_list {
            info.extra_pua_map.entry(row.month).or_insert(Decimal::ZERO) += row.amount;
        }
        */

        // /////////////////////////////////////////////////////////////////////////////////////////////
        // Calculate plan

        generate_plan(&mut info, Some(today)).unwrap();

        // EOF Calculate plan
        // /////////////////////////////////////////////////////////////////////////////////////////////

        // Calculate total paid and total unpaid amounts
        info.total_paid = info
            .plan
            .iter()
            .map(|m| m.premium_total + m.budget_total)
            .sum();

        info.total_unpaid = info.debt_balance.unwrap_or_default() - info.total_paid;

        Ok(Output {
            client,
            debt_info: info,
        })
    }

    // Format decimal optional value as $100/mo $1,200/yr or $100.50/mo $1,200.50/yr if cents exist
    pub fn format_decimal_optional(value: Option<Decimal>, empty_value: &str) -> String {
        if let Some(value) = value {
            let monthly = value / Decimal::from(12);
            let yearly = value;

            // Check if monthly has cents
            let monthly_str = if monthly.fract().is_zero() {
                format!("{}/mo", format_currency_us_0(monthly.trunc()))
            } else {
                format!("{}/mo", format_currency_us_0(monthly))
            };

            // Check if yearly has cents
            let yearly_str = if yearly.fract().is_zero() {
                format!("{}/yr", format_currency_us_0(yearly.trunc()))
            } else {
                format!("{}/yr", format_currency_us_0(yearly))
            };

            format!("{} {}", monthly_str, yearly_str)
        } else {
            empty_value.to_string()
        }
    }

    // Return date in the format: '25 Jun
    pub fn date_formatted_yy_mon(year: u32, month: u32) -> String {
        format!(
            "'{} {}",
            year % 100,
            month_number_to_abbr(month).unwrap_or("")
        )
    }

    pub fn month_number_to_abbr(month: u32) -> Option<&'static str> {
        match month {
            1 => Some("Jan"),
            2 => Some("Feb"),
            3 => Some("Mar"),
            4 => Some("Apr"),
            5 => Some("May"),
            6 => Some("Jun"),
            7 => Some("Jul"),
            8 => Some("Aug"),
            9 => Some("Sep"),
            10 => Some("Oct"),
            11 => Some("Nov"),
            12 => Some("Dec"),
            _ => None,
        }
    }

    // Helper function to get date tuple
    fn get_date_tuple(months: u32, start_month: u32, start_year: u32) -> (u32, u32) {
        let mut new_month = start_month + months;
        let new_year = start_year + (new_month - 1) / 12;
        new_month = ((new_month - 1) % 12) + 1;
        (new_month, new_year)
    }

    // Helper function to add months to a date
    fn add_months(date: NaiveDate, months: u32) -> NaiveDate {
        let mut year = date.year() as u32;
        let mut month = date.month() + months;

        while month > 12 {
            month -= 12;
            year += 1;
        }

        while month < 1 {
            month += 12;
            year -= 1;
        }

        // Create new date with same day (or last day of month if day is invalid)
        let last_day = get_last_day_of_month(year, month);
        let day = std::cmp::min(date.day(), last_day);

        NaiveDate::from_ymd_opt(year as i32, month, day).unwrap_or(date)
    }

    // Helper function to get the last day of a month
    fn get_last_day_of_month(year: u32, month: u32) -> u32 {
        match month {
            1 | 3 | 5 | 7 | 8 | 10 | 12 => 31,
            4 | 6 | 9 | 11 => 30,
            2 => {
                if is_leap_year(year) {
                    29
                } else {
                    28
                }
            }
            _ => 30, // Default for invalid months
        }
    }

    // Helper function to check if a year is a leap year
    fn is_leap_year(year: u32) -> bool {
        (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)
    }

    // TODO: VERIFY generate_plan with Smart python code
    fn generate_plan(debt_info: &mut DebtInfo, run_date: Option<NaiveDate>) -> Result<(), String> {
        if debt_info.debt_list.is_empty() {
            return Err("You must have at least one debt entered.".to_string());
        }

        if debt_info.monthly_insurance_premium.is_none() {
            return Err("Please add the Annual Insurance Premium.".to_string());
        }

        if debt_info.monthly_insurance_pua.is_none() {
            return Err("Please add the Annual Insurance PUA.".to_string());
        }

        // Month name mapping
        let ym: HashMap<u32, &str> = [
            (1, "Jan"),
            (2, "Feb"),
            (3, "Mar"),
            (4, "Apr"),
            (5, "May"),
            (6, "Jun"),
            (7, "Jul"),
            (8, "Aug"),
            (9, "Sep"),
            (10, "Oct"),
            (11, "Nov"),
            (12, "Dec"),
        ]
        .iter()
        .cloned()
        .collect();

        // Helper function to get date string
        let get_date = |months: u32| -> String {
            let (new_month, new_year) =
                get_date_tuple(months, debt_info.start_month, debt_info.start_year);
            format!("{}\u{2011}{}", ym[&new_month], new_year) // Non-breaking hyphen
        };

        let today = run_date.unwrap_or_else(|| Local::now().date_naive());

        // Calculate program start date
        let current_date = run_date.unwrap_or_else(|| Local::now().date_naive());
        let _current_month = current_date.month();
        let _current_year = current_date.year() as u32;

        // Calculate next month and following month dates
        let next_month_date = add_months(current_date, 1);
        let next_month = next_month_date.month();
        let next_month_year = next_month_date.year() as u32;

        let following_month_date = add_months(current_date, 2);
        let following_month = following_month_date.month();
        let following_month_year = following_month_date.year() as u32;

        // Generate month-by-month plan (simplified)
        debt_info.plan = Vec::new();
        let all_paid = false;
        //let mut last_month_plan: Option<MonthPlan> = None;

        for i in 1..361 {
            if all_paid {
                break;
            }

            // calculate month start date considering program start date stored in debt_info
            let (month, year) = get_date_tuple(i, debt_info.start_month, debt_info.start_year);
            let month_start_date = NaiveDate::from_ymd_opt(year as i32, month, 1).unwrap();

            let mut month_plan = MonthPlan {
                month: i,
                month_text: get_date(i),
                month_year_tuple: MonthYear { month, year },
                debt_list: Vec::new(),
                premium_total: Decimal::ZERO,
                premium_pua: Decimal::ZERO,
                premium_base: Decimal::ZERO,
                budget_total: Decimal::ZERO,
                debt_starting_balance: Decimal::ZERO,
                debt_ending_balance: Decimal::ZERO,
                loan_starting_balance: Decimal::ZERO,
                loan_draw: Decimal::ZERO,
                loan_payed: Decimal::ZERO,
                budget_premium: Decimal::ZERO,
                budget_minimum_payments: Decimal::ZERO,
                budget_extra_payments: Decimal::ZERO,
                budget_repayment: Decimal::ZERO,
                budget_remaining: Decimal::ZERO,
                pua_added: Decimal::ZERO,
                pua_extra: Decimal::ZERO,
                pua_balance: Decimal::ZERO,
                debt_interest_charge: Decimal::ZERO,
                debt_paid_down: Decimal::ZERO,
                cash_value: Decimal::ZERO,
                cash_net: Decimal::ZERO,
                sms_map: HashMap::new(),
            };

            // Initialize debt list for this month
            month_plan.debt_list = debt_info
                .debt_list
                .iter()
                .map(|d| MonthDebt {
                    full_name: d.full_name.clone(),
                    starting_balance: Decimal::ZERO,
                    interest_charge: Decimal::ZERO,
                    minimum_payment: Decimal::ZERO,
                    extra_payment: Decimal::ZERO,
                    payoff_payment: Decimal::ZERO,
                    ending_balance: Decimal::ZERO,
                })
                .collect();

            // Set premium values
            month_plan.premium_total = debt_info.monthly_insurance_premium.unwrap_or(Decimal::ZERO);
            month_plan.premium_pua = debt_info.monthly_insurance_pua.unwrap_or(Decimal::ZERO);
            month_plan.premium_base = month_plan.premium_total - month_plan.premium_pua;

            // Calculate budget
            month_plan.budget_total = debt_info.monthly_budget.unwrap_or(Decimal::ZERO);

            // Process each debt (simplified)
            let last_month = debt_info.plan.last();

            for (debt_idx, debt) in debt_info.debt_list.iter_mut().enumerate() {
                let month_debt = &mut month_plan.debt_list[debt_idx];

                // Calculate starting balance
                month_debt.starting_balance = if let Some(last) = last_month {
                    last.debt_list[debt_idx].ending_balance
                } else {
                    debt.balance
                };

                // If debt is already paid off, skip
                if month_debt.starting_balance <= Decimal::ZERO {
                    month_debt.ending_balance = Decimal::ZERO;
                    continue;
                }

                // Calculate interest
                month_debt.interest_charge =
                    (month_debt.starting_balance * debt.interest_rate * Decimal::new(1, 2)
                        / Decimal::new(12, 0))
                    .round_dp(2);

                // Update debt payoff interest
                debt.payoff_interest += month_debt.interest_charge;

                // Calculate minimum payment
                month_debt.minimum_payment = std::cmp::min(
                    debt.monthly_payment,
                    month_debt.starting_balance + month_debt.interest_charge,
                );

                // Calculate ending balance (simplified)
                month_debt.ending_balance = month_debt.starting_balance
                    + month_debt.interest_charge
                    - month_debt.minimum_payment;

                // Check if debt is paid off for the first time
                if month_debt.ending_balance <= Decimal::ZERO && debt.payoff_month.is_none() {
                    debt.payoff_month = Some(month_plan.month);
                    debt.payoff_date = Some(get_date(month_plan.month));
                    // format payoff_date as '23, May. Use month_start_date
                    debt.payoff_date_formatted = Some(format!(
                        "'{}, {}",
                        month_start_date.year() % 100,
                        month_number_to_abbr(month_start_date.month()).unwrap_or("")
                    ));

                    //println!("xxxxxxxxxxxxx {}: {} - {} - {} - IS PAYED OFF {}\n", debt.full_name, debt.payoff_date_formatted.as_ref().unwrap(), month_start_date, today, month_start_date <= today);

                    // NEW FIELD TO POWER RUST UI
                    debt.is_paid_off_as_of_today = month_start_date <= today;
                }

                // Calculate estimated cash value  - it's a pua balance on the policy on today's date
                if month_start_date <= today {
                    debt_info.esimated_cash_value += month_plan.premium_pua;
                }

                // Calculate effective interest cost percent
                debt.effective_interest_cost_percent =
                    if month_debt.starting_balance > Decimal::ZERO {
                        ((month_debt.interest_charge / month_debt.starting_balance)
                            * Decimal::new(100, 0))
                        .round_dp(2)
                    } else {
                        Decimal::ZERO
                    };
            } // end for debt

            // Calculate month summary values
            month_plan.debt_starting_balance = month_plan
                .debt_list
                .iter()
                .map(|d| d.starting_balance)
                .sum();
            month_plan.debt_ending_balance =
                month_plan.debt_list.iter().map(|d| d.ending_balance).sum();

            // Check if this is the next month
            if month == next_month && year == next_month_year {
                // Create payoff info list
                let payoff_info_list: Vec<String> = month_plan
                    .debt_list
                    .iter()
                    .filter_map(|debt| {
                        if debt.payoff_payment > Decimal::ZERO {
                            Some(format!("{} : ${:.2}", debt.full_name, debt.payoff_payment))
                        } else {
                            None
                        }
                    })
                    .collect();

                // Create extra payment info list
                let this_month_extra: Vec<String> = month_plan
                    .debt_list
                    .iter()
                    .filter_map(|debt| {
                        if debt.extra_payment > Decimal::ZERO {
                            Some(format!("{}: ${:.2}", debt.full_name, debt.extra_payment))
                        } else {
                            None
                        }
                    })
                    .collect();

                // Store next month info
                debt_info.upcoming_changes.next_month = Some(MonthChange {
                    starting_balance: month_plan.debt_starting_balance,
                    ending_balance: month_plan.debt_ending_balance,
                    interest_charge: month_plan.debt_list.iter().map(|d| d.interest_charge).sum(),
                    minimum_payment: month_plan.debt_list.iter().map(|d| d.minimum_payment).sum(),
                    this_month_extra,
                    payoff: payoff_info_list,
                });
            }

            // Check if this is the following month
            if month == following_month && year == following_month_year {
                // Create payoff info list
                let payoff_info_list: Vec<String> = month_plan
                    .debt_list
                    .iter()
                    .filter_map(|debt| {
                        if debt.payoff_payment > Decimal::ZERO {
                            Some(format!("{} : ${:.2}", debt.full_name, debt.payoff_payment))
                        } else {
                            None
                        }
                    })
                    .collect();

                // Create extra payment info list
                let this_month_extra: Vec<String> = month_plan
                    .debt_list
                    .iter()
                    .filter_map(|debt| {
                        if debt.extra_payment > Decimal::ZERO {
                            Some(format!("{}: ${:.2}", debt.full_name, debt.extra_payment))
                        } else {
                            None
                        }
                    })
                    .collect();

                // Store following month info
                debt_info.upcoming_changes.following_month = Some(MonthChange {
                    starting_balance: month_plan.debt_starting_balance,
                    ending_balance: month_plan.debt_ending_balance,
                    interest_charge: month_plan.debt_list.iter().map(|d| d.interest_charge).sum(),
                    minimum_payment: month_plan.debt_list.iter().map(|d| d.minimum_payment).sum(),
                    this_month_extra,
                    payoff: payoff_info_list,
                });
            }

            debt_info.plan.push(month_plan);

            // TODO: CHECK BELOW
            // Bail out if we are at the end
            //if month_plan.debt_ending_balance <= Decimal::ZERO /* && month_plan.loan_ending_balance <= Decimal::ZERO */ {
            if false {
                break;
            }
        } // end for loop

        // Calculate total paid and total unpaid amounts
        debt_info.total_paid = debt_info
            .plan
            .iter()
            .map(|m| m.premium_total + m.budget_total)
            .sum();
        debt_info.total_unpaid = debt_info.debt_balance.unwrap_or_default() - debt_info.total_paid;

        Ok(())
    }
}
