pub mod api;
pub mod web;

pub trait App: approck::App + approck_postgres::App + auth_fence::App {}

pub trait Identity: approck::Identity + auth_fence::Identity {
    fn web_usage(&self) -> bool;
    fn api_usage(&self) -> bool;

    fn agent_list(&self) -> bool;
    fn agent_add(&self) -> bool;
    fn agent_read(&self, agent_uuid: granite::Uuid) -> bool;
    fn agent_write(&self, agent_uuid: granite::Uuid) -> bool;
}

pub trait Document: bux::document::Cliffy {}

pub trait DocumentPublic: bux::document::Base {}
