#[approck::api]
pub mod admin_advisor_reassign_identity {

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub identity_uuid: Option<Uuid>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.advisor_write(input.advisor_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to reassign advisor identity".to_string(),
            ));
        }

        let dbcx = app.postgres_dbcx().await?;

        // Update the advisor's identity_uuid
        granite::pg_execute!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
                $identity_uuid: &input.identity_uuid,
            };
            UPDATE
                df4l.advisor
            SET
                identity_uuid = $identity_uuid
            WHERE
                advisor_uuid = $advisor_uuid
        )
        .await?;

        let message = match input.identity_uuid {
            Some(_) => "Identity successfully assigned to advisor".to_string(),
            None => "Identity successfully removed from advisor".to_string(),
        };

        Ok(Response::Output(Output {
            detail_url: crate::ml_advisor(input.advisor_uuid),
            message,
        }))
    }
}
