#[approck::api]
pub mod admin_client_detail {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
        pub advisor_esid: String,
        pub advisor_name: String,
        pub advisor_link: String,
        pub create_ts: DateTimeUtc,
        pub first_name: String,
        pub last_name: String,
        pub name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub country: Option<String>,
        pub active: bool,
        pub note: Option<String>,
    }
    impl Output {
        pub fn advisor_name(&self) -> String {
            format!("Advisor {}", self.advisor_uuid)
        }

        pub fn address(&self) -> String {
            let mut parts = Vec::new();

            if let Some(addr1) = &self.address1 {
                parts.push(addr1.clone());
            }
            if let Some(addr2) = &self.address2 {
                parts.push(addr2.clone());
            }
            if let Some(city) = &self.city {
                parts.push(city.clone());
            }
            if let Some(state) = &self.state {
                parts.push(state.clone());
            }
            if let Some(zip) = &self.zip {
                parts.push(zip.clone());
            }
            if let Some(country) = &self.country {
                parts.push(country.clone());
            }

            parts.join(" ")
        }
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.client_read(input.client_uuid) {
            return_authorization_error!(
                "insufficient permissions to advisor {}",
                input.client_uuid
            );
        }

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_uuid: Uuid,
                advisor_uuid: Uuid,
                advisor_esid: String,
                advisor_name: String,
                create_ts: DateTimeUtc,
                first_name: String,
                last_name: String,
                name: String,
                email: Option<String>,
                phone: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,
                country: Option<String>,
                active: bool,
                note: Option<String>,
            };
            SELECT
                c.client_uuid,
                a.advisor_uuid,
                a.advisor_esid,
                a.first_name || " " || a.last_name AS advisor_name,
                c.create_ts,
                c.first_name,
                c.last_name,
                c.first_name || " " || c.last_name AS name,
                c.email,
                c.phone,
                c.address1,
                c.address2,
                c.city,
                c.state,
                c.zip,
                c.country,
                c.active,
                c.note

            FROM
                df4l.client as c
                INNER JOIN df4l.advisor as a
                    ON c.advisor_uuid = a.advisor_uuid
            WHERE true
                AND c.client_uuid = $client_uuid::uuid
        )
        .await?;

        Ok(Output {
            client_uuid: row.client_uuid,
            advisor_uuid: row.advisor_uuid,
            advisor_esid: row.advisor_esid,
            advisor_name: row.advisor_name,
            advisor_link: crate::ml_advisor(row.advisor_uuid),
            create_ts: row.create_ts,
            first_name: row.first_name,
            last_name: row.last_name,
            name: row.name,
            email: row.email,
            phone: row.phone,
            address1: row.address1,
            address2: row.address2,
            city: row.city,
            state: row.state,
            zip: row.zip,
            country: row.country,
            active: row.active,
            note: row.note,
        })
    }
}
