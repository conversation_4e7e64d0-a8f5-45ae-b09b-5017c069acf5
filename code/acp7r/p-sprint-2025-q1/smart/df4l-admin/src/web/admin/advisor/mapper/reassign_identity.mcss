/* Advisor Identity Reassignment Page Styling */

/* Fixed width for action panel as per user preferences */
bux-action-panel {
    width: 42rem;
    min-width: 42rem;
}

/* Current assignment section */
.current-assignment {
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;

    h3 {
        margin-top: 0;
        margin-bottom: 0.75rem;
        font-size: 1.125rem;
        font-weight: 600;
        color: #495057;
    }

    p {
        margin-bottom: 0.5rem;
        font-size: 0.875rem;

        &:last-child {
            margin-bottom: 0;
        }

        &.no-assignment {
            color: #6c757d;
            font-style: italic;
        }



        &.identity-uuid {
            font-size: 0.8rem;
            color: #6c757d;
        }

        strong {
            font-weight: 600;
            color: #495057;
        }

        code {
            font-family: monospace;
            background-color: #e9ecef;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.8rem;
            color: #495057;
        }
    }

    .current-identity-details {
        padding: 0.75rem;
        background-color: #f8fff9;
        border: 1px solid #c3e6cb;
        border-radius: 0.375rem;
        border-left: 4px solid #28a745;
    }
}

/* Identity picker section */
.identity-picker {
    margin-bottom: 1.5rem;
}

/* Override entity picker styles for fixed width and left alignment */
bux-component-entity-picker {
    width: 100%;

    h2 {
        font-size: 18px;
        color: #555;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    ul {
        li {
            width: 100%;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                background-color: #f8f9fa;
                border-color: #007bff;
            }

            &.selected {
                background-color: #e6f0ff;
                border-color: #007bff;

                .entity-name {
                    color: #0056b3;
                    font-weight: 600;
                }

                .entity-role {
                    color: #0056b3;
                    font-weight: 600;
                }
            }

            .entity-info {
                text-align: left;
                justify-content: flex-start;
                gap: 12px;
                flex: 1;
            }

            .entity-icon {
                font-size: 24px;
                min-width: 30px;
                text-align: center;
            }

            .entity-role {
                font-size: 12px;
                color: #666;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                min-width: 80px;
                text-align: left;
            }

            .entity-name {
                font-size: 18px;
                font-weight: bold;
                color: #007bff;
                text-decoration: none;
                flex: 1;
                text-align: left;

                &:hover {
                    text-decoration: none;
                }
            }

            .entity-uuid {
                font-size: 12px;
                color: #999;
                font-family: monospace;
                margin-left: auto;
                flex-shrink: 0;
            }
        }
    }
}

/* Special styling for remove identity option */
bux-component-entity-picker ul li:first-child {
    border-color: #dc3545;
    background-color: #fff5f5;

    &:hover {
        background-color: #ffe6e6;
        border-color: #dc3545;
    }

    &.selected {
        background-color: #f8d7da;
        border-color: #dc3545;
        box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);

        .entity-name {
            color: #721c24;
            font-weight: 600;
        }

        .entity-role {
            color: #721c24;
            font-weight: 600;
        }
    }

    .entity-name {
        color: #dc3545;
    }

    .entity-role {
        color: #dc3545;
    }
}


