import "./edit.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/textarea/string.mts";
import "@addr-iso/input/address_us_select.mts";

import { SE } from "@granite/lib.mts";
import { admin_client_edit } from "@crate/api/admin/client/editλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import FormPanel from "@bux/component/form_panel.mts";
import { AddressUsSelect } from "@addr-iso/input/address_us_select.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $client_uuid: HTMLInputElement = SE($form, "[name=client_uuid]");
const $first_name: BuxInputTextString = SE($form, "[name=first_name]");
const $last_name: BuxInputTextString = SE($form, "[name=last_name]");
const $email: BuxInputTextString = SE($form, "[name=email]");
const $phone: BuxInputTextString = SE($form, "[name=phone]");
const $address1: BuxInputTextString = SE($form, "[name=address1]");
const $address2: BuxInputTextString = SE($form, "[name=address2]");
const $city: BuxInputTextString = SE($form, "[name=city]");
const $state: AddressUsSelect = SE($form, "[name=state]");
const $note: BuxInputTextareaString = SE($form, "[name=note]");

new FormPanel({
    $form,
    api: admin_client_edit.api,
    on_cancel: go_back,

    err: (errors) => {
        $first_name.set_e(errors.first_name);
        $last_name.set_e(errors.last_name);
        $email.set_e(errors.email);
        $phone.set_e(errors.phone);
        $address1.set_e(errors.address1);
        $address2.set_e(errors.address2);
        $city.set_e(errors.city);
        $state.set_e(errors.state);
        $note.set_e(errors.note);
    },

    get: () => {
        return {
            client_uuid: $client_uuid.value,
            first_name: $first_name.value,
            last_name: $last_name.value,
            email: $email.value_option,
            phone: $phone.value_option,
            address1: $address1.value_option,
            address2: $address2.value_option,
            city: $city.value_option,
            state: $state.value_option,
            note: $note.value_option,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});
