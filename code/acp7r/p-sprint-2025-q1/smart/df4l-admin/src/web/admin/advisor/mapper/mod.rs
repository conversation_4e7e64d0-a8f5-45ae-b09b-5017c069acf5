pub mod add_agency;
pub mod client;
pub mod client0;
pub mod edit;
pub mod index;
pub mod reassign_identity;

#[approck::prefix(/admin/advisor/{advisor_uuid:Uuid}/)]
pub mod prefix {
    pub fn menu(app: App, menu: Menu, advisor_uuid: Uuid) {
        menu.set_label_name_uri(
            "Advisor Details",
            app.uuid_to_label(advisor_uuid),
            &crate::ml_advisor(advisor_uuid),
        );
        menu.add_link(
            "Clients",
            &crate::ml_admin_advisor_client_list(advisor_uuid),
        );
        menu.add_link(
            "Legacy Clients",
            &crate::ml_admin_advisor_client0_list(advisor_uuid),
        );
        menu.add_link(
            "Reassign Identity",
            &format!("/admin/advisor/{}/reassign-identity", advisor_uuid),
        );
    }
    pub fn auth() {}
}
