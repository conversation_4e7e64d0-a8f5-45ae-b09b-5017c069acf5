#[approck::http(GET /admin/advisor/{advisor_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;

        use crate::api::admin::advisor::detail::admin_advisor_detail;

        let advisor = admin_advisor_detail::call(
            app,
            identity,
            admin_advisor_detail::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        doc.page_nav_edit_record(
            "Edit Advisor",
            &crate::ml_advisor_edit(advisor.advisor_uuid),
        );
        doc.page_nav_delete_record(
            "Delete Advisor",
            &crate::ml_advisor_delete(advisor.advisor_uuid),
        );
        doc.set_title("Advisor Details");

        let mut table = bux::component::info_table(advisor);
        table.set_heading("Advisor Information");
        table.add_row("Advisor Name:", |a| html! { (a.name()) });
        table.add_row("Advisor ID:", |a| html! { (a.advisor_esid) });
        table.add_active_status_row("Advisor Status:", |a| a.active);
        table.add_row(
            "Agency Name:",
            |a| {
                if a.agency_uuid.is_some() {
                    html! {
                        a href=(crate::ml_agency(a.agency_uuid.unwrap_or_default())) {
                            (a.agency_name.clone().unwrap_or_default()) " (" (a.agency_esid.clone().unwrap_or_default()) ")"
                        }
                    }
                } else {
                    html! {
                        "N/A "
                        a href=(crate::ml_advisor_add_agency(a.advisor_uuid)) {
                            "Assign Agency"
                        }
                    }
                }
            },
        );
        table.add_row(
            "GBU ID:",
            |a| html! { (a.gbu_advisor_esid.as_deref().unwrap_or("")) },
        );
        table.add_row("Email:", |a| html! { (a.email.as_deref().unwrap_or("")) });
        table.add_row("Phone:", |u| html! { (u.phone.as_deref().unwrap_or("")) });
        table.add_row("Address:", |u| html! { (u.address()) });
        table.add_row("States Of Licensure: ", |u| html! { (u.statelic_html()) });

        doc.add_body(html!((table)));

        Ok(Response::HTML(doc.into()))
    }
}
