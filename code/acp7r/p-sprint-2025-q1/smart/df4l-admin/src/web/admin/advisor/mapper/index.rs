#[approck::http(GET /admin/advisor/{advisor_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        use crate::api::admin::advisor::detail::admin_advisor_detail;

        let advisor = admin_advisor_detail::call(
            app,
            identity,
            admin_advisor_detail::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        // Fetch identity details if advisor has an identity_uuid
        let identity_details = if let Some(identity_uuid) = advisor.identity_uuid {
            match auth_fence::api::admin::identity::detail::detail::call(
                app,
                identity,
                auth_fence::api::admin::identity::detail::detail::Input { identity_uuid },
            )
            .await
            {
                Ok(details) => Some(details),
                Err(e) => {
                    approck::warn!(
                        "Failed to fetch identity details for advisor {}: {}",
                        advisor.advisor_uuid,
                        e
                    );
                    None
                }
            }
        } else {
            None
        };

        // Fetch SSO providers if identity exists
        let sso_providers = if let Some(identity_uuid) = advisor.identity_uuid {
            match auth_fence::api::identity::sso::providers::index::call(
                app,
                identity,
                auth_fence::api::identity::sso::providers::index::Input { identity_uuid },
            )
            .await
            {
                Ok(providers) => Some(providers.providers),
                Err(e) => {
                    approck::warn!(
                        "Failed to fetch SSO providers for advisor {}: {}",
                        advisor.advisor_uuid,
                        e
                    );
                    None
                }
            }
        } else {
            None
        };

        doc.set_title("Advisor Details");

        // Prepare display values
        let display_email = advisor.email.as_deref().unwrap_or("Email not available");

        let display_phone = advisor.phone.as_deref().unwrap_or("Phone not available");

        // Prepare URLs for edit links
        let edit_url = crate::ml_advisor_edit(advisor.advisor_uuid);
        let add_agency_url = crate::ml_advisor_add_agency(advisor.advisor_uuid);
        let reassign_identity_url =
            format!("/admin/advisor/{}/reassign-identity", advisor.advisor_uuid);

        // Create separate InsightDeck instances for different logical groupings

        // 1. Advisor Identification InsightDeck (with action buttons in header)
        let mut advisor_identification =
            bux::component::insight_deck::InsightDeck::new("Advisor Identification");
        advisor_identification
            .description("Core advisor identifiers and agency assignment information.");

        // Add general action buttons to the header (edit/delete only)
        advisor_identification.add_button(bux::button::link::edit(&crate::ml_advisor_edit(
            advisor.advisor_uuid,
        )));
        advisor_identification.add_button(html!(" "));
        advisor_identification.add_button(bux::button::link::delete(&crate::ml_advisor_delete(
            advisor.advisor_uuid,
        )));

        advisor_identification.add_basic_tile(
            "fas fa-id-badge",
            "Advisor ID",
            html!((advisor.advisor_esid)),
        );

        advisor_identification.add_edit_tile(
            "fas fa-pen",
            "GBU Advisor ID",
            match &advisor.gbu_advisor_esid {
                Some(id) => html!((id)),
                None => html!("None"),
            },
            edit_url.clone(),
        );

        advisor_identification.add_edit_row(
           "fas fa-building",
           "Agency Assignment",
           html! {
               @if advisor.agency_uuid.is_some() {
                   a href=(crate::ml_agency(advisor.agency_uuid.unwrap_or_default())) {
                       (advisor.agency_name.clone().unwrap_or_default()) " (" (advisor.agency_esid.clone().unwrap_or_default()) ")"
                   }
               } @else {
                   "No agency assigned"
               }
           },
           if advisor.agency_uuid.is_some() {
               &edit_url
           } else {
               &add_agency_url
           }
       );

        advisor_identification.add_basic_tile(
            "fas fa-calendar-plus",
            "Created On",
            html!((advisor.create_ts.format("%b %d, %Y %I:%M %p"))),
        );

        // 2. Contact Information InsightDeck
        let mut contact_information =
            bux::component::insight_deck::InsightDeck::new("Contact Information");
        contact_information.description("Advisor contact details and address information.");

        contact_information.add_edit_row_email(advisor.email.as_deref(), &edit_url);
        contact_information.add_edit_row_phone(advisor.phone.as_deref(), &edit_url);
        contact_information.add_edit_row_address(
            if !advisor.address().trim().is_empty() {
                Some(advisor.address())
            } else {
                None
            }
            .as_deref(),
            &edit_url,
        );

        // 3. Administrative Information InsightDeck
        let mut administrative_info =
            bux::component::insight_deck::InsightDeck::new("Administrative Information");
        administrative_info
            .description("Status, licensing, and administrative details for this advisor.");

        administrative_info.add_edit_row(
            "fas fa-toggle-on",
            "Status",
            html! {
                @if advisor.active {
                    label-tag.success { "Active" }
                } @else {
                    label-tag.danger { "Inactive" }
                }
            },
            &edit_url,
        );

        administrative_info.add_edit_row(
            "fas fa-id-badge",
            "States of Licensure",
            html! {
                @if advisor.statelics.is_empty() {
                    label-tag.warning { "None" }
                } @else {
                    @for (i, statelic) in advisor.statelics.iter().enumerate() {
                        @if i > 0 {
                            " "
                        }
                        label-tag.primary { (statelic.label) }
                    }
                }
            },
            &edit_url,
        );

        // Admin note if available
        if let Some(admin_note) = &advisor.admin_note {
            if !admin_note.trim().is_empty() {
                administrative_info.add_edit_row(
                    "fas fa-sticky-note",
                    "Admin Note",
                    html!((admin_note)),
                    &edit_url,
                );
            }
        }

        // 4. Identity Information InsightDeck
        let mut identity_information =
            bux::component::insight_deck::InsightDeck::new("Identity Information");
        identity_information
            .description("Login credentials and account access details for this advisor.");

        // Add identity-related action buttons to the header
        if advisor.identity_uuid.is_some() {
            identity_information.add_button(bux::button::link::label_icon_class(
                "Reassign Identity",
                "fas fa-exchange-alt",
                &reassign_identity_url,
                "sm primary",
            ));
            identity_information.add_button(html!(" "));
            identity_information.add_button(bux::button::link::label_icon_class(
                "View Identity",
                "fas fa-eye",
                &auth_fence::api::admin::identity::types::ml_admin(
                    &advisor.identity_uuid.unwrap(),
                    "",
                ),
                "sm secondary",
            ));
        }

        identity_information.add_basic_row(
            "fas fa-user-circle",
            "Identity Status",
            html! {
                @if let Some(identity_uuid) = &advisor.identity_uuid {
                    a href=(auth_fence::api::admin::identity::types::ml_admin(identity_uuid, "")) {
                        (identity_uuid)
                    }
                } @else {
                    span.text-muted { "No identity linked (Login not available)" }
                }
            },
        );

        // Identity details if available
        if let Some(ref details) = identity_details {
            identity_information.add_basic_row(
                "fas fa-user",
                "Identity Name",
                html!((details.name)),
            );

            if let Some(ref email) = details.email {
                identity_information.add_basic_row(
                    "fas fa-envelope",
                    "Identity Email",
                    html!(a href=(format!("mailto:{}", email)) { (email) }),
                );
            }
        }

        // Sign-in methods if available
        if let Some(ref providers) = sso_providers {
            let google_provider = providers
                .iter()
                .find(|p| p.ssopro_xsid.to_lowercase() == "google");
            let microsoft_provider = providers
                .iter()
                .find(|p| p.ssopro_xsid.to_lowercase() == "microsoft");

            identity_information.add_basic_row(
                "fas fa-key",
                "Sign-in Methods",
                html! {
                    div.signin-methods {
                        @if let Some(google) = google_provider {
                            div.signin-method {
                                i.fab.fa-google aria-hidden="true" {}
                                span { "Google: " }
                                @if google.is_connected {
                                    label-tag.success { "Enabled" }
                                } @else {
                                    label-tag.warning { "Disabled" }
                                }
                            }
                        } @else {
                            div.signin-method {
                                i.fab.fa-google aria-hidden="true" {}
                                span { "Google: " }
                                label-tag.secondary { "Not Available" }
                            }
                        }


                        @if let Some(microsoft) = microsoft_provider {
                            div.signin-method {
                                i.fab.fa-microsoft aria-hidden="true" {}
                                span { "Microsoft: " }
                                @if microsoft.is_connected {
                                    label-tag.success { "Enabled" }
                                } @else {
                                    label-tag.warning { "Disabled" }
                                }
                            }
                        } @else {
                            div.signin-method {
                                i.fab.fa-microsoft aria-hidden="true" {}
                                span { "Microsoft: " }
                                label-tag.secondary { "Not Available" }
                            }
                        }


                        div.signin-method {
                            i.fas.fa-lock aria-hidden="true" {}
                            span { "Login/Password: " }
                            @if identity_details.is_some() {
                                label-tag.success { "Enabled" }
                            } @else {
                                label-tag.warning { "Unknown" }
                            }
                        }
                    }
                },
            );
        }

        doc.add_body(html!(
           admin-advisor-detail {
               grid-12 {
                   cell-3 {
                       panel {
                           content {
                               contact-info {
                                   h1 { (advisor.name()) }
                                   p.phone.mb-0 {
                                       (display_phone)
                                   }
                                   p.email {
                                       @if let Some(email) = &advisor.email {
                                           a href=(format!("mailto:{}", email)) { (email) }
                                       } @else {
                                           (display_email)
                                       }
                                   }
                                   a.btn.primary.block href=(df4l_advisor::ml_advisor(advisor.advisor_uuid)) { "Visit Advisor Dashboard" }
                                   hr;
                                   @if advisor.active {
                                       label-tag.success { "Active Advisor" }
                                   } @else {
                                       label-tag.danger { "Inactive Advisor" }
                                   }
                               }
                           }
                       }
                   }
                   cell-9 {
                       // This is the new rendering implementation for advisor details using the insight deck
                       @for deck in &[&advisor_identification, &identity_information, &contact_information, &administrative_info] {
                           (deck)
                       }
                   }
               }
           }
       ));
        Ok(Response::HTML(doc.into()))
    }
}
