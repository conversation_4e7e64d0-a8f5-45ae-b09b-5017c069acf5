#[approck::http(GET /admin/client/add; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("Add Client");

        let mut form_panel =
            bux::component::add_cancel_form_panel("Add New Client", &crate::ml_client_list());

        use crate::api::admin::advisor::list::admin_advisor_list;

        let advisors = admin_advisor_list::call(
            app,
            identity,
            admin_advisor_list::Input {
                keyword: None,
                active: Some(true),
            },
        )
        .await?;

        // Format advisors for dropdown: "Name (ESID)"
        let advisor_options_strings: Vec<(String, String)> = advisors
            .advisor_list
            .iter()
            .map(|a| {
                let display = format!("{} {}", a.first_name, a.last_name);
                (a.advisor_uuid.to_string(), display)
            })
            .collect();

        // Convert to the expected type for nilla_select
        let advisor_options: Vec<(&str, &str)> = advisor_options_strings
            .iter()
            .map(|(id, name)| (id.as_str(), name.as_str()))
            .collect();

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            grid-12 {
                cell-6 {
                    (bux::input::select::nilla::nilla_select(
                        "advisor_uuid",
                        "Advisor",
                        &advisor_options,
                        None,
                    ))
                    (bux::input::text::string::name_label_value("first_name", "Client First Name:", None))
                    (bux::input::text::string::name_label_value("last_name", "Client Last Name:", None))
                    (bux::input::text::string::name_label_value("email", "Email:", None))
                    (bux::input::text::string::name_label_value_help("phone", "Send Text Messages To Phone Number", None, "Do not include country code."))
                }
                cell-6 {
                    (bux::input::text::string::name_label_value("address1", "Address Line 1", None))
                    (bux::input::text::string::name_label_value("address2", "Address Line 2", None))
                    grid-3 {
                        (bux::input::text::string::name_label_value("city", "City", None))
                        (addr_iso::input::address_us_select::us_state_select_with_help(app, "state", "State", None, "").await?)
                        (bux::input::text::string::name_label_value("zip", "ZIP", None))
                    }
                    (bux::input::textarea::string::name_label_value("note", "Note:", None))

                }
            }
        ));
        doc.add_body(html! {
            section {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}
