#[approck::http(GET /admin/client/{client_uuid:Uuid}/edit?keyword=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::client::detail::admin_client_detail;
        use maud::html;

        let client = admin_client_detail::call(
            app,
            identity,
            admin_client_detail::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        doc.set_title(&format!("Edit Client: {}", client.name));

        let mut form_panel = bux::component::save_cancel_form_panel(
            &format!("Edit Client: {}", client.name),
            &crate::ml_client(client.client_uuid),
        );

        form_panel.set_hidden("client_uuid", path.client_uuid);

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            grid-12 {
                cell-6 {
                    (bux::input::text::string::name_label_value("first_name", "Client First Name:", Some(&client.first_name)))
                    (bux::input::text::string::name_label_value("last_name", "Client Last Name:", Some(&client.last_name)))
                    (bux::input::text::string::name_label_value("email", "Email:", Some(&client.email.unwrap_or_default())))
                    (bux::input::text::string::name_label_value("phone", "Send Text Messages To Phone Number:", Some(&client.phone.unwrap_or_default())))
                    }
                cell-6 {
                    (bux::input::text::string::name_label_value("address1", "Address Line 1", client.address1.as_deref()))
                    (bux::input::text::string::name_label_value("address2", "Address Line 2", client.address2.as_deref()))
                    grid-3 {
                        (bux::input::text::string::name_label_value("city", "City", client.city.as_deref()))
                        (addr_iso::input::address_us_select::us_state_select_with_help(app, "state", "State", client.state.as_deref(), "").await?)
                        (bux::input::text::string::name_label_value("zip", "ZIP", client.zip.as_deref()))
                    }
                    (bux::input::textarea::string::name_label_value("note", "Note:", Some(&client.note.unwrap_or_default())))

                }
            }
        ));
        doc.add_body(html! {
            section {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}
