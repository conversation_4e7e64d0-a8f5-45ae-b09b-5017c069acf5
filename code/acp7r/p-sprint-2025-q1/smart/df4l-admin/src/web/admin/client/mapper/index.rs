#[approck::http(GET /admin/client/{client_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::client::detail::admin_client_detail;
        use maud::html;

        let client = admin_client_detail::call(
            app,
            identity,
            admin_client_detail::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        doc.set_title("Client Details");

        // Prepare display values
        let display_email = client.email.as_deref().unwrap_or("Email not available");
        let display_phone = client.phone.as_deref().unwrap_or("Phone not available");

        doc.add_body(html!(
            admin-client-detail {
                grid-12 {
                    cell-3 {
                        panel {
                            content {
                                contact-info {
                                    h1 { (client.name) }
                                    p.phone.mb-0 {
                                        (display_phone)
                                    }
                                    p.email {
                                        @if let Some(email) = &client.email {
                                            a href=(format!("mailto:{}", email)) { (email) }
                                        } @else {
                                            (display_email)
                                        }
                                    }
                                    hr;
                                    @if client.active {
                                        label-tag.success { "Active Client" }
                                    } @else {
                                        label-tag.danger { "Inactive Client" }
                                    }
                                }
                            }
                        }
                    }
                    cell-9 {
                        // Client Identification Panel
                        panel.client-identification {
                            header {
                                div {
                                    h5 { "Client Identification" }
                                    p { "Core client identifiers and account creation information." }
                                }
                            }
                            content {
                                // Top section with Client UUID and Created Date
                                grid-2 {
                                    panel.gray-tile {
                                        content {
                                            hbox {
                                                i.far.fa-id-badge.client-id aria-hidden="true" {}
                                                dl {
                                                    dt { "Client UUID" }
                                                    dd { (client.client_uuid) }
                                                }
                                            }
                                        }
                                    }
                                    panel.gray-tile {
                                        content {
                                            hbox {
                                                i.fas.fa-calendar-plus aria-hidden="true" {}
                                                dl {
                                                    dt { "Created On" }
                                                    dd {
                                                        (client.create_ts.format("%b %d, %Y %I:%M %p"))
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        // Contact Information Panel
                        panel.contact-information {
                            header {
                                div {
                                    h5 { "Contact Information" }
                                    p { "Client contact details and address information." }
                                }
                                (bux::button::link::label_icon_class("Edit", "fas fa-pencil-alt", &crate::ml_client_edit(client.client_uuid), "sm primary"))
                            }
                            content {
                                ul.data-list {
                                    li {
                                        hbox {
                                            i.fas.fa-envelope-open-text aria-hidden="true" {}
                                            dl {
                                                dt { "Email" }
                                                dd {
                                                    @if let Some(email) = &client.email {
                                                        a href=(format!("mailto:{}", email)) { (email) }
                                                    } @else {
                                                        "No email provided"
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-phone-alt aria-hidden="true" {}
                                            dl {
                                                dt { "Phone" }
                                                dd {
                                                    @if let Some(phone) = &client.phone {
                                                        (phone)
                                                    } @else {
                                                        "No phone provided"
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-map-marker-alt aria-hidden="true" {}
                                            dl {
                                                dt { "Address" }
                                                dd {
                                                    @if !client.address().trim().is_empty() {
                                                        (client.address())
                                                    } @else {
                                                        "No address provided"
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        // Administrative Information Panel
                        panel.administrative-information {
                            header {
                                div {
                                    h5 { "Administrative Information" }
                                    p { "Advisor assignment and administrative notes for this client." }
                                }
                                div.header-buttons {
                                    (bux::button::link::label_icon_class("Reassign Advisor", "fas fa-exchange-alt", &crate::ml_client_assign_advisor(client.client_uuid), "sm primary"))
                                    " "
                                    (bux::button::link::label_icon_class("View Advisor", "fas fa-eye", &client.advisor_link, "sm secondary"))
                                }
                            }
                            content {
                                ul.data-list {
                                    li {
                                        hbox {
                                            i.fas.fa-user-tie aria-hidden="true" {}
                                            dl {
                                                dt { "Assigned Advisor" }
                                                dd {
                                                    a href=(client.advisor_link) {
                                                        (format!("{} ({})", client.advisor_name, client.advisor_esid))
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    @if let Some(note) = &client.note {
                                        @if !note.trim().is_empty() {
                                            li {
                                                hbox {
                                                    i.fas.fa-sticky-note aria-hidden="true" {}
                                                    dl {
                                                        dt { "Admin Note" }
                                                        dd {
                                                            (note)
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
