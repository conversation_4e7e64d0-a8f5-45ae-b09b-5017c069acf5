#[approck::http(GET /admin/?name=String; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("Admin Dashboard");

        use crate::api::admin::dashboard::admin_dashboard;
        let dashboard_data = admin_dashboard::call(app).await?;

        let mut quick_info_box = bux::ui::quick_info_box::new();
        quick_info_box.icon_name_href_count(
            "🏛️",
            "Active Agencies",
            "/admin/agency/",
            dashboard_data.agency_count,
        );

        quick_info_box.icon_name_href_count(
            "👤",
            "Active Clients",
            "/admin/client/",
            dashboard_data.client_count,
        );
        quick_info_box.icon_name_href_count(
            "📋",
            "Active Legacy Clients",
            "/admin/client0/",
            dashboard_data.legacy_client_count,
        );
        quick_info_box.icon_name_href_count(
            "🎯",
            "Active Advisors",
            "/admin/advisor/",
            dashboard_data.advisor_count,
        );

        doc.add_body(html!(
            panel {
                content {
                    h2 { "Welcome, Admin!" }
                    p { "We hope you're having a great day. Here's a quick overview of the system." }
                    (quick_info_box)
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
