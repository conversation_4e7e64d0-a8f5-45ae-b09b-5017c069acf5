#[approck::http(GET /api/icover/client/{client_uuid:Uuid}/; AUTH None; return JSON|Empty;)]
pub mod get {
    use crate::api::client::detail as client_api;

    pub async fn request(identity: Identity, path: Path) -> Result<Response> {
        // check permissions
        if !identity.scope_d2c_read() {
            let response = Empty {
                status: approck::StatusCode::UNAUTHORIZED,
                ..Default::default()
            };
            return Ok(Response::Empty(response));
        }

        let input = client_api::get::Input {
            client_uuid: path.client_uuid,
        };

        match client_api::get::call(identity.as_ref(), input).await {
            Ok(output) => {
                approck::debug!(
                    "/api/icover/client/{{uuid}}/ : Agent and Client data loaded successfully"
                );

                // Client will not expect Some(value), etc.. so we're using serde instead
                Ok(Response::JSON(serde_json::json!(output).into()))
            }
            Err(e) => {
                approck::error!(
                    "/api/icover/client/{{uuid}} : Error getting agent or client data: {}",
                    e
                );

                let response = Empty {
                    status: approck::StatusCode::BAD_REQUEST,
                    ..Default::default()
                };
                return Ok(Response::Empty(response));
            }
        }
    }
}
