// Client and Policy Data Endpoint:
// Host: df4l-1.us-east-2.acp7.link
// GET /api/icover/client-policy/{client_uuid}
pub mod get {
    pub struct Input {
        pub client_uuid: granite::Uuid,
    }

    #[derive(serde::Serialize, Debug)]
    pub struct Output {
        pub agent: Agent,
        pub client: Client,
    }

    #[derive(serde::Serialize, Debug)]
    #[serde(rename_all = "camelCase")]
    pub struct Agent {
        pub agent_email: String,
        pub agent_first_name: String,
        pub agent_last_name: String,
        pub agent_contact_number: String,
        pub agent_access_level: String,
        pub agent_states_approved: Vec<AgentStateApproval>,
        pub client_agent_id: String, // GBU Agent ID
        pub agent_address: AgentAddress,
    }

    #[derive(serde::Serialize, Debug)]
    #[serde(rename_all = "camelCase")]
    pub struct AgentStateApproval {
        pub product_name: String, // Debt2Capital
        pub states: Vec<String>,
    }

    #[derive(serde::Serialize, Debug)]
    #[serde(rename_all = "camelCase")]
    pub struct AgentAddress {
        pub street_one: String,
        pub street_two: String,
        pub city: String,
        pub state: String,
        pub zipcode: String,
    }

    #[derive(serde::Serialize, Debug)]
    #[serde(rename_all = "camelCase")]
    pub struct Client {
        pub client_uuid: granite::Uuid,
        pub first_name: String,
        pub last_name: String,
        pub street_one: String,
        pub street_two: String,
        pub city: String,
        pub state: String,
        pub zipcode: String,
        pub phone: String,
        pub gender: String,
        pub birth_date: String,
        pub target_premium: granite::Decimal,
        pub pua_split: i32,
        pub pua_type: PuaType,
        pub pua_amount: granite::Decimal,
        pub pua_duration: i32,
    }

    #[derive(serde::Serialize, Debug)]
    #[serde(rename_all = "snake_case")]
    pub enum PuaType {
        LumpSum,
        Recurring,
    }

    impl std::fmt::Display for PuaType {
        fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
            match self {
                PuaType::LumpSum => write!(f, "lump_sum"),
                PuaType::Recurring => write!(f, "recurring"),
            }
        }
    }

    pub async fn call(identity: &impl crate::Identity, input: Input) -> granite::Result<Output> {
        if !identity.scope_d2c_read() {
            return Err(granite::authorization_error!(
                "insufficient permissions to read client policy information"
            ));
        }

        let _identity_uuid = match identity.identity_uuid() {
            Some(identity_uuid) => identity_uuid,
            None => {
                return Err(granite::authorization_error!("User not logged in"));
            }
        };

        // TODO: Look up advisor
        // TODO: Look up applicant/policy

        // For now, return mock data
        Ok(Output {
            agent: Agent {
                agent_email: "<EMAIL>".to_string(),
                agent_first_name: "John".to_string(),
                agent_last_name: "Doe".to_string(),
                agent_contact_number: "1234567891".to_string(),
                agent_access_level: "agent".to_string(),
                agent_states_approved: vec![AgentStateApproval {
                    product_name: "Debt2Capital".to_string(),
                    states: vec![
                        "CA".to_string(),
                        "MN".to_string(),
                        "OH".to_string(),
                        "WI".to_string(),
                    ],
                }],
                client_agent_id: "1111".to_string(),
                agent_address: AgentAddress {
                    street_one: "15511 Olive Suite".to_string(),
                    street_two: "Suite 100".to_string(),
                    city: "St. Louis".to_string(),
                    state: "MO".to_string(),
                    zipcode: "63017".to_string(),
                },
            },
            client: Client {
                client_uuid: input.client_uuid,
                first_name: "John".to_string(),
                last_name: "Doe".to_string(),
                street_one: "123 Main St".to_string(),
                street_two: "Apt 4B".to_string(),
                city: "Anytown".to_string(),
                state: "NY".to_string(),
                zipcode: "12345".to_string(),
                phone: "******-123-4567".to_string(),
                gender: "M".to_string(),
                birth_date: "1990-01-01".to_string(),
                target_premium: granite::Decimal::new(1000, 0),
                pua_split: 50,
                pua_type: PuaType::LumpSum,
                pua_amount: granite::Decimal::new(500, 0),
                pua_duration: 12,
            },
        })
    }
}
