use std::env;

#[test]
fn test_df4l_executable() {
    // Get the path to the current executable
    let current_exe = env::current_exe().expect("Failed to get current executable path");

    println!("Current executable path: {}", current_exe.display());

    // Get the target directory (parent of the test executable)
    let target_dir = current_exe
        .parent()
        .expect("Failed to get parent directory")
        .parent()
        .expect("Failed to get target directory");

    // Construct the path to the df4l executable
    // The executable is directly in the target/debug or target/release directory
    let df4l_exe = target_dir.join("df4l.toml");

    println!("df4l executable path: {}", df4l_exe.display());
    assert!(
        df4l_exe.exists(),
        "df4l executable not found at expected path"
    );
}
