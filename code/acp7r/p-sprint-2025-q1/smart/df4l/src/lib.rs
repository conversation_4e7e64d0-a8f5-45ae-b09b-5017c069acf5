#![allow(clippy::manual_map)]

#[path = "libλ.rs"]
pub mod libλ;

mod api;
mod module;
mod web;

use auth_fence_provider::sso::oauth2::token;
use std::collections::HashMap;

///////////////////////////////////////////////////////////////////////////////////////////////////
pub trait App: approck_postgres::App + auth_fence::App + addr_iso::App {}

pub trait Identity: approck::Identity + auth_fence::Identity {
    fn web_usage(&self) -> bool;
    fn api_usage(&self) -> bool;
}

pub trait Document: bux::document::Cliffy {}

pub fn ml_myaccount(identity_uuid: granite::Uuid) -> String {
    format!("/myaccount/{}/", identity_uuid)
}

///////////////////////////////////////////////////////////////////////////////////////////////////

#[derive(serde::Deserialize)]
pub struct AppConfig {
    pub redis: approck_redis::ModuleConfig,
    pub postgres: approck_postgres::ModuleConfig,
    pub webserver: approck::server::ModuleConfig,
    pub auth_fence: auth_fence::types::ModuleConfig,
    pub auth_fence_provider: auth_fence_provider::ModuleConfig,
    pub api_sendgrid: api_sendgrid::ModuleConfig,
    pub api_stripe: api_stripe::ModuleConfig,
    pub api_twilio: api_twilio::ModuleConfig,
}

pub struct AppStruct {
    pub webserver: approck::server::Module,
    pub postgres: approck_postgres::ModuleStruct,
    pub redis: approck_redis::ModuleStruct,
    pub auth_fence: auth_fence::types::ModuleStruct,
    pub auth_fence_provider: auth_fence_provider::ModuleStruct,
    pub api_sendgrid: api_sendgrid::ModuleStruct,
    pub api_stripe: api_stripe::ModuleStruct,
    pub api_twilio: api_twilio::ModuleStruct,
    pub legal_plane: legal_plane::ModuleStruct,
    pub uuid_cache: approck::UuidCache,
}

#[derive(Debug)]
pub struct IdentityStruct {
    request: RequestIdentity,
    auth_fence: Option<auth_fence::api::identity::Identity>,
    auth_fence_provider: Option<AuthFenceProviderTokenIdentity>,
    df4l_admin: Option<Df4lAdminIdentity>,
    df4l_advisor: Option<HashMap<granite::Uuid, Df4lAdvisorIdentity>>,
}

#[derive(Debug)]
pub struct RequestIdentity {
    remote_address: std::net::IpAddr,
    session_token: String,
}

#[derive(Debug)]
pub struct Df4lAdminIdentity {
    agency_read: bool,
    agency_write: bool,
    advisor_read: bool,
    advisor_write: bool,
    client_read: bool,
    client_write: bool,
}

#[derive(Debug)]
pub struct Df4lAdvisorIdentity {
    client_read: bool,
    client_write: bool,
}

#[derive(Debug)]
pub struct AuthFenceProviderTokenIdentity {
    scope_email: bool,
    scope_profile: bool,
    scope_d2c_read: bool,
}

pub use crate::web::Document::Document as DocumentStruct;
pub use crate::web::DocumentPublic::DocumentPublic as DocumentPublicStruct;

///////////////////////////////////////////////////////////////////////////////////////////////////

impl approck::App for AppStruct {
    type Identity = IdentityStruct;
    type Config = AppConfig;

    async fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            webserver: approck::server::Module::new(config.webserver).await?,
            postgres: approck_postgres::ModuleStruct::new(config.postgres).await?,
            redis: approck_redis::ModuleStruct::new(config.redis).await?,
            auth_fence: auth_fence::types::ModuleStruct::new(config.auth_fence).await?,
            auth_fence_provider: auth_fence_provider::ModuleStruct::new(config.auth_fence_provider)
                .await?,
            api_sendgrid: api_sendgrid::ModuleStruct::new(config.api_sendgrid).await?,
            api_stripe: api_stripe::ModuleStruct::new(config.api_stripe).await?,
            api_twilio: api_twilio::ModuleStruct::new(config.api_twilio).await?,
            legal_plane: legal_plane::ModuleStruct::new(legal_plane::ModuleConfig::default())
                .await?,
            uuid_cache: approck::UuidCache::default(),
        })
    }

    async fn init(&self) -> granite::Result<()> {
        use approck_postgres::App as _a;

        // get the crate name using the env! macro
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {}", crate_name);

        // TODO: this needs to be moved into a postgresql listener that we can pull initial results
        // and stream updates into
        let dbcx = self.postgres_dbcx().await.unwrap();
        let rows = granite::pg_row_vec!(
            db = dbcx;
            row = {
                uuid: Uuid,
                label: String,
            };
            SELECT advisor_uuid, first_name || " " || last_name FROM df4l.advisor
            UNION
            SELECT client_uuid, first_name || " " || last_name FROM df4l.client0
            UNION
            SELECT client_uuid, first_name || " " || last_name FROM df4l.client
            UNION
            SELECT client_debt_uuid, name FROM df4l.client0_debt
            UNION
            SELECT agency_uuid, name FROM df4l.agency
            UNION
            SELECT identity_uuid, name FROM auth_fence.identity
        )
        .await
        .expect("Error loading UUID labels");

        for row in rows {
            self.uuid_cache.set(row.uuid, row.label);
        }

        Ok(())
    }

    async fn auth(&self, req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        use approck_postgres::App as _a;
        use auth_fence::App as _b;
        let auth_fence = self.auth_fence_system();

        let mut redis = match self.redis.get_dbcx().await {
            Ok(redis) => redis,
            Err(e) => {
                return Err(granite::process_error!(
                    "Error getting Redis connection: {}",
                    e
                ));
            }
        };

        let request = RequestIdentity {
            remote_address: req.remote_ip(),
            session_token: req.session_token(),
        };

        match req.auth_bearer() {
            Some(bearer_token) => {
                // TODO: Log token usage
                // TODO: Rate limiting
                let ip_addr = req.remote_address().ip();
                approck::debug!(
                    "{} :: authenticate_request: Bearer token found in request from IP {}",
                    req.uri_string(),
                    ip_addr
                );

                // load the token
                // TODO: Validate the request ip against the ip in the token
                match token::load_access_token(&mut redis, &bearer_token).await {
                    Ok(access_token) => {
                        let dbcx = self.postgres_dbcx().await?;

                        // load the identity based on the identity_uuid in the token
                        let auth_fence = match auth_fence::api::identity::load(
                            &dbcx,
                            &access_token.identity_uuid,
                        )
                        .await
                        {
                            Ok(auth_fence) => auth_fence,
                            Err(e) => {
                                approck::error!("Error loading identity: {:?}", e);
                                return Err(granite::process_error!("Error loading identity")
                                    .add_context(e));
                            }
                        };

                        // load the scopes for this token into the identity struct
                        let auth_fence_provider = AuthFenceProviderTokenIdentity {
                            scope_email: access_token.validate_scope("email"),
                            scope_profile: access_token.validate_scope("profile"),
                            scope_d2c_read: access_token.validate_scope("d2c:read"),
                        };

                        return Ok(IdentityStruct {
                            request,
                            auth_fence: Some(auth_fence),
                            auth_fence_provider: Some(auth_fence_provider),
                            df4l_admin: None,
                            df4l_advisor: None,
                        });
                    }
                    Err(e) => {
                        return Err(granite::authorization_error!(
                            "Invalid or expired Bearer token"
                        )
                        .add_context(e));
                    }
                }
            }
            None => {
                // Do nothing
            }
        };

        let auth_fence = match auth_fence
            .get_user_identity(&req.session_token(), &mut redis)
            .await
        {
            Ok(user_info) => user_info,
            Err(_e) => {
                approck::error!("Error getting user info: {:?}", _e);
                None
            }
        };

        let dbcx = self.postgres_dbcx().await?;

        let df4l_admin = match &auth_fence {
            Some(user_info) => {
                let row = granite::pg_row_option!(
                    db = dbcx;
                    args = {
                        $identity_uuid: &user_info.identity_uuid,
                    };
                    row = {
                        perm_agency_read: bool,
                        perm_agency_write: bool,
                        perm_advisor_read: bool,
                        perm_advisor_write: bool,
                        perm_client_read: bool,
                        perm_client_write: bool,
                    };
                    SELECT
                        perm_agency_read,
                        perm_agency_write,
                        perm_advisor_read,
                        perm_advisor_write,
                        perm_client_read,
                        perm_client_write
                    FROM
                        df4l_admin.identity
                    WHERE TRUE
                        AND identity_uuid = $identity_uuid::uuid
                )
                .await?;

                match row {
                    Some(row) => Some(Df4lAdminIdentity {
                        agency_read: row.perm_agency_read,
                        agency_write: row.perm_agency_write,
                        advisor_read: row.perm_advisor_read,
                        advisor_write: row.perm_advisor_write,
                        client_read: row.perm_client_read,
                        client_write: row.perm_client_write,
                    }),
                    None => None,
                }
            }
            None => None,
        };

        let df4l_advisor = match &auth_fence {
            Some(user_info) => {
                let rows = granite::pg_row_vec!(
                    db = dbcx;
                    args = {
                        $identity_uuid: &user_info.identity_uuid,
                    };
                    row = {
                        advisor_uuid: Uuid,
                        perm_client_read: bool,
                        perm_client_write: bool,
                    };
                    SELECT
                        advisor_uuid,
                        true AS perm_client_read,
                        true AS perm_client_write
                    FROM
                        df4l.advisor
                    WHERE TRUE
                        AND identity_uuid = $identity_uuid::uuid
                )
                .await?;

                let rows: HashMap<granite::Uuid, Df4lAdvisorIdentity> = rows
                    .into_iter()
                    .map(|row| {
                        (
                            row.advisor_uuid,
                            Df4lAdvisorIdentity {
                                client_read: row.perm_client_read,
                                client_write: row.perm_client_write,
                            },
                        )
                    })
                    .collect();

                if !rows.is_empty() { Some(rows) } else { None }
            }
            None => None,
        };

        Ok(IdentityStruct {
            request,
            auth_fence,
            auth_fence_provider: None,
            df4l_admin,
            df4l_advisor,
        })
    }

    fn uuid_to_label(&self, uuid: granite::Uuid) -> Option<String> {
        self.uuid_cache.get(uuid)
    }
}

impl approck::Identity for IdentityStruct {}

impl AppStruct {
    pub fn sendgrid(&self) -> &api_sendgrid::ModuleStruct {
        &self.api_sendgrid
    }

    pub fn twilio(&self) -> &api_twilio::ModuleStruct {
        &self.api_twilio
    }
}
