#[approck::http(GET /myaccount/{identity_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::{PreEscaped, html};

        doc.set_title("My Account");

        // Fetch all account data using our consolidated API
        use crate::api::myaccount::summary::get_myaccount_summary;

        let account_data = get_myaccount_summary::call(
            app,
            identity,
            get_myaccount_summary::Input {
                identity_uuid: path.identity_uuid,
            },
        )
        .await?;

        // Prepare display values
        let display_email = account_data
            .email
            .as_deref()
            .unwrap_or("Email not available");

        doc.add_body(html!(
            d2c-my-account {
                grid-12 {
                    cell-3 {
                        panel {
                            content {
                                contact-info {
                                    h1 { (account_data.name) }
                                    p.phone {
                                        i.fas.fa-phone-alt aria-hidden="true" {}
                                        "Phone not available"
                                    }
                                    p.email {
                                        i.far.fa-envelope aria-hidden="true" {}
                                        @if let Some(email) = &account_data.email {
                                            a href=(format!("mailto:{}", email)) { (email) }
                                        } @else {
                                            (display_email)
                                        }
                                    }
                                }
                            }
                        }
                    }
                    cell-9 {
                        panel {
                            header {
                                h5 { "Login & Security" }
                                (bux::button::link::label_icon_class("Details", "fas fa-cog", &format!("/myaccount/{}/security/status", path.identity_uuid), "sm primary"))
                            }
                            content {
                                dl {
                                    dt { "Username" }
                                    dd { (account_data.username) }
                                }
                                dl {
                                    dt { "MFA (Multi-Factor Authentication) Settings" }
                                    dd {
                                        @if account_data.mfa_email_enabled {
                                            label-tag.success { "MFA Email Enabled" }
                                        } @else {
                                            label-tag.warning { "MFA Email Disabled" }
                                        }
                                        (PreEscaped("&nbsp;"));
                                        @if account_data.mfa_backup_codes_enabled {
                                            label-tag.success { "Backup Codes Enabled" }
                                        } @else {
                                            label-tag.warning { "Backup Codes Disabled" }
                                        }
                                    }
                                }
                                dl {
                                    dt { "Last Login" }
                                    dd {
                                        @if let Some(last_login) = &account_data.last_login {
                                            (last_login)
                                        } @else {
                                            "Never"
                                        }
                                    }
                                }
                                dl {
                                    dt { "Password Last Changed" }
                                    dd {
                                        @if let Some(password_changed) = &account_data.password_last_changed {
                                            (password_changed)
                                        } @else {
                                            "Unknown"
                                        }
                                    }
                                }
                                dl {
                                    dt { "Login Attempts Today" }
                                    dd {
                                        @if account_data.login_attempts_today == 0 {
                                            label-tag.success { "0 attempts" }
                                        } @else if account_data.login_attempts_today <= 2 {
                                            label-tag.info { (format!("{} attempts", account_data.login_attempts_today)) }
                                        } @else {
                                            label-tag.warning { (format!("{} attempts", account_data.login_attempts_today)) }
                                        }
                                    }
                                }
                            }
                        }

                        @for advisor in &account_data.advisors {
                            // Advisor Panel with horizontal flex layout
                            panel.advisor-panel {
                                header {
                                    h6 { "Advisor: " (format!("{} {}", advisor.first_name, advisor.last_name)) " (" (advisor.advisor_esid) ")" }
                                }
                                content {
                                    div style="display: flex; gap: 2rem; flex-wrap: wrap;" {
                                        // Column 1: Name and Address
                                        div style="flex: 1; min-width: 200px;" {
                                            h6 {
                                                "Name & Address "
                                                @if advisor.can_edit_gbu_id {
                                                    a href=(format!("/myaccount/{}/advisor/{}/contact", path.identity_uuid, advisor.advisor_uuid)) { "Edit" }
                                                }
                                            }
                                            dl {
                                                dt { "Name:" }
                                                dd { (format!("{} {}", advisor.first_name, advisor.last_name)) }
                                            }
                                            dl {
                                                dt { "Address:" }
                                                dd {
                                                    @if advisor.address.is_some() || advisor.city.is_some() || advisor.state.is_some() || advisor.zip.is_some() {
                                                        @if let Some(address) = &advisor.address {
                                                            div { (address) }
                                                        }
                                                        @if advisor.city.is_some() || advisor.state.is_some() || advisor.zip.is_some() {
                                                            div {
                                                                @if let Some(city) = &advisor.city {
                                                                    (city)
                                                                    @if advisor.state.is_some() || advisor.zip.is_some() {
                                                                        ", "
                                                                    }
                                                                }
                                                                @if let Some(state) = &advisor.state {
                                                                    (state)
                                                                    @if let Some(zip) = &advisor.zip {
                                                                        " " (zip)
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    } @else {
                                                        label-tag.danger { "None" }
                                                    }
                                                }
                                            }
                                        }

                                        // Column 2: Phone and Email
                                        div style="flex: 1; min-width: 200px;" {
                                            h6 {
                                                "Contact Information "
                                                @if advisor.can_edit_gbu_id {
                                                    a href=(format!("/myaccount/{}/advisor/{}/contact", path.identity_uuid, advisor.advisor_uuid)) { "Edit" }
                                                }
                                            }
                                            dl {
                                                dt { "Phone:" }
                                                dd {
                                                    @if let Some(phone) = &advisor.phone {
                                                        (phone)
                                                    } @else {
                                                        label-tag.danger { "None" }
                                                    }
                                                }
                                            }
                                            dl {
                                                dt { "Email:" }
                                                dd {
                                                    @if let Some(email) = &advisor.email {
                                                        (email)
                                                    } @else {
                                                        label-tag.danger { "None" }
                                                    }
                                                }
                                            }
                                        }

                                        // Column 3: GBU Advisor ID and States of Licensure
                                        div style="flex: 1; min-width: 200px;" {
                                            h6 {
                                                "GBU Advisor ID "
                                                @if advisor.can_edit_gbu_id {
                                                    a href=(format!("/myaccount/{}/advisor/{}/gbu", path.identity_uuid, advisor.advisor_uuid)) { "Edit" }
                                                }
                                            }
                                            dl {
                                                dd {
                                                    @if let Some(gbu_id) = &advisor.gbu_advisor_esid {
                                                        (gbu_id)
                                                    } @else {
                                                        label-tag.danger { "None" }
                                                    }
                                                }
                                            }

                                            h6 {
                                                "States of Licensure "
                                                @if advisor.can_edit_gbu_id {
                                                    a href=(format!("/myaccount/{}/advisor/{}/statelic", path.identity_uuid, advisor.advisor_uuid)) { "Edit" }
                                                }
                                            }
                                            dl {
                                                dd {
                                                    @if advisor.states_of_licensure.is_empty() {
                                                        label-tag.danger { "None" }
                                                    } @else {
                                                        @for (i, state) in advisor.states_of_licensure.iter().enumerate() {
                                                            @if i > 0 {
                                                                (PreEscaped("&nbsp;"));
                                                            }
                                                            label-tag.info { (state) }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            // Add margin between multiple advisors
                            @if account_data.advisors.len() > 1 {
                                div style="margin-bottom: 1rem;" {}
                            }
                        }
                        panel {
                            header {
                                h5 { "Finances" }
                                (bux::button::link::label_icon_class("Edit", "fas fa-edit", "/myaccount/", "sm primary"))
                            }
                            content {
                                table {
                                    thead {
                                        tr {
                                            th { "Brand" }
                                            th { "Last 4" }
                                            th { "Exp Date" }
                                            th { "" }
                                        }
                                    }
                                    tbody {
                                        tr {
                                            th { "Visa" }
                                            td { "0837" }
                                            td { "10/2027" }
                                            td.text-right {
                                                label-tag.primary { "Default Card" }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}
