#[approck::http(GET /myaccount/{identity_uuid:Uuid}/security/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Security");

        // Fetch all account data using our consolidated API
        use crate::api::myaccount::summary::get_myaccount_summary;

        let account_data = get_myaccount_summary::call(
            app,
            identity,
            get_myaccount_summary::Input {
                identity_uuid: path.identity_uuid,
            },
        )
        .await?;

        // Prepare display values
        let display_email = account_data
            .email
            .as_deref()
            .unwrap_or("Email not available");

        doc.add_body(html!(
            d2c-security {
                grid-12 {
                    cell-3 {
                        panel {
                            content {
                                contact-info {
                                    h1 { (account_data.name) }
                                    p.phone.mb-0 {
                                        "Phone not available"
                                    }
                                    p.email {
                                        @if let Some(email) = &account_data.email {
                                            a href=(format!("mailto:{}", email)) { (email) }
                                        } @else {
                                            (display_email)
                                        }
                                    }
                                    hr;
                                    a.btn.primary.block href="#" { "Visit Stripe Billing" }
                                }
                            }
                        }
                    }
                    cell-9 {
                        panel.login-info {
                            header {
                                div {
                                    h5 { "Login Info" }
                                    p { "Manage your login credentials and review recent login activity." }
                                }
                            }
                            content {
                                ul.data-list {
                                    li {
                                        hbox {
                                            i.fas.fa-fingerprint aria-hidden="true" {}
                                            dl {
                                                dt { "Username" }
                                                dd { (account_data.username) }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-key aria-hidden="true" {}
                                            dl {
                                                dt { "Password" }
                                                dd {
                                                    "Last updated "
                                                    @if let Some(password_changed) = &account_data.password_last_changed {
                                                        (password_changed)
                                                    } @else {
                                                        "Unknown"
                                                    }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-sign-in-alt aria-hidden="true" {}
                                            dl {
                                                dt { "Last Login" }
                                                dd {
                                                    @if let Some(last_login) = &account_data.last_login {
                                                        (last_login)
                                                    } @else {
                                                        "Never"
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-calendar-alt aria-hidden="true" {}
                                            dl {
                                                dt { "Login Attempts Today" }
                                                dd {
                                                    @if account_data.login_attempts_today == 0 {
                                                        "0 attempts"
                                                    } @else if account_data.login_attempts_today <= 2 {
                                                        (format!("{} attempts", account_data.login_attempts_today))
                                                    } @else {
                                                        (format!("{} attempts", account_data.login_attempts_today))
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fab.fa-google style="color: #DB4437;" aria-hidden="true" {}
                                            dl {
                                                dt { "Login with Google" }
                                                dd {
                                                    label-tag.success { "Enabled as of 06/06/2025" }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fab.fa-microsoft style="color: #00A4EF;" aria-hidden="true" {}
                                            dl {
                                                dt { "Login with Microsoft" }
                                                dd {
                                                    label-tag.default { "Not Enabled" }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                }
                            }
                        }
                        panel.security-settings {
                            header {
                                div {
                                    h5 { "Security Settings" }
                                    p { "Manage your security settings and verification status." }
                                }
                            }
                            content {
                                ul.data-list {
                                    li {
                                        hbox {
                                            i.fas.fa-mobile-alt aria-hidden="true" {}
                                            dl {
                                                dt { "Mobile Number" }
                                                dd {
                                                    small { "+****************" }
                                                    br;
                                                    label-tag.success { "Verified on 06/06/2025" }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-envelope-open-text aria-hidden="true" {}
                                            dl {
                                                dt { "Email Address" }
                                                dd {
                                                    small { "<EMAIL>" }
                                                    br;
                                                    label-tag.warning { "Not Verified" }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-shield-alt aria-hidden="true" {}
                                            dl {
                                                dt { "2-Step Verification" }
                                                dd {
                                                    label-tag.success { "Enabled on 06/06/2025" }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-sms aria-hidden="true" {}
                                            dl {
                                                dt { "MFA by SMS" }
                                                dd {
                                                    label-tag.success { "MFA SMS Enabled" }
                                                    br;
                                                    small { "Messages sent to +18148888888" }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-envelope aria-hidden="true" {}
                                            dl {
                                                dt { "MFA by Email" }
                                                dd {
                                                    label-tag.default { "MFA Email Not Enabled" }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-mobile-alt aria-hidden="true" {}
                                            dl {
                                                dt { "MFA by Authenticator App" }
                                                dd {
                                                    label-tag.default { "Authenticator App Not Enabled" }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-key aria-hidden="true" {}
                                            dl {
                                                dt { "Backup Codes" }
                                                dd {
                                                    label-tag.success { "Backup Codes Enabled" }
                                                    br;
                                                    small { "20 Codes Unused" }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}
