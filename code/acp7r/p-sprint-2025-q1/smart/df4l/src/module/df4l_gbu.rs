use auth_fence::openid::{OpenIDProvider, OpenIDProviderType};
use auth_fence::types::BaseOpenIDConfig;

pub struct OpenIDGBUProvider {
    pub config: BaseOpenIDConfig,
}

#[async_trait::async_trait]
impl OpenIDProvider for OpenIDGBUProvider {
    fn key(&self) -> String {
        self.config.key.clone()
    }
    fn provider(&self) -> OpenIDProviderType {
        OpenIDProviderType::Custom("gbu".to_string())
    }
    fn client_id(&self) -> String {
        self.config.client_id.clone()
    }
    fn client_secret(&self) -> String {
        self.config.client_secret.clone()
    }
    fn get_scopes(&self) -> Vec<String> {
        vec![
            "openid".to_string(),
            "profile".to_string(),
            "email".to_string(),
        ]
    }
    fn get_base_uri(&self) -> String {
        "https://sauth-uat.gbu.org".to_string()
    }
    fn get_issuer_url(&self) -> String {
        format!("{}/auth/realms/gbu", self.get_base_uri())
    }
    fn get_user_info_uri(&self) -> String {
        format!(
            "{}/auth/realms/gbu/protocol/openid-connect/userinfo",
            self.get_base_uri()
        )
    }
    fn get_redirect_uri(&self, app_url: String) -> String {
        format!("{}/oauth2/{}/redirect", app_url, self.key())
    }
    fn get_auth_uri(&self) -> String {
        format!(
            "{}/auth/realms/gbu/protocol/openid-connect/auth",
            self.get_base_uri()
        )
    }
    fn get_token_uri(&self) -> String {
        format!(
            "{}/auth/realms/gbu/protocol/openid-connect/token",
            self.get_base_uri()
        )
    }
    fn get_well_known_uri(&self) -> String {
        format!(
            "{}/auth/realms/gbu/.well-known/openid-configuration",
            self.get_base_uri()
        )
    }
    fn get_jwks_uri(&self) -> String {
        format!(
            "{}/auth/realms/gbu/protocol/openid-connect/certs",
            self.get_base_uri()
        )
    }
    fn get_button_html(&self, next_uri: Option<String>) -> maud::PreEscaped<String> {
        maud::html!(
            a href=(self.get_button_uri(next_uri)) class="btn btn-outline-custom social-btn" {
                "GBU"
            }
        )
    }
}
