
schema df4l {

    table advisor {
        column advisor_uuid Uuid {
            default = uuidv7()
            update = false
            primary_key = true
        }

        column identity_uuid Uuid {
            nullable = true
            fkrr = "auth_fence.identity.identity_uuid"
        }

        column agency_uuid Uuid {
            nullable = true
            fkrr = "agency.agency_uuid"
        }

        column create_ts DateTimeTz {
            default = now()
            update = false
        }

        column advisor_esid Esid {
            length = 7
        }

        column gbu_advisor_esid EsidNull {
            max_length = 20
        }


        column first_name FirstName {}

        column last_name LastName {}

        column active Active {}
    }

}