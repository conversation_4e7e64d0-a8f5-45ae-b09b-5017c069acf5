//! This file is powered off of the df4l.signup_advisor table
//! SECON checks must be done before calling this functionality.

use granite::Uuid;

//-------------------------------------------------------------------------------------------------

pub struct Signup {
    pub contact: Result<Contact, Contact_Error>,
    pub verify: Result<Verify, Verify_Error>,
    pub terms: Result<Terms, Terms_Error>,
    pub done: Option<Done>,
}

impl Signup {
    pub async fn load(
        db: &impl approck_postgres::DB,
        signup_advisor_uuid: granite::Uuid,
    ) -> granite::Result<Signup> {
        // query to get all the data
        let row = granite::pg_row!(
            db = db;
            args = {
                $signup_advisor_uuid: &signup_advisor_uuid,
            };
            row = {
                first_name: Option<String>,
                last_name: Option<String>,
                email: Option<String>,
                email_verified_ts: Option<DateTimeTz>,
                phone: Option<String>,
                phone_verified_ts: Option<DateTimeTz>,
                terms_accepted_name: Option<String>,
                terms_accepted_ts: Option<DateTimeUtc>,
                terms_document_uuid: Option<Uuid>,
                terms_revision: Option<String>,
                terms_accepted_addr: Option<IpAddr>,
                advisor_uuid_created: Option<Uuid>,
            };
            SELECT
                first_name,
                last_name,
                email,
                email_verified_ts,
                phone,
                phone_verified_ts,
                terms_accepted_name,
                terms_accepted_ts,
                terms_document_uuid,
                terms_revision,
                terms_accepted_addr::inet AS terms_accepted_addr,
                advisor_uuid_created
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        Ok(Signup {
            contact: Contact_Partial {
                first_name: row.first_name.clone(),
                last_name: row.last_name.clone(),
                email: row.email.clone(),
                phone: row.phone.clone(),
            }
            .validate(),
            verify: Verify_Partial {
                email: row.email,
                email_verified_ts: Some(row.email_verified_ts),
                phone: row.phone,
                phone_verified_ts: Some(row.phone_verified_ts),
            }
            .validate(),
            terms: Terms_Partial {
                first_name: row.first_name,
                last_name: row.last_name,
                terms_accepted_name: row.terms_accepted_name,
                document_uuid: row.terms_document_uuid,
                revision: row.terms_revision,
                remote_addr: row.terms_accepted_addr,
            }
            .validate(),
            done: row
                .advisor_uuid_created
                .map(|advisor_uuid| Done { advisor_uuid }),
        })
    }

    // is this ready to complete?
    pub fn ready_to_complete(&self) -> bool {
        self.contact.is_ok() && self.verify.is_ok() && self.terms.is_ok() && self.done.is_none()
    }
}

//-------------------------------------------------------------------------------------------------
#[granite::gtype(RsType, RsPartial, RsError, RsDebug)]
pub struct Contact {
    pub first_name: String,
    pub last_name: String,
    pub email: String,
    pub phone: String,
}
impl Contact_Partial {
    pub fn validate(self) -> Result<Contact, Contact_Error> {
        let mut errors = Contact_Error {
            first_name: None,
            last_name: None,
            email: None,
            phone: None,
        };

        let first_name = self
            .first_name
            .map(|s| s.trim().to_string())
            .unwrap_or_default();
        let last_name = self
            .last_name
            .map(|s| s.trim().to_string())
            .unwrap_or_default();
        let email = self.email.map(|s| s.trim().to_string()).unwrap_or_default();
        let phone = self.phone.map(|s| s.trim().to_string()).unwrap_or_default();

        if first_name.is_empty() {
            errors.first_name = Some("First name is required.".to_string());
        }
        if last_name.is_empty() {
            errors.last_name = Some("Last name is required.".to_string());
        }
        if email.is_empty() {
            errors.email = Some("Email is required.".to_string());
        }
        if phone.is_empty() {
            errors.phone = Some("Phone is required.".to_string());
        }

        if errors.first_name.is_some()
            || errors.last_name.is_some()
            || errors.email.is_some()
            || errors.phone.is_some()
        {
            return Err(errors);
        }

        Ok(Contact {
            first_name,
            last_name,
            email,
            phone,
        })
    }
}

//-------------------------------------------------------------------------------------------------
#[granite::gtype(RsType, RsPartial, RsError)]
pub struct Verify {
    email: String,
    email_verified_ts: Option<DateTimeTz>,
    phone: String,
    phone_verified_ts: Option<DateTimeTz>,
}

impl Verify_Partial {
    fn validate(self) -> Result<Verify, Verify_Error> {
        let mut errors = Verify_Error {
            email: None,
            email_verified_ts: None,
            phone: None,
            phone_verified_ts: None,
        };

        // Check if we have all required fields for verification completion
        match (
            self.email,
            self.email_verified_ts,
            self.phone,
            self.phone_verified_ts,
        ) {
            (Some(email), Some(email_verified_ts), Some(phone), Some(phone_verified_ts)) => {
                if email_verified_ts.is_none() && phone_verified_ts.is_none() {
                    errors.email_verified_ts = Some("Email verification is required.".to_string());
                    errors.phone_verified_ts = Some("Phone verification is required.".to_string());
                    return Err(errors);
                }

                // Both email and phone are verified
                Ok(Verify {
                    email,
                    email_verified_ts,
                    phone,
                    phone_verified_ts,
                })
            }
            (email, email_verified_ts, phone, phone_verified_ts) => {
                // Set error messages for missing verification
                if email.is_none() {
                    errors.email = Some("Email is required.".to_string());
                }
                if email_verified_ts.is_none() {
                    errors.email_verified_ts = Some("Email verification is required.".to_string());
                }
                if phone.is_none() {
                    errors.phone = Some("Phone is required.".to_string());
                }
                if phone_verified_ts.is_none() {
                    errors.phone_verified_ts = Some("Phone verification is required.".to_string());
                }
                Err(errors)
            }
        }
    }
}

//-------------------------------------------------------------------------------------------------
#[granite::gtype(RsType, RsPartial, RsError)]
pub struct Terms {
    pub first_name: String,
    pub last_name: String,
    pub terms_accepted_name: String,
    pub document_uuid: Uuid,
    pub revision: String,
    pub remote_addr: IpAddr,
}

impl Terms_Partial {
    #[allow(clippy::result_large_err)]
    pub fn validate(self) -> Result<Terms, Terms_Error> {
        let mut errors = Terms_Error {
            first_name: None,
            last_name: None,
            terms_accepted_name: None,
            document_uuid: None,
            revision: None,
            remote_addr: None,
        };

        let terms = match (
            self.first_name,
            self.last_name,
            self.terms_accepted_name,
            self.document_uuid,
            self.revision,
            self.remote_addr,
        ) {
            (
                Some(first_name),
                Some(last_name),
                Some(terms_accepted_name),
                Some(document_uuid),
                Some(revision),
                Some(remote_addr),
            ) => Terms {
                first_name: first_name.trim().to_string(),
                last_name: last_name.trim().to_string(),
                terms_accepted_name: terms_accepted_name.trim().to_string(),
                document_uuid,
                revision,
                remote_addr,
            },
            (first_name, last_name, terms_accepted_name, document_uuid, revision, remote_addr) => {
                errors.first_name = first_name
                    .is_none()
                    .then(|| "First name is required.".to_string());
                errors.last_name = last_name
                    .is_none()
                    .then(|| "Last name is required.".to_string());
                errors.terms_accepted_name = terms_accepted_name
                    .is_none()
                    .then(|| "Terms must be accepted.".to_string());
                errors.document_uuid = document_uuid
                    .is_none()
                    .then(|| "Document UUID is required.".to_string());
                errors.revision = revision
                    .is_none()
                    .then(|| "Revision is required.".to_string());
                errors.remote_addr = remote_addr
                    .is_none()
                    .then(|| "Remote address is required.".to_string());
                return Err(errors);
            }
        };

        let calculated_name = format!("{}{}", terms.first_name, terms.last_name).to_lowercase();
        let entered_name = terms.terms_accepted_name.to_lowercase().replace(' ', "");

        if calculated_name != entered_name {
            errors.terms_accepted_name = Some(
                "You must enter your First and Last Name to accept the Terms & Conditions."
                    .to_string(),
            );
            return Err(errors);
        }

        Ok(terms)
    }
}

pub struct Done {
    pub advisor_uuid: Uuid,
}
