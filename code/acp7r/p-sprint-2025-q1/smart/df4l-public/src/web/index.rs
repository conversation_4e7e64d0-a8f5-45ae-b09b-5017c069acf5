#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Debt Free 4 Life");

        doc.add_body(html!(
            page-content {
                img src="https://asset7.net/Zagula/Smart/DF4L/df4l-money-icon.png" class="img-fluid df4l-logo" {}
                hr;
                h1 {
                    "Don't Make Another Debt Payment" br;
                    "Until You've Read This Page"
                }
                p {
                    "We guarantee you'll never look at debt" br;
                    "(and how you pay it) the same way again..."
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
