#[approck::http(GET /signup/advisor/{signup_advisor_uuid:Uuid}/terms; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document, path: Path) -> Result<Response> {
        use super::super::{WizardData, WizardStep};
        use approck::html;

        doc.set_title("Terms and Conditions");

        let wizard_data = WizardData {
            signup_advisor_uuid: path.signup_advisor_uuid,
        };

        #[rustfmt::skip]
        let mut wizard = bux::component::form_wizard::new(
            WizardStep::Terms,
            wizard_data,
        );
        wizard.set_id("terms-form");
        wizard.set_hidden("action", "accept_terms");

        wizard.add_body(html! {
            panel.terms-box {
                header {
                    h5 { i."fas fa-file-signature" {} " Terms and Conditions" }
                }
                content {
                    p.notice { "Please review and accept the terms below to continue with your advisor setup." }
                    div.terms {
                        p { strong { "By providing pre-authorization for the use of my credit card, I acknowledge and agree to the following terms and conditions:" } }
                        p { strong { "1. Authorized Use" } br; "I authorize Smart Publishing to charge my credit card for the specified amount agreed upon for the ongoing subscription to Debt2Capital™ and the monthly charges associated with my subscription (this includes the required monthly soft credit pulls" em { "**" } " for each of the clients (and possibly their spouse/relationship partner) associated with my account in order for the functionality of the software to remain accurate) and prospects you've entered into the system. Any additional charges beyond this authorization require my explicit written or verbal consent." }
                        p { strong { "2. Payment Processing" } br; "The pre-authorized amount will be charged to my credit card on an ongoing monthly basis. I understand that charges will be processed securely, and a receipt will be available in the My Account area of my Debt2Capital.com account." }
                        p { strong { "3. Cardholder Responsibilities" } br; "I confirm that I am the authorized cardholder and that the credit card provided is valid and in good standing. I take full responsibility for ensuring that sufficient funds or credit limits are available to cover authorized transactions." }
                        p { strong { "4. Revocation of Authorization" } br; "I may revoke this pre-authorization at any time by providing written notice to Smart Publishing, at least 3 business days before the next scheduled charge. Any charges already processed before revocation will not be reversed unless an error has occurred." }
                        p { strong { "5. Disputes and Errors" } br; "If I identify an unauthorized or incorrect charge, I will notify Smart Publishing within 30 days of the transaction date. Disputes will be handled according to the company’s policies and applicable financial institution regulations." }
                        p { strong { "6. Confidentiality and Security" } br; "Smart Publishing agrees to keep my credit card information secure and confidential, only using it for the pre-authorized purposes outlined in this agreement." }
                        p { strong { "7. Indemnification" } br; "I agree to indemnify and hold harmless Smart Publishing and its representatives from any claims, damages, or liabilities resulting from the authorized use of my credit card, except in cases of fraud or gross negligence by the company." }
                        p { strong { "8. Governing Law" } br; "This agreement shall be governed by and construed in accordance with the laws of West Virginia, and any disputes shall be resolved in the appropriate jurisdiction." }
                        p { em { "** Each advisor Debt2Capital™ account will have 14 free soft credit pulls monthly included in their subscription. All soft credit pulls over 14, each month, will be billed at $2.25 for each client and their spouse/relationship partner." } }
                    }
                    (bux::input::checkbox::bux_checkbox("terms_accepted", "I have read and agree to the Terms and Conditions above.", false))
                }
            }
        });

        doc.add_body(html! {
            (wizard)
        });

        Ok(Response::HTML(doc.into()))
    }
}
