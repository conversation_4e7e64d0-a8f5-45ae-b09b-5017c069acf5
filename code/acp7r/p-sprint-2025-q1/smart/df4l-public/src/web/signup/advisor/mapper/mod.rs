pub mod billing;
pub mod complete;
pub mod index;
pub mod terms;
pub mod verify;

#[approck::prefix(/signup/advisor/{signup_advisor_uuid:Uuid}/)]
pub mod prefix {
    pub fn menu(menu: Menu, signup_advisor_uuid: Uuid) {
        menu.set_label_uri(
            "Get Started",
            &format!("/signup/advisor/{}/", signup_advisor_uuid),
        );
        menu.add_link(
            "Terms & Conditions",
            &format!("/signup/advisor/{}/terms", signup_advisor_uuid),
        );
        menu.add_link(
            "Verification",
            &format!("/signup/advisor/{}/verify", signup_advisor_uuid),
        );
        menu.add_link(
            "Billing Information",
            &format!("/signup/advisor/{}/billing", signup_advisor_uuid),
        );
        menu.add_link(
            "Complete Signup",
            &format!("/signup/advisor/{}/complete", signup_advisor_uuid),
        );
    }
}

use bux::component::form_wizard::FormWizardImpl;
use granite::Uuid;

#[derive(PartialEq)]
pub enum WizardStep {
    Index,
    Terms,
    Verify,
    Billing,
    Complete,
}

pub struct WizardData {
    pub signup_advisor_uuid: Uuid,
}

#[allow(refining_impl_trait)]
impl FormWizardImpl for WizardStep {
    type Context = WizardData;

    fn all_variants() -> Vec<Self> {
        vec![
            WizardStep::Index,
            WizardStep::Terms,
            WizardStep::Verify,
            WizardStep::Billing,
            WizardStep::Complete,
        ]
    }

    fn label(&self, _context: &Self::Context) -> String {
        match self {
            WizardStep::Index => "Personal Information".to_string(),
            WizardStep::Terms => "Terms and Conditions".to_string(),
            WizardStep::Verify => "Verification".to_string(),
            WizardStep::Billing => "Billing".to_string(),
            WizardStep::Complete => "Complete Signup".to_string(),
        }
    }

    fn href(&self, context: &Self::Context) -> String {
        let uuid = context.signup_advisor_uuid;
        match self {
            WizardStep::Index => format!("/signup/advisor/{}/", uuid),
            WizardStep::Terms => format!("/signup/advisor/{}/terms", uuid),
            WizardStep::Verify => format!("/signup/advisor/{}/verify", uuid),
            WizardStep::Billing => format!("/signup/advisor/{}/billing", uuid),
            WizardStep::Complete => format!("/signup/advisor/{}/complete", uuid),
        }
    }

    fn enabled(&self, _context: &Self::Context) -> bool {
        true
    }

    fn complete(&self, _context: &Self::Context) -> bool {
        false
    }
}
