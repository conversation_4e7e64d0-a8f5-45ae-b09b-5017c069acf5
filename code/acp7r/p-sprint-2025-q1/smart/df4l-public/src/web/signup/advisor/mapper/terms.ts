import "@bux/component/form_wizard.mts";
import "./terms.mcss";
import "@bux/input/checkbox.mts";

// Terms and conditions page functionality
document.addEventListener("DOMContentLoaded", function () {
    const form = document.querySelector("wizard-panel") as HTMLFormElement;
    const checkbox = document.querySelector('input[name="terms_accepted"]') as HTMLInputElement;
    const nextButton = document.querySelector('button[type="submit"]') as HTMLButtonElement;

    if (!form || !checkbox || !nextButton) return;

    // Initially disable the next button
    nextButton.disabled = true;
    nextButton.style.opacity = "0.5";

    // Enable/disable next button based on checkbox state
    function updateNextButton() {
        if (checkbox.checked) {
            nextButton.disabled = false;
            nextButton.style.opacity = "1";
        } else {
            nextButton.disabled = true;
            nextButton.style.opacity = "0.5";
        }
    }

    // Listen for checkbox changes
    checkbox.addEventListener("change", updateNextButton);

    // Form submission validation
    form.addEventListener("submit", function (e) {
        if (!checkbox.checked) {
            e.preventDefault();
            alert("Please accept the Terms and Conditions to continue.");
            return;
        }
    });
});
