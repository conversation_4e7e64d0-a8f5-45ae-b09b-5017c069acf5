//-------------------------------------------------------------------------------------------------
// 1. Import Components

import "./password.mcss";
import "@bux/component/password_creator.mts";
import "@bux/component/form_wizard.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code

import { SE } from "@granite/lib.mts";
import { signup_advisor_complete } from "@crate/api/signup/advisorλ.mts";
import BuxComponentPasswordCreator from "@bux/component/password_creator.mts";
import FormWizard from "@bux/component/form_wizard.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements

const $form = SE(document, "form.bux-form-wizard") as HTMLFormElement;
const $signup_advisor_uuid: HTMLInputElement = SE($form, "[name=signup_advisor_uuid]");
const $password_creator: BuxComponentPasswordCreator = SE($form, "bux-component-password-creator");

const signup_advisor_uuid = $signup_advisor_uuid.value;

const $next_button = $form.querySelector("a.x-next-button") as HTMLAnchorElement | null;

// -------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

if ($next_button) {
    $next_button.addEventListener("click", (event) => {
        event.preventDefault();
        $form.dispatchEvent(new Event("submit"));
    });
} else {
    const $submit_button = document.createElement("button");
    $submit_button.type = "submit";
    $submit_button.className = "bux-button primary";
    $submit_button.innerHTML = '<i class="fas fa-check"></i> Complete Signup';

    const $footer = $form.querySelector("footer");
    if ($footer) {
        $footer.appendChild($submit_button);
    }
}

// -------------------------------------------------------------------------------------------------
// 5. Form Wizard
// -------------------------------------------------------------------------------------------------

new FormWizard({
    $form,
    api: signup_advisor_complete.api,

    err: (errors) => {
        $password_creator.set_e(errors.password);
    },

    get: () => {
        // Validate passwords before submitting
        if (!$password_creator.validate_passwords()) {
            throw new Error("Password validation failed");
        }

        return {
            signup_advisor_uuid: signup_advisor_uuid,
            password: $password_creator.value,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        if ($next_button) {
            window.location.href = $next_button.href;
        } else {
            window.location.href = output.next_url;
        }
    },
});
