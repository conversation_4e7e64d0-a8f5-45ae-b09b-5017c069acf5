#[approck::http(GET /signup/advisor/{signup_advisor_uuid:Uuid}/billing; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        doc: Document,
        identity: Identity,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use crate::api::signup::advisor::signup_advisor_wizard_data;
        use crate::api::signup::advisor::stripe_data;
        use approck::html;

        let wizard_data = signup_advisor_wizard_data::call(
            app,
            identity,
            signup_advisor_wizard_data::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;
        let stripe_data = stripe_data::call(
            app,
            identity,
            stripe_data::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;

        let mut show_payment_button = true;

        // If stripe is marked as Paid inside DB, we do not need to deal with session at all
        if stripe_data.stripe_payment_status == Some("paid".to_string()) {
            show_payment_button = false;
        } else {
            // load stripe session object from stripe api
            let session_id = stripe_data.stripe_checkout_session_id.unwrap_or_default();
            println!("DB - stripe_data: session_id: {}", session_id);

            //we need to check if we have a session id, and get checkout_session from stripe
            let checkout_session = if session_id.is_empty() {
                None
            } else {
                Some(app.stripe().checkout_session_get(&session_id).await?)
            };
            let payment_status = checkout_session
                .as_ref()
                .map(|s| s.payment_status.clone())
                .unwrap_or_default();
            let checkout_session_id = checkout_session.as_ref().map(|s| s.id.clone());
            let stripe_subscription_id = checkout_session.as_ref().map(|s| s.subscription.clone());

            println!("STRIPE checkout_session: {:?}", checkout_session);
            println!("checkout_session.id: {:?}", checkout_session_id);
            println!("checkout_session.payment_status: {:?}", payment_status);

            //update signup_advisor with stripe_checkout_session_id and stripe_payment_status
            let dbcx = app.postgres_dbcx().await?;
            granite::pg_execute!(
                db = dbcx;
                args = {
                    $signup_advisor_uuid: &path.signup_advisor_uuid,
                    $stripe_checkout_session_id: &checkout_session_id,
                    $stripe_payment_status: &payment_status,
                    $stripe_subscription_id: &stripe_subscription_id,
                };
                UPDATE
                    df4l.signup_advisor
                SET
                    stripe_checkout_session_id = $stripe_checkout_session_id,
                    stripe_payment_status = $stripe_payment_status,
                    stripe_subscription_id = $stripe_subscription_id,
                    payment_completed_ts = NOW()
                WHERE
                    signup_advisor_uuid = $signup_advisor_uuid
            )
            .await?;

            // if payment_status is paid, we don't need to show the button
            if payment_status == "paid" {
                show_payment_button = false;
            }
        } // if

        let mut portal_link = String::new();
        if stripe_data.stripe_customer_id.is_some() {
            portal_link = app
                .stripe()
                .create_billing_portal_link(
                    stripe_data
                        .stripe_customer_id
                        .as_deref()
                        .unwrap_or_default(),
                    &format!(
                        "{}/signup/advisor/{}/billing",
                        "https://local.acp7.net:3014", // TODO
                        path.signup_advisor_uuid
                    ),
                    None,
                )
                .await?
                .url;
        }

        let mut wizard = bux::component::form_wizard::new(WizardStep::Billing, wizard_data)?;
        wizard.set_id("billing-form");
        wizard.set_hidden("action", "billing_info");
        wizard.set_hidden("signup_advisor_uuid", path.signup_advisor_uuid);

        wizard.add_body(html! {
            // show this button if we don't have checkout_session
            @if show_payment_button {
                div.credit-card-form {
                    // Create Customer Link with id="create-customer"
                    div {
                        a.btn.primary id="call-stripe" {
                            i.fas.fa-check aria-hidden="true" {}
                            " "
                            span { "Continue To Billing" }
                        }
                    }
                }
            }
            @else {
                div.x-success-wrapper {
                    p { "Your subscription has been successfully activated." }
                    p { "You are now fully set up." }
                    p { "To manage your subscription, please use the link below." }
                    a href=(portal_link) { "Manage Your Subscription" }
                }
            }
        });

        doc.set_title("Billing Information");
        doc.add_body(html! {
            (wizard)
        });

        Ok(Response::HTML(doc.into()))
    }
}
