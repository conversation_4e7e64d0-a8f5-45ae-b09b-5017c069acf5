#[approck::http(GET /signup/advisor/{signup_advisor_uuid:Uuid}/billing; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document, path: Path) -> Response {
        use super::super::{WizardData, WizardStep};
        use approck::html;

        doc.set_title("Billing Information");

        let wizard_data = WizardData {
            signup_advisor_uuid: path.signup_advisor_uuid,
        };

        #[rustfmt::skip]
        let mut wizard = bux::component::form_wizard::new(
            WizardStep::Billing,
            wizard_data,
        );
        wizard.set_id("billing-form");
        wizard.set_hidden("action", "billing_info");

        wizard.add_body(html! {
            div.developer-note {
                strong { "⚠️ Developer Note: " }
                "This billing screen needs more clarification about what should be implemented here. "
                "Please specify the required billing fields, payment methods, pricing plans, and validation requirements."
            }
            p { "Please provide your billing information. Your payment details are securely processed." }
        });
        doc.add_body(html! {
            (wizard)
        });

        Response::HTML(doc.into())
    }
}
