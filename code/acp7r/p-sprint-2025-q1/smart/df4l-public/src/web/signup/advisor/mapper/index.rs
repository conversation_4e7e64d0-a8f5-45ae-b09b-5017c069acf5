#[approck::http(GET /signup/advisor/{signup_advisor_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document, identity: Identity, path: Path) -> Result<Response> {
        use super::super::{WizardData, WizardStep};
        use approck::html;

        doc.set_title("Personal Information");

        if identity.is_logged_in() {
            doc.add_body(html!(
                bux-border-panel {
                    header {
                        h5 { i."fas fa-user-check" style="margin-right: 0.3rem;" {}
                            "Already Signed In"
                        }
                    }
                    br;
                    content {
                        p { "You are already signed in to your account. You don't need to sign up again." }
                        p { "Would you like to go to your dashboard instead?" }
                    }
                    hr;
                    footer style="text-align: right;" {
                        (bux::button::link::label_icon_class("Go to Dashboard ", "", "/dashboard", "primary"))
                        " "
                        (bux::button::link::label_icon_class("Sign Out ", "fas fa-sign-out-alt", "/auth/logout", "secondary"))
                    }
                }
            ));
            return Ok(Response::HTML(doc.into()));
        }

        let wizard_data = WizardData {
            signup_advisor_uuid: path.signup_advisor_uuid,
        };

        #[rustfmt::skip]
        let mut wizard = bux::component::form_wizard::new(
            WizardStep::Index,
            wizard_data,
        );
        wizard.set_id("personal-info-form");
        wizard.set_hidden("action", "signup");

        wizard.add_body(html! {
            p {"Please fill out your personal details. All fields are required to continue."} br;
            grid-2 {
                (bux::input::text::string::name_label_value("firstname", "👤 First Name:", None))
                (bux::input::text::string::name_label_value("lastname", "👤 Last Name:", None))
                div {
                    (bux::input::text::string::name_label_value("phone", "📱 Phone:", None))
                    p { "We'll send SMS updates to this mobile number." }
                }
                div {
                    (bux::input::text::string::name_label_value("email", "✉️ Email:", None))
                    p { "A verification code will be sent to this email address." }
                }
            }
        });

        doc.add_body(html! {
            (wizard)
        });

        Ok(Response::HTML(doc.into()))
    }
}
