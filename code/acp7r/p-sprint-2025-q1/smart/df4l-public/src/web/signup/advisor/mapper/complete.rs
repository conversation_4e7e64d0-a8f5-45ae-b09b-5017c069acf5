#[approck::http(GET /signup/advisor/{signup_advisor_uuid:Uuid}/complete; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        _app: App,
        doc: Document,
        _identity: Identity,
        _path: Path,
    ) -> Result<Response> {
        use approck::html;

        doc.set_title("Complete Signup");
        doc.add_body(html! {
            div.signup-wrap {
                h1.signup-title {
                    "Thanks for Signing Up!"
                }
                div.signup-icon {
                    i."fas fa-check-circle" {}
                }
                p.signup-msg {
                    "Please check your inbox to verify your email address." br;
                    "Once confirmed, you'll be all set."
                }
                div.signup-actions {
                    a.bux-button.primary href="/dashboard" {
                        i."fas fa-check" {}
                        " Go to Dashboard"
                    }
                }
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}
