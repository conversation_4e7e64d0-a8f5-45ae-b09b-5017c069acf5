#[approck::http(GET /signup/advisor/{signup_advisor_uuid:Uuid}/complete; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document, path: Path) -> Response {
        use super::super::{WizardData, WizardStep};
        use approck::html;

        doc.set_title("Complete Signup");

        let wizard_data = WizardData {
            signup_advisor_uuid: path.signup_advisor_uuid,
        };

        #[rustfmt::skip]
        let mut wizard = bux::component::form_wizard::new(
            WizardStep::Complete,
            wizard_data,
        );
        wizard.set_id("complete-form");
        wizard.set_hidden("redirect", "dashboard");
        // For the complete step, we'll customize the button labels
        wizard.next_label = "Go to Dashboard".to_string();
        wizard.next_icon = "fas fa-check".to_string();
        wizard.back_label = "Back".to_string();
        wizard.back_icon = "fas fa-arrow-left".to_string();

        wizard.add_body(html! {
            div.signup-wrap {
                h1.signup-title {
                    "Thanks for Signing Up!"
                }
                div.signup-icon {
                    i."fas fa-check-circle" {}
                }
                p.signup-msg {
                    "Please check your inbox to verify your email address." br;
                    "Once confirmed, you'll be all set."
                }
            }
        });

        doc.add_body(html! {
            (wizard)
        });
        Response::HTML(doc.into())
    }
}
