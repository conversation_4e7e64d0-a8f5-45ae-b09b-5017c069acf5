#[approck::http(GET /signup/advisor/{signup_advisor_uuid:Uuid}/verify; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document, path: Path) -> Response {
        use super::super::{WizardData, WizardStep};
        use approck::html;

        doc.set_title("Verification");

        let wizard_data = WizardData {
            signup_advisor_uuid: path.signup_advisor_uuid,
        };

        #[rustfmt::skip]
        let mut phone_verification = bux::component::verification_code();
        phone_verification.set_title("Verify your Phone");
        phone_verification.set_description("Enter the 4-digit code sent to your phone ending in:");
        phone_verification.set_phone_ending("0982");
        phone_verification.set_code_length(4);
        phone_verification.set_resend_link("#");

        let mut email_verification = bux::component::verification_code();
        email_verification.set_title("Verify your Email");
        email_verification
            .set_description("Please enter the 4-digit code sent to your email address");
        email_verification.set_phone_ending("<EMAIL>");
        email_verification.set_code_length(4);
        email_verification.set_email_link("#");

        #[rustfmt::skip]
        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::Verify, wizard_data);
            wizard.set_id("verification-form");
            wizard.set_hidden("action", "verify_codes");
            wizard.add_body(html! {
                grid-2 {
                    (phone_verification)
                    (email_verification)
                }
            });
            wizard
        };

        doc.add_body(html!((wizard)));
        Response::HTML(doc.into())
    }
}
