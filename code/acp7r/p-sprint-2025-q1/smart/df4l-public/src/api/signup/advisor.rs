// Manages the multi-step signup process for a financial advisor. This file contains
// a series of API endpoints that guide the user through the necessary stages of
// creating an advisor account. The entire process is designed as a wizard, where
// each step must be completed before proceeding to the next.
//
// The flow is as follows:
// 1. `signup_advisor_create`: Initiates the process, creating a temporary record.
// 2. `signup_advisor_contact_set/get`: Collects and retrieves the user's contact details.
// 3. `signup_advisor_verify_get/email/phone`: Handles two-factor verification by sending
//    and confirming codes sent to the user's email and phone.
// 4. `signup_advisor_terms_get/set`: Displays the terms of service and records the user's
//    acceptance.
// 5. `signup_advisor_password_set`: Allows the user to set a secure password for their account.
// 6. `signup_advisor_billing_save`: Integrates with <PERSON><PERSON> to set up the customer's
//    billing profile.
//
// The `signup_advisor_wizard_data` endpoint provides a snapshot of the user's progress
// at any point, enabling the frontend to display the correct state of the wizard.
/// ### Initiates the advisor signup process.
/// * This is the first step in the advisor signup process.
/// * It generates a unique UUID for the signup and a URL to continue the process.
#[approck::api]
pub mod signup_advisor_create {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {}

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub signup_advisor_uuid: Uuid,
        pub signup_url: String,
    }

    pub async fn call(app: App, identity: Identity) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.signup_advisor_create() {
            return_authorization_error!("signup_advisor_create");
        }

        let session_token = identity.session_token();
        let remote_addr = identity.get_address();

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $session_token: &session_token,
                $remote_addr: &remote_addr,
            };
            row = {
                signup_advisor_uuid: Uuid,
            };
            INSERT INTO df4l.signup_advisor (session_token, create_addr)
            VALUES ($session_token, $remote_addr::text::cidr)
            RETURNING signup_advisor_uuid
        )
        .await?;

        let signup_url = crate::ml_signup_advisor(row.signup_advisor_uuid);

        Ok(Output {
            signup_advisor_uuid: row.signup_advisor_uuid,
            signup_url,
        })
    }
}

/// ### Retrieves the current state of the signup wizard.
/// * This endpoint is used to determine which steps of the signup process are complete, pending, or have errors.
/// * It provides a snapshot of the entire signup flow's status.
#[approck::api]
pub mod signup_advisor_wizard_data {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub signup_advisor_uuid: Uuid,
        pub step_contact_enabled: bool,
        pub step_contact_complete: bool,
        pub step_contact_error: Option<String>,
        pub step_verify_enabled: bool,
        pub step_verify_complete: bool,
        pub step_verify_error: Option<String>,
        pub step_terms_enabled: bool,
        pub step_terms_complete: bool,
        pub step_terms_error: Option<String>,
        pub step_password_enabled: bool,
        pub step_password_complete: bool,
        pub step_password_error: Option<String>,
        pub step_billing_enabled: bool,
        pub step_billing_complete: bool,
        pub step_billing_error: Option<String>,
        pub done: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_read(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_read");
        }

        let wizard_data =
            crate::core::signup::advisor::Signup::load(&dbcx, input.signup_advisor_uuid).await?;

        Ok(Output {
            signup_advisor_uuid: input.signup_advisor_uuid,
            step_contact_enabled: true,
            step_contact_complete: wizard_data.contact.is_ok(),
            step_contact_error: wizard_data
                .contact
                .err()
                .map(|_| "Contact Information Missing".to_string()),
            step_verify_enabled: true,
            step_verify_complete: wizard_data.verify.is_ok(),
            step_verify_error: wizard_data
                .verify
                .err()
                .map(|_| "Verification Incomplete".to_string()),
            step_terms_enabled: true,
            step_terms_complete: wizard_data.terms.is_ok(),
            step_terms_error: wizard_data
                .terms
                .err()
                .map(|_| "Terms & Conditions Not Accepted".to_string()),
            step_password_enabled: true,
            step_password_complete: false, // Password is now handled separately
            step_password_error: None,
            step_billing_enabled: true,
            // TODO:DF4L - Set to true for now for password testing. Fix billing implementation.
            step_billing_complete: true,
            step_billing_error: None,
            done: wizard_data.done.is_some(),
        })
    }
}

/// ### Retrieves the contact information for a signup.
/// * Used to populate the contact information form when the user returns to this step.
#[approck::api]
pub mod signup_advisor_contact_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub signup_advisor_uuid: Uuid,
        pub first_name: Option<String>,
        pub last_name: Option<String>,
        pub email: Option<String>,
        pub phone: Option<String>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_read(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_read");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                signup_advisor_uuid: Uuid,
                first_name: Option<String>,
                last_name: Option<String>,
                email: Option<String>,
                phone: Option<String>,
            };
            SELECT
                signup_advisor_uuid,
                first_name,
                last_name,
                email,
                phone
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        Ok(Output {
            signup_advisor_uuid: row.signup_advisor_uuid,
            first_name: row.first_name,
            last_name: row.last_name,
            email: row.email,
            phone: row.phone,
        })
    }
}

/// ### Sets or updates the contact information for the signup.
/// * Validates the provided contact details.
/// * If the email or phone number changes, it resets the corresponding verification status, requiring the user to verify the new information.
#[approck::api]
pub mod signup_advisor_contact_set {
    use granite::{NestedError, ResultExt, return_authorization_error};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub email: String,
        pub phone: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let mut dbcx = app.postgres_dbcx().await?;

        // start transaction block
        {
            let dbtx = dbcx.transaction().await?;

            if !identity
                .signup_advisor_write(&dbtx, input.signup_advisor_uuid)
                .await
            {
                return_authorization_error!("signup_advisor_write");
            }

            let contact_result = crate::core::signup::advisor::Contact_Partial {
                first_name: Some(input.first_name),
                last_name: Some(input.last_name),
                email: Some(input.email),
                phone: Some(input.phone),
            }
            .validate();

            println!("contact_result: {:#?}", contact_result);

            let contact_validated = match contact_result {
                Ok(contact) => contact,
                Err(errors) => {
                    return Ok(Response::ValidationError(NestedError {
                        outer: "Validation Error".to_string(),
                        inner: Some(Input_Error {
                            signup_advisor_uuid: None,
                            first_name: errors.first_name,
                            last_name: errors.last_name,
                            email: errors.email,
                            phone: errors.phone,
                        }),
                    }));
                }
            };

            // Find data that changed.
            let changed = granite::pg_row!(
                db = dbtx;
                args = {
                    $signup_advisor_uuid: &input.signup_advisor_uuid,
                    $email: &contact_validated.email,
                    $phone: &contact_validated.phone,
                };
                row = {
                    email: bool,
                    phone: bool,
                };
                SELECT
                    email IS DISTINCT FROM $email AS email,
                    phone IS DISTINCT FROM $phone AS phone
                FROM
                    df4l.signup_advisor
                WHERE
                    signup_advisor_uuid = $signup_advisor_uuid
            )
            .await?;

            // if email changed, clear verification fields for email
            if changed.email {
                granite::pg_execute!(
                    db = dbtx;
                    args = {
                        $signup_advisor_uuid: &input.signup_advisor_uuid,
                    };
                    UPDATE
                        df4l.signup_advisor
                    SET
                        email_code = NULL,
                        email_code_sent_ts = NULL,
                        email_code_expire_ts = NULL,
                        email_code_attempts = 0,
                        email_verified_ts = NULL
                    WHERE
                        signup_advisor_uuid = $signup_advisor_uuid
                )
                .await?;
            }

            // if phone changed, clear verification fields for phone
            if changed.phone {
                granite::pg_execute!(
                    db = dbtx;
                    args = {
                        $signup_advisor_uuid: &input.signup_advisor_uuid,
                    };
                    UPDATE
                        df4l.signup_advisor
                    SET
                        phone_code = NULL,
                        phone_code_sent_ts = NULL,
                        phone_code_expire_ts = NULL,
                        phone_code_attempts = 0,
                        phone_verified_ts = NULL
                    WHERE
                        signup_advisor_uuid = $signup_advisor_uuid
                )
                .await?;
            }

            granite::pg_execute!(
                db = dbtx;
                args = {
                    $signup_advisor_uuid: &input.signup_advisor_uuid,
                    $first_name: &contact_validated.first_name,
                    $last_name: &contact_validated.last_name,
                    $email: &contact_validated.email,
                    $phone: &contact_validated.phone,
                };
                UPDATE
                    df4l.signup_advisor
                SET
                    first_name = $first_name,
                    last_name = $last_name,
                    email = $email,
                    phone = $phone
                WHERE
                    signup_advisor_uuid = $signup_advisor_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;
        }
        // end transaction block

        Ok(Response::Output(Output {}))
    }
}

/// ### Gets verification status and sends codes if necessary.
/// * This endpoint is called when the user enters the verification step.
/// * If verification codes have not been sent, it triggers sending them via email (SendGrid) and SMS (Twilio).
/// * It returns the timestamps when codes were sent and whether the email/phone have been verified.
/// * Fails if contact information (email, phone) is missing.
#[approck::api]
pub mod signup_advisor_verify_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub signup_advisor_uuid: Uuid,
        pub email: String,
        pub phone: String,
        pub email_code_sent_ts: DateTimeTz,
        pub phone_code_sent_ts: DateTimeTz,
        pub email_verified: bool,
        pub phone_verified: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_write(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_write");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                email: Option<String>,
                phone: Option<String>,
                email_code_sent_ts: Option<DateTimeUtc>,
                phone_code_sent_ts: Option<DateTimeUtc>,
                email_verified_ts: Option<DateTimeUtc>,
                phone_verified_ts: Option<DateTimeUtc>,
            };
            SELECT
                email,
                phone,
                email_code_sent_ts,
                phone_code_sent_ts,
                email_verified_ts,
                phone_verified_ts
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        // Must have phone and email to continue
        let (email, phone) = match (row.email, row.phone) {
            (Some(email), Some(phone)) => (email, phone),
            _ => {
                return Err(granite::process_error!(
                    "Email and phone are required.  Please go back to the previous step and enter them."
                ));
            }
        };

        // If email code hasn't been sent, send it
        let email_code_sent_ts = match row.email_code_sent_ts {
            Some(ts) => ts,
            None => {
                let email_code = granite::random_code_4();

                // Send email with verification code using SendGrid
                let email_subject = "Your D2C Verification Code";
                let email_message = format!(
                    "Your D2C verification code is: {}\n\nThis code will expire in 60 minutes.\n\n",
                    email_code
                );

                app.sendgrid()
                    .send_email(app, "default", &email, email_subject, &email_message)
                    .await?;

                let now = granite::Utc::now();

                // update the database
                granite::pg_execute!(
                    db = dbcx;
                    args = {
                        $signup_advisor_uuid: &input.signup_advisor_uuid,
                        $email_code: &email_code,
                        $now: &now,
                    };
                    UPDATE
                        df4l.signup_advisor
                    SET
                        email_code = $email_code,
                        email_code_sent_ts = $now::timestamptz,
                        email_code_expire_ts = $now::timestamptz + "1 hour"::interval
                    WHERE
                        signup_advisor_uuid = $signup_advisor_uuid
                )
                .await?;

                now
            }
        };

        let phone_code_sent_ts = match row.phone_code_sent_ts {
            Some(ts) => ts,
            None => {
                let phone_code = granite::random_code_4();

                // Send SMS with verification code using Twilio
                let sms_message = format!(
                    "Your D2C verification code is: {}.  Expires in 60 minutes.",
                    phone_code
                );

                app.twilio()
                    .send_sms(app, "default", &phone, &sms_message)
                    .await?;

                let now = granite::Utc::now();

                // update the database
                granite::pg_execute!(
                    db = dbcx;
                    args = {
                        $signup_advisor_uuid: &input.signup_advisor_uuid,
                        $phone_code: &phone_code,
                        $now: &now,
                    };
                    UPDATE
                        df4l.signup_advisor
                    SET
                        phone_code = $phone_code,
                        phone_code_sent_ts = $now::timestamptz,
                        phone_code_expire_ts = $now::timestamptz + "1 hour"::interval
                    WHERE
                        signup_advisor_uuid = $signup_advisor_uuid
                )
                .await?;

                now
            }
        };

        Ok(Output {
            signup_advisor_uuid: input.signup_advisor_uuid,
            email,
            phone,
            email_code_sent_ts: email_code_sent_ts.into(),
            phone_code_sent_ts: phone_code_sent_ts.into(),
            email_verified: row.email_verified_ts.is_some(),
            phone_verified: row.phone_verified_ts.is_some(),
        })
    }
}

/// ### Verifies the phone number using a user-provided code.
/// * Checks the code against the one stored in the database.
/// * Handles code expiration and limits the number of attempts.
/// * Marks the phone number as verified upon successful code entry.
#[approck::api]
pub mod signup_advisor_verify_phone {
    use granite::{return_authorization_error, return_process_error};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
        pub phone_code: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub phone: String,
        pub status: Status,
    }

    #[granite::gtype(ApiOutput)]
    pub enum Status {
        Verified,
        Error(String),
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_write(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_write");
        }

        // Get current phone code and verify status from database
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                phone: Option<String>,
                phone_code: Option<String>,
                phone_code_expired: Option<bool>,
                phone_code_attempts: i32,
                phone_verified_ts: Option<DateTimeUtc>,
            };
            SELECT
                phone,
                phone_code,
                phone_code_expire_ts < NOW() AS phone_code_expired,
                phone_code_attempts,
                phone_verified_ts
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        // if the number of attempts is > 8, then fail, otherwise, update the number of attempts
        if row.phone_code_attempts > 8 {
            return Ok(Output {
                phone: row.phone.unwrap_or_default(),
                status: Status::Error("Too many attempts.  Please resend the code.".to_string()),
            });
        } else {
            granite::pg_execute!(
                db = dbcx;
                args = {
                    $signup_advisor_uuid: &input.signup_advisor_uuid,
                };
                UPDATE
                    df4l.signup_advisor
                SET
                    phone_code_attempts = phone_code_attempts + 1
                WHERE
                    signup_advisor_uuid = $signup_advisor_uuid
            )
            .await?;
        }

        let phone = match row.phone {
            Some(phone) => phone,
            None => {
                return_process_error!(
                    "Phone number is required.  Please go back to the previous step and enter it."
                );
            }
        };

        // if it is aready verified, return success
        if row.phone_verified_ts.is_some() {
            return Ok(Output {
                phone,
                status: Status::Verified,
            });
        }

        // if there is no code, return error
        let phone_code = match row.phone_code {
            Some(code) if code.len() == 4 => code,
            _ => {
                return Ok(Output {
                    phone,
                    status: Status::Error(
                        "No phone verification code on file.  Please resend.".to_string(),
                    ),
                });
            }
        };

        // verify the code isn't expired
        match row.phone_code_expired {
            Some(false) => {
                // not expired
            }
            _ => {
                return Ok(Output {
                    phone,
                    status: Status::Error(
                        "Phone verification code has expired.  Please resend.".to_string(),
                    ),
                });
            }
        };

        // verify the code matches
        if phone_code != input.phone_code {
            return Ok(Output {
                phone,
                status: Status::Error(format!(
                    "Phone verification code \"{}\" is incorrect.",
                    input.phone_code
                )),
            });
        }

        // update database
        let now = granite::Utc::now();
        granite::pg_execute!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
                $phone_verified_ts: &now,
            };
            UPDATE
                df4l.signup_advisor
            SET
                phone_verified_ts = $phone_verified_ts
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        // return success
        Ok(Output {
            phone,
            status: Status::Verified,
        })
    }
}

/// ### Verifies the email address using a user-provided code.
/// * Checks the code against the one stored in the database.
/// * Handles code expiration and limits the number of attempts.
/// * Marks the email as verified upon successful code entry.
#[approck::api]
pub mod signup_advisor_verify_email {
    use granite::{return_authorization_error, return_process_error};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
        pub email_code: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub email: String,
        pub status: Status,
    }

    #[granite::gtype(ApiOutput)]
    pub enum Status {
        Verified,
        Error(String),
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_write(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_write");
        }

        // Get current email code and verify status from database
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                email: Option<String>,
                email_code: Option<String>,
                email_code_expired: Option<bool>,
                email_code_attempts: i32,
                email_verified_ts: Option<DateTimeUtc>,
            };
            SELECT
                email,
                email_code,
                email_code_expire_ts < NOW() AS email_code_expired,
                email_code_attempts,
                email_verified_ts
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        // if the number of attempts is > 8, then fail, otherwise, update the number of attempts
        if row.email_code_attempts > 8 {
            return Ok(Output {
                email: row.email.unwrap_or_default(),
                status: Status::Error("Too many attempts.  Please resend the code.".to_string()),
            });
        } else {
            granite::pg_execute!(
                db = dbcx;
                args = {
                    $signup_advisor_uuid: &input.signup_advisor_uuid,
                };
                UPDATE
                    df4l.signup_advisor
                SET
                    email_code_attempts = email_code_attempts + 1
                WHERE
                    signup_advisor_uuid = $signup_advisor_uuid
            )
            .await?;
        }

        let email = match row.email {
            Some(email) => email,
            None => {
                return_process_error!(
                    "Email is required.  Please go back to the previous step and enter it."
                );
            }
        };

        // if it is aready verified, return success
        if row.email_verified_ts.is_some() {
            return Ok(Output {
                email,
                status: Status::Verified,
            });
        }

        // if there is no code, return error
        let email_code = match row.email_code {
            Some(code) if code.len() == 4 => code,
            _ => {
                return Ok(Output {
                    email,
                    status: Status::Error(
                        "No email verification code on file.  Please resend.".to_string(),
                    ),
                });
            }
        };

        // verify the code isn't expired
        match row.email_code_expired {
            Some(false) => {
                // not expired
            }
            _ => {
                return Ok(Output {
                    email,
                    status: Status::Error(
                        "Email verification code has expired.  Please resend.".to_string(),
                    ),
                });
            }
        };

        // verify the code matches
        if email_code != input.email_code {
            return Ok(Output {
                email,
                status: Status::Error(format!(
                    "Email verification code {} is incorrect.",
                    input.email_code
                )),
            });
        }

        // update database
        let now = granite::Utc::now();
        granite::pg_execute!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
                $email_verified_ts: &now,
            };
            UPDATE
                df4l.signup_advisor
            SET
                email_verified_ts = $email_verified_ts
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        // return success
        Ok(Output {
            email,
            status: Status::Verified,
        })
    }
}

/// ### Retrieves the legal terms and conditions for acceptance.
/// * Fetches the active "AdvisorAgreement" document from the `legal_plane` module.
/// * Returns the document content (HTML), UUID, revision, and whether the terms have already been accepted.
#[approck::api]
pub mod signup_advisor_terms_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub document_uuid: Uuid,
        pub revision: String,
        pub terms_html: String,
        pub terms_accepted_name: Option<String>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_read(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_read");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                terms_accepted_name: Option<String>,
            };
            SELECT
                terms_accepted_name
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        let legal_plane_document =
            match legal_plane::core::load_active_by_psid(&dbcx, "AdvisorAgreement").await? {
                Some(document) => document,
                None => {
                    return Err(granite::Error::process_error(
                        "Unable to find current terms document. Please contact support."
                            .to_string(),
                    ));
                }
            };

        Ok(Output {
            document_uuid: legal_plane_document.document_uuid,
            revision: legal_plane_document.revision,
            terms_html: legal_plane_document.body_html,
            terms_accepted_name: row.terms_accepted_name,
        })
    }
}

/// ### Records the user's acceptance of the terms.
/// * Validates that the document version being accepted is still the current one.
/// * Stores the user's name, remote address, and timestamp of acceptance.
#[approck::api]
pub mod signup_advisor_terms_set {
    use granite::{NestedError, return_authorization_error};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,

        // for confirmation that nothing changed during the user's review
        pub document_uuid: Uuid,

        // for confirmation that nothing changed during the user's review
        pub revision: String,

        /// Users first and last name
        pub terms_accepted_name: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_write(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_write");
        }

        // get the current terms document from the legal plane module
        let legal_plane_document =
            match legal_plane::core::load_active_by_psid(&dbcx, "AdvisorAgreement").await? {
                Some(document) => document,
                None => {
                    return Err(granite::Error::process_error(
                        "Unable to find current terms document. Please contact support."
                            .to_string(),
                    ));
                }
            };

        if legal_plane_document.document_uuid != input.document_uuid {
            return Err(granite::Error::process_error(
                "Document content has changed. Please refresh.".to_string(),
            ));
        }

        if legal_plane_document.revision != input.revision {
            return Err(granite::Error::process_error(
                "Document content has changed. Please refresh.".to_string(),
            ));
        }

        // pull the first and last name from the signup table
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                first_name: Option<String>,
                last_name: Option<String>,
            };
            SELECT
                first_name,
                last_name
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        let remote_addr = identity.remote_addr();

        let terms = match (crate::core::signup::advisor::Terms_Partial {
            first_name: row.first_name,
            last_name: row.last_name,
            terms_accepted_name: Some(input.terms_accepted_name),
            document_uuid: Some(legal_plane_document.document_uuid),
            revision: Some(legal_plane_document.revision),
            remote_addr: Some(remote_addr),
        })
        .validate()
        {
            Ok(terms) => terms,
            Err(errors) => {
                return Ok(Response::ValidationError(NestedError {
                    outer: "Validation Error".to_string(),
                    inner: Some(Input_Error {
                        signup_advisor_uuid: None,
                        document_uuid: None,
                        revision: None,
                        terms_accepted_name: errors.terms_accepted_name,
                    }),
                }));
            }
        };

        let remote_addr = identity.get_address();

        granite::pg_execute!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
                $terms_document_uuid: &terms.document_uuid,
                $terms_revision: &terms.revision,
                $terms_accepted_name: &terms.terms_accepted_name,
                $terms_accepted_addr: &remote_addr,
            };
            UPDATE
                df4l.signup_advisor
            SET
                terms_document_uuid = $terms_document_uuid,
                terms_revision = $terms_revision,
                terms_accepted_name = $terms_accepted_name,
                terms_accepted_addr = $terms_accepted_addr::text::cidr
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        Ok(Response::Output(Output {}))
    }
}

/// ### Creates a Stripe customer and a billing portal link.
/// * If a Stripe customer ID doesn't already exist for the signup, it creates one.
/// * Generates and returns a URL for the Stripe billing portal where the user can enter payment details.
#[approck::api]
pub mod signup_advisor_billing_save {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_write(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_write");
        }

        //Find first and last name, email from signup_advisor_uuid
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                first_name: String,
                last_name: String,
                email: Option<String>,
                stripe_customer_id: Option<String>,
            };
            SELECT
                first_name,
                last_name,
                email,
                stripe_customer_id
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        let customer_name = format!("{} {}", row.first_name, row.last_name);
        let customer_email = row.email.unwrap_or_default();
        let mut stripe_customer_id = row.stripe_customer_id.unwrap_or_default();

        // Check if stripe_customer_id exists and is not empty
        let has_stripe_customer = !stripe_customer_id.trim().is_empty();

        println!("has_stripe_customer: {:?}", has_stripe_customer);
        println!("customer_name: {:?}", customer_name);
        println!("customer_email: {:?}", customer_email);

        // Create customer if no valid stripe_customer_id exists
        if !has_stripe_customer {
            let stripe_customer = app
                .stripe()
                .create_customer(&customer_name, &customer_email)
                .await?;
            println!("Created Stripe customer: {:?}", stripe_customer);

            granite::pg_execute!(
                db = dbcx;
                args = {
                    $signup_advisor_uuid: &input.signup_advisor_uuid,
                    $stripe_customer_id: &stripe_customer.id,
                };
                UPDATE
                    df4l.signup_advisor
                SET
                    stripe_customer_id = $stripe_customer_id
                WHERE
                    signup_advisor_uuid = $signup_advisor_uuid
            )
            .await?;
            stripe_customer_id = stripe_customer.id;
        }

        // Create billing portal link
        let return_url = format!(
            "{}/signup/advisor/{}/billing",
            "https://local.acp7.net:3014", // TODO
            input.signup_advisor_uuid
        );
        let checkout_session = app
            .stripe()
            .checkout_session_create(&stripe_customer_id, &return_url, &return_url)
            .await?;
        println!("Created Stripe checkout session: {:?}", checkout_session);
        // if we have checkout_session let's update signup_advisor
        granite::pg_execute!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
                $checkout_session_id: &checkout_session.id,
                $payment_status: &checkout_session.payment_status,
            };
            UPDATE
                df4l.signup_advisor
            SET
                stripe_checkout_session_id = $checkout_session_id,
                stripe_payment_status = $payment_status
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        // Note, url is only returned if payment_status is not "paid"
        if checkout_session.url.is_none() {
            return Err(granite::Error::process_error(
                "Payment already completed. Please contact support.".to_string(),
            ));
        }

        Ok(Output {
            url: checkout_session.url.unwrap_or_default(),
        })
    }
}

#[approck::api]
//API to retrive stripe data
pub mod stripe_data {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub stripe_customer_id: Option<String>,
        pub stripe_checkout_session_id: Option<String>,
        pub stripe_payment_status: Option<String>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_read(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_read");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                stripe_customer_id: Option<String>,
                stripe_checkout_session_id: Option<String>,
                stripe_payment_status: Option<String>,
            };
            SELECT
                stripe_customer_id,
                stripe_checkout_session_id,
                stripe_payment_status
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        Ok(Output {
            stripe_customer_id: row.stripe_customer_id,
            stripe_checkout_session_id: row.stripe_checkout_session_id,
            stripe_payment_status: row.stripe_payment_status,
        })
    }
}

/// ### Creates the identity and login records for the new advisor account.
/// * Validates the password strength and confirmation.
/// * Creates records in auth_fence.identity and auth_fence.login tables.
/// * Creates the df4l.advisor record and links it to the identity.
/// * Completes the signup process without storing passwords in signup_advisor.
#[approck::api]
pub mod signup_advisor_complete {
    use granite::NestedError;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
        pub password: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub advisor_uuid: Uuid,
        pub next_url: String,
    }

    /// TRANSACTION-BLOCK
    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let mut dbcx = app.postgres_dbcx().await?;
        let dbtx = dbcx.transaction().await?;

        if !identity
            .signup_advisor_write(&dbtx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_write");
        }

        let password_strength = granite::password::password_analysis(&input.password);
        if !password_strength.acceptable {
            let input_error = Input_Error {
                signup_advisor_uuid: None,
                password: Some("Password does not meet strength requirements".into()),
            };
            return Ok(Response::ValidationError(NestedError {
                outer: "Validation Error".to_string(),
                inner: Some(input_error),
            }));
        }

        // get core signup data
        let signup_data =
            crate::core::signup::advisor::Signup::load(&dbtx, input.signup_advisor_uuid).await?;

        let contact_info = match signup_data.contact {
            Ok(contact) => contact,
            Err(_) => {
                return Ok(Response::ValidationError(NestedError {
                    outer:
                        "Contact information is incomplete.  Please go back and correct any errors."
                            .to_string(),
                    inner: None,
                }));
            }
        };

        let _verify_info = match signup_data.verify {
            Ok(verify) => verify,
            Err(_) => {
                return Ok(Response::ValidationError(NestedError {
                    outer: "Verification is incomplete.  Please go back and correct any errors."
                        .to_string(),
                    inner: None,
                }));
            }
        };

        let _terms_info = match signup_data.terms {
            Ok(terms) => terms,
            Err(_) => {
                return Ok(Response::ValidationError(NestedError {
                    outer: "Terms and Conditions have not been accepted.  Please go back and correct any errors.".to_string(),
                    inner: None,
                }));
            }
        };

        // Call auth-fence core to create identity and login records
        let create_data = auth_fence::core::signup::CreateIdentityData {
            first_name: contact_info.first_name.clone(),
            last_name: contact_info.last_name.clone(),
            email: contact_info.email.clone(),
            phone: Some(contact_info.phone.clone()),
            password: input.password,
        };

        let create_identity_result = create_data.create_identity_with_login(app).await?;

        let identity_uuid = create_identity_result.identity_uuid;

        // Create session for the newly created user to automatically log them in
        let mut redis = app.redis_dbcx().await?;
        let session_token = identity.session_token();

        let identity_details = auth_fence::api::identity::Identity {
            identity_uuid,
            identity_type: auth_fence::api::identity::IdentityType::User,
            name: format!("{} {}", contact_info.first_name, contact_info.last_name),
            email: Some(contact_info.email.clone()),
            note: None,
            avatar_uri: None,
            active: true,
        };

        // Store identity info in redis to log the user in
        auth_fence::api::identity::set_redis_session(&mut redis, &session_token, &identity_details)
            .await?;

        // Log the authentication event
        let remote_address = identity.remote_address().to_string();
        auth_fence::postgres::log::auth_log(
            &dbtx,
            auth_fence::postgres::log::AuthLogData {
                create_addr: remote_address,
                session_token: session_token.clone(),
                identity_uuid: Some(identity_uuid),
                user_esid: Some(create_identity_result.username),
                user_email: Some(contact_info.email.clone()),
                auth_type: "Signup".to_string(),
                auth_action: "SignupComplete".to_string(),
                auth_provider: Some("AdvisorSignup".to_string()),
                success: true,
                blocked: false,
                data: None,
            },
        )
        .await?;

        // Generate unique advisor_esid using a single query
        let advisor_esid = granite::pg_row!(
            db = dbtx;
            row = {
                advisor_esid: String,
            };
            r#"
            WITH potential_esids AS (
                SELECT
                    LPAD((RANDOM() * 999)::int::text, 3, '0') || '-' ||
                    LPAD((RANDOM() * 999)::int::text, 3, '0') AS advisor_esid
                FROM generate_series(1, 100)
            )
            SELECT 
                advisor_esid
            FROM 
                potential_esids
            WHERE true
                AND advisor_esid NOT IN (
                    SELECT advisor_esid
                    FROM df4l.advisor
                    WHERE advisor_esid IS NOT NULL
                )
            LIMIT 1
            "#
        )
        .await?
        .advisor_esid;

        let advisor_result = granite::pg_row!(
            db = dbtx;
            args = {
                $identity_uuid: &identity_uuid,
                $advisor_esid: &advisor_esid,
                $first_name: &contact_info.first_name,
                $last_name: &contact_info.last_name,
                $email: &contact_info.email,
                $phone: &contact_info.phone,
            };
            row = {
                advisor_uuid: Uuid,
            };
            INSERT INTO df4l.advisor (
                identity_uuid,
                advisor_esid,
                first_name,
                last_name,
                email,
                phone
            )
            VALUES (
                $identity_uuid,
                $advisor_esid,
                $first_name,
                $last_name,
                $email,
                $phone
            )
            RETURNING
                advisor_uuid
        )
        .await?;

        let advisor_uuid = advisor_result.advisor_uuid;

        // Update signup_advisor record with the new advisor_uuid
        granite::pg_execute!(
            db = dbtx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
                $advisor_uuid: &advisor_uuid,
            };
            UPDATE
                df4l.signup_advisor
            SET
                advisor_uuid_created = $advisor_uuid,
                signup_completed_ts = now()
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        // commit
        dbtx.commit().await?;

        Ok(Response::Output(Output {
            advisor_uuid,
            next_url: "/dashboard".to_string(),
        }))
    }
}
