#[approck::http(GET /realrr/resource; AUTH None; return HTML | Redirect;)]
pub mod page {
    async fn request(doc: Document) -> Response {
        use maud::html;

        doc.add_body(html!(
            rrr-adv-resources."bux-narrow-75" {
                h1 { "The Real Return Reporter™ Advisor Resources" }
                p { "Fusce consequat lacus augue, non iaculis ligula dignissim non." }
                grid-2 {
                    panel {
                        header {
                            h5 { "RRR Analysis Report" }
                        }
                        content {
                            a href="https://asset7.net/Zagula/Smart/Debt2Capital/advisor-resources/GBU_FIA-REPORT.pdf" target="_blank" {
                                img src="https://asset7.net/Zagula/Smart/Debt2Capital/advisor-resources/rrr-analysis-cover-image.png" {}
                            }
                        }
                        footer {
                            a href="https://asset7.net/Zagula/Smart/Debt2Capital/advisor-resources/GBU_FIA-REPORT.pdf" target="_blank" { "View/Download" }
                        }
                    }
                    panel {
                        header {
                            h5 { "RRR Powerpoint" }
                        }
                        content {
                            a href="https://asset7.net/Zagula/Smart/Debt2Capital/advisor-resources/RRR+Pres+2025.pptx" target="_blank" {
                                img src="https://asset7.net/Zagula/Smart/Debt2Capital/advisor-resources/rrr-powerpoint-cover-image.png" {}
                            }
                        }
                        footer {
                            a href="https://asset7.net/Zagula/Smart/Debt2Capital/advisor-resources/RRR+Pres+2025.pptx" target="_blank" { "View/Download" }
                        }
                    }
                    panel {
                        header {
                            h5 { "Whitepaper Demo" }
                        }
                        content {
                            iframe src="https://www.youtube.com/embed/SU8GuB55f5Q?si=h9GsvwgbKkOA9swS" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen {}
                        }
                    }
                    panel {
                        header {
                            h5 { "Software Demo" }
                        }
                        content {
                            iframe src="https://www.youtube.com/embed/SU8GuB55f5Q?si=h9GsvwgbKkOA9swS" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen {}
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
