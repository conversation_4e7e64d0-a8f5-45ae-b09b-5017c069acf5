pub mod api;
pub mod core;
pub mod web;

pub trait App: approck::App + auth_fence::App + approck_redis::App + approck_postgres::App {
    fn df4l_crs_module(&self) -> &self::ModuleStruct;
}

pub trait Identity: approck::Identity + auth_fence::Identity {
    fn web_usage(&self) -> bool;
    fn api_usage(&self) -> bool;
}

#[derive(serde::Deserialize)]
pub struct ModuleConfig {}
pub struct ModuleStruct {}

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;
    async fn new(_config: Self::Config) -> granite::Result<Self> {
        Ok(Self {})
    }
    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}

pub trait Document: bux::document::<PERSON><PERSON> {}
