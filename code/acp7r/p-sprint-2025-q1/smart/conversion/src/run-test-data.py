import hashlib
import secrets
import time
from faker import Faker
import json
#from database import get_source_db, get_target_db
import psycopg2



fake = Faker('en_US')

import argparse

# Parse command line arguments
parser = argparse.ArgumentParser(description='Generate test data for agencies')
parser.add_argument('--seed', type=str, default="DEFAULT", help='Optional seed for random generation')
args = parser.parse_args()

# Use provided seed or generate a random one
SEED = args.seed

# Hardcode the db connection
DB = psycopg2.connect(
    host="127.0.0.1",
    port=20000,
    database="df4l",
    user="app",
    password="pass"
)
DB.autocommit = True
CURSOR = DB.cursor()

fake.seed_instance(SEED)


def gen():
    for identity in identity_table:
        print(f"Processing identity: {identity.name}")

def pick_one_of(seed, some_list):
    hash = hashlib.sha256(seed.encode()).hexdigest()
    index = int(hash, 16) % len(some_list)
    return some_list[index]

def generate_uuidv7(seed) -> str:
    # generate a deterministic UUID from seed
    hash = hashlib.sha256(seed.encode()).hexdigest()
    
    # Force first 5 characters to be 0
    modified_hash = "00000" + hash[5:]
    
    # Format as UUIDv7
    return f"{modified_hash[:8]}-{modified_hash[8:12]}-7{modified_hash[12:15]}-{modified_hash[15:19]}-{modified_hash[19:31]}"

def generate_agency_esid(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return f"{hash[:2]}-{hash[2:6]}"

def generate_advisor_esid(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return f"{hash[:3]}-{hash[3:6]}"

def generate_timestamp(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    # Use modulo to limit to reasonable range (e.g., 2010-2030)
    base_timestamp = 1262304000  # 2010-01-01 00:00:00
    range_seconds = 631152000    # ~20 years in seconds
    timestamp = base_timestamp + (int(hash[:8], 16) % range_seconds)
    return time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(timestamp))

def generate_date(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    # Use modulo to limit to reasonable range (e.g., 2010-2030)
    base_timestamp = 1262304000  # 2010-01-01 00:00:00
    range_seconds = 631152000    # ~20 years in seconds
    timestamp = base_timestamp + (int(hash[8:16], 16) % range_seconds)
    return time.strftime('%Y-%m-%d', time.gmtime(timestamp))

def active_or_not(seed) -> bool:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return int(hash[:1], 16) % 10 > 0

def generate_note(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    
    if int(hash[:1], 16) % 2 == 0:
        return None
    else:
        return f"Note for {hash[:10]}"
    
def generate_client_note(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return f"Note for {hash[:10]}"
    
def generate_phone(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    fake.seed_instance(int(hash[:8], 16))
    
    # Use a custom phone number format that's guaranteed to be under 20 chars
    phone_number = fake.numerify(text="###-###-####")
    
    return phone_number

def generate_email(seed, first_name) -> str:
    domain = pick_one_of(seed, DOMAIN_LIST)
    name = first_name.lower().replace(" ", "")
    return f"{name}@{domain}"

def generate_monthly_budget(seed) -> float:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return int(hash[7:15], 16) % 1000000 / 100.0

def generate_insurance_premium(seed) -> float:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return int(hash[:8], 16) % 1000000 / 100.0

def generate_insurance_pua(seed) -> float:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return int(hash[:8], 16) % 100000 / 100.0

def generate_net_cash_at_end(seed) -> float:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return int(hash[8:16], 16) % 1000000 / 100.0

def generate_balance(seed) -> float:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    fake.seed_instance(int(hash[:8], 16))
    return fake.random_int(min=10000, max=1000000)

def generate_interest_rate() -> float:
    return fake.pyfloat(min_value=0, max_value=10, right_digits=2)

def generate_monthly_payment(balance, seed) -> float:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    fake.seed_instance(int(hash[:8], 16))
    return round(balance / fake.random_int(min=10, max=100), 2)

def generate_crs_debt_esid(seed) -> str:
    """Generate CRS debt ESID in format: crs-[8-digit]-[7-digit]-[7-digit]"""
    hash = hashlib.sha256(seed.encode()).hexdigest()

    # Extract numbers from hash and format them
    part1 = str(int(hash[:8], 16))[-8:].zfill(8)
    part2 = str(int(hash[8:16], 16))[-7:].zfill(7)
    part3 = str(int(hash[16:24], 16))[-7:].zfill(7)

    return f"crs-{part1}-{part2}-{part3}"

def is_crs_debt(seed) -> bool:
    """Determine if debt should be CRS (85%) or manual (15%)"""
    hash = hashlib.sha256(seed.encode()).hexdigest()
    # Use modulo 100 to get percentage, 85% should be CRS
    return (int(hash[:2], 16) % 100) < 85
 
ADVISOR_PART_OF_AGENCY = [1, 0, 0]

AGENCY_COUNT = [3,5,6,8,4,34]

AGENCY_NAME_LIST = [
    "Admin Group",
    "Brokers' Choice of America",
    "Cincy SMART",
    "Equis Financial",
    "FFL",
    "GBU",
    "Provisor",
    "River Rock Advisors",
    "RRR",
    "Rick Law Agency",
    "SMART Advisor Network News: KD",
    "SMART Retirement",
    "SOS",
    "Trustworthy",
    "Summit Financial Group",
    "Legacy Wealth Partners",
    "Cornerstone Advisory",
    "Elite Benefits Team",
    "Guardian Financial",
    "Horizon Planning Group",
    "Integrity First Advisors",
    "KeyPoint Solutions",
    "Liberty Financial Services",
    "Meridian Wealth Management",
    "North Star Advisors",
    "Oakmont Strategic Group",
    "Pacific Coast Planning",
    "Quantum Financial",
    "Reliable Insurance Group",
    "Secure Path Advisors",
    "Steadfast Solutions",
    "True North Financial",
    "Unity Insurance Partners",
    "Venture Wealth Advisors"
]

NAME_LIST = [
    # Short names
    dict(first_name="Al", last_name="Wu"),
    dict(first_name="Bo", last_name="Li"),
    dict(first_name="Ty", last_name="Ng"),
    dict(first_name="Ed", last_name="Ko"),
    dict(first_name="Mo", last_name="Yu"),
    
    # Long names
    dict(first_name="Christopher", last_name="Montgomery-Wells"),
    dict(first_name="Alexandria", last_name="Van der Woodsen"),
    dict(first_name="Maximilian", last_name="Worthington III"),
    dict(first_name="Evangelina", last_name="Rodriguez-Martinez"),
    dict(first_name="Sebastian", last_name="Thorne-Blackwood"),
    
    # Normal names
    dict(first_name="James", last_name="Smith"),
    dict(first_name="Maria", last_name="Garcia"),
    dict(first_name="David", last_name="Johnson"),
    dict(first_name="Sarah", last_name="Williams"),
    dict(first_name="Michael", last_name="Brown"),
    
    # Special characters
    dict(first_name="Jean-Pierre", last_name="D'Arcy"),
    dict(first_name="María-José", last_name="O'Connor"),
    dict(first_name="André", last_name="St. James"),
    dict(first_name="François", last_name="Le'Blanc"),
    dict(first_name="Zoë", last_name="McKenzie"),
    
    # International
    dict(first_name="Wei", last_name="Zhang"),
    dict(first_name="Yuki", last_name="Tanaka"),
    dict(first_name="Priya", last_name="Patel"),
    dict(first_name="Jung-Ho", last_name="Kim"),
    dict(first_name="Olga", last_name="Ivanova"),
    
    # Compound
    dict(first_name="Mary Jane", last_name="Watson-Parker"),
    dict(first_name="John Paul", last_name="Jones-Smith"),
    dict(first_name="Anna Marie", last_name="von Trapp"),
    dict(first_name="Jean Claude", last_name="Van Damme"),
    dict(first_name="Billy Bob", last_name="O'Reilly"),
    
    # Professional titles
    dict(first_name="Dr. Robert", last_name="Wilson"),
    dict(first_name="Rev. Michael", last_name="Thomas"),
    dict(first_name="Hon. Elizabeth", last_name="Warren"),
    dict(first_name="Prof. William", last_name="White"),
    dict(first_name="Capt. James", last_name="Kirk"),
    
    # Single name
    dict(first_name="Madonna", last_name=""),
    dict(first_name="Cher", last_name=""),
    dict(first_name="Prince", last_name=""),
    dict(first_name="Bono", last_name=""),
    dict(first_name="Seal", last_name=""),
]

FIRST_NAME_LIST = [
    # Short names
    "Al", "Bo", "Ty", "Ed", "Mo",
    
    # Long names
    "Christopher", "Alexandria", "Maximilian", "Evangelina", "Sebastian",
    
    # Normal names
    "James", "Maria", "David", "Sarah", "Michael",
    
    # Special characters
    "Jean-Pierre", "María-José", "André", "François", "Zoë",
    
    # International
    "Wei", "Yuki", "Priya", "Jung-Ho", "Olga",
    
    # Compound
    "Mary Jane", "John Paul", "Anna Marie", "Jean Claude", "Billy Bob",
    
    # Professional titles
    "Dr. Robert", "Rev. Michael", "Hon. Elizabeth", "Prof. William", "Capt. James",
    
    # Single names
    "Madonna", "Cher", "Prince", "Bono", "Seal"
]

LAST_NAME_LIST = [
    # Short names
    "Wu", "Li", "Ng", "Ko", "Yu",
    
    # Long names
    "Montgomery-Wells", "Van der Woodsen", "Worthington III", "Rodriguez-Martinez", "Thorne-Blackwood",
    
    # Normal names
    "Smith", "Garcia", "Johnson", "Williams", "Brown",
    
    # Special characters
    "D'Arcy", "O'Connor", "St. James", "Le'Blanc", "McKenzie",
    
    # International
    "Zhang", "Tanaka", "Patel", "Kim", "Ivanova",
    
    # Compound
    "Watson-Parker", "Jones-Smith", "von Trapp", "Van Damme", "O'Reilly",
    
    # Professional
    "Wilson", "Thomas", "Warren", "White", "Kirk",
    
    # Empty for single names
    ""
]

ADDRESS_LIST = [
    # Short
    dict(line1="123 Main", line2=None, city="Anytown", state="CA", zip="12345", country="USA"),
    dict(line1="45 Oak", line2=None, city="Troy", state="NY", zip="12180", country="USA"),
    dict(line1="7 Pine", line2=None, city="Dover", state="DE", zip="19901", country="USA"),
    dict(line1="9 Elm", line2=None, city="Salem", state="MA", zip="01970", country="USA"),
    dict(line1="12 Cedar", line2=None, city="Rome", state="GA", zip="30161", country="USA"),
    # Long
    dict(line1="4567 NorthWest Oak Avenue", line2="Apartment 12B, Building C, Floor 3", city="Springfield", state="IL", zip="62701", country="USA"),
    dict(line1="7890 South Pine Road Extension", line2="Suite 204, The Grand Pines Complex", city="Smallville", state="KS", zip="66002", country="USA"),
    dict(line1="1011 East Elm Street Boulevard", line2="Unit B-17, Lower Level, Garden View", city="Boulder", state="CO", zip="80301", country="USA"),
    dict(line1="3210 Cedar Lane Drive Northeast", line2="Penthouse 9, Tower II, Historic District", city="Savannah", state="GA", zip="31401", country="USA"),
    dict(line1="6543 Birch Drive West", line2="Corporate Suite 300, Birch Business Park", city="Portland", state="OR", zip="97201", country="USA"),
    # Normal
    dict(line1="987 Maple Ct", line2=None, city="Asheville", state="NC", zip="28801", country="USA"),
    dict(line1="147 Willow Way", line2="Apt 4C", city="Miami", state="FL", zip="33101", country="USA"),
    dict(line1="258 Spruce St", line2=None, city=" Boise", state="ID", zip="83702", country="USA"),
    dict(line1="369 Laurel Dr", line2="Unit 8", city="Austin", state="TX", zip="78701", country="USA"),
    dict(line1="741 Sycamore Ave", line2=None, city="Madison", state="WI", zip="53703", country="USA"),
    # Abnormal
    dict(line1="13 Moonbeam Way", line2="Shack 3", city="Roswell", state="NM", zip="88201", country="USA"),
    dict(line1="1 Gator Swamp Rd", line2="Trailer 9", city="Gainesville", state="FL", zip="32601", country="USA"),
    dict(line1="666 Haunted Hollow", line2=None, city="Salem", state="OR", zip="97301", country="USA"),
    dict(line1="0 Nowhere Ln", line2="Box 42", city="Eureka", state="NV", zip="89316", country="USA"),
    dict(line1="9999 Unicorn Trail", line2="Glitter Barn", city="Bozeman", state="MT", zip="59715", country="USA"),
    # Short
    dict(line1="22 Birch", line2=None, city="Fargo", state="ND", zip="58102", country="USA"),
    dict(line1="8 Maple", line2=None, city="Bangor", state="ME", zip="04401", country="USA"),
    dict(line1="15 Elm", line2=None, city="Provo", state="UT", zip="84601", country="USA"),
    dict(line1="3 Oak", line2=None, city="Tulsa", state="OK", zip="74103", country="USA"),
    dict(line1="77 Pine", line2=None, city="Mobile", state="AL", zip="36602", country="USA"),
    # Long
    dict(line1="12345 West Magnolia Boulevard", line2="Condominium 56A, Tower 1, Upper Deck", city="Charleston", state="SC", zip="29401", country="USA"),
    dict(line1="67890 Northern Starlight Avenue", line2="Estate 12, Gated Community, Lot 89", city="Anchorage", state="AK", zip="99501", country="USA"),
    dict(line1="23456 Riverfront Parkway South", line2="Office 401, Riverside Corporate Center", city="Memphis", state="TN", zip="38103", country="USA"),
    dict(line1="78901 Coastal Highway East", line2="Beachfront Villa 7, Second Floor, Oceanview", city="Virginia Beach", state="VA", zip="23451", country="USA"),
    dict(line1="34567 Mountain Ridge Trail", line2="Cabin 23, Alpine Meadows Subdivision", city="Flagstaff", state="AZ", zip="86001", country="USA"),
    # Normal
    dict(line1="852 Chestnut St", line2=None, city="Lincoln", state="NE", zip="68508", country="USA"),
    dict(line1="963 Poplar Ave", line2="Apt 202", city="Columbus", state="OH", zip="43215", country="USA"),
    dict(line1="174 Aspen Dr", line2=None, city="Reno", state="NV", zip="89501", country="USA"),
    dict(line1="285 Magnolia Ln", line2="Unit 5B", city="Baton Rouge", state="LA", zip="70801", country="USA"),
    dict(line1="396 Walnut St", line2=None, city="Des Moines", state="IA", zip="50309", country="USA"),
    # Abnormal
    dict(line1="4 Dragonfly Alley", line2="Hut 2", city="Asheville", state="NC", zip="28802", country="USA"),
    dict(line1="13 Sasquatch Crossing", line2=None, city="Eureka", state="CA", zip="95501", country="USA"),
    dict(line1="7 Mermaid Cove", line2="Boat Slip 8", city="Key West", state="FL", zip="33040", country="USA"),
    dict(line1="0 Zero Point Rd", line2="Tent 6", city="Moab", state="UT", zip="84532", country="USA"),
    dict(line1="1111 Stardust Loop", line2="Rocket Pad 4", city="Houston", state="TX", zip="77002", country="USA"),
    # Short
    dict(line1="5 Laurel", line2=None, city="Sioux Falls", state="SD", zip="57103", country="USA"),
    dict(line1="9 Spruce", line2=None, city="Concord", state="NH", zip="03301", country="USA"),
    dict(line1="11 Cedar", line2=None, city="Cheyenne", state="WY", zip="82001", country="USA"),
    dict(line1="33 Pine", line2=None, city="Montpelier", state="VT", zip="05602", country="USA"),
    dict(line1="2 Oak", line2=None, city="Huntington", state="WV", zip="25701", country="USA"),
    # Long
    dict(line1="45678 Central Park Avenue Northwest", line2="Penthouse 12, Executive Towers, Suite 900", city="Oklahoma City", state="OK", zip="73102", country="USA"),
    dict(line1="89012 Eastern Shoreline Drive", line2="Mansion 3, Historic Coastal Estates", city="Newport", state="RI", zip="02840", country="USA"),
    dict(line1="56789 Downtown Plaza Boulevard", line2="Loft 45, Urban Center, Skyline View", city="Phoenix", state="AZ", zip="85003", country="USA"),
    dict(line1="123456 Golden Gate Parkway West", line2="Apartment 78C, Bridgeview Condominiums", city="San Francisco", state="CA", zip="94102", country="USA"),
    dict(line1="678901 Southern Hills Lane", line2="Farmhouse 5, Rolling Acres Subdivision", city="Lexington", state="KY", zip="40502", country="USA"),
    # Null addresses
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
]

DOMAIN_LIST = [
    "gmail.com",
    "yahoo.com",
    "hotmail.com",
    "outlook.com",
    "aol.com",
    "icloud.com",
    "protonmail.com",
    "zoho.com",
    "mail.com",
    "gmx.com",
    "yandex.com",
    "mail.ru",
    "inbox.com",
    "fastmail.com",
    "tutanota.com",
    "riseup.net",
]

TIMEZONE_LIST = [
    "GMT +0"
    "GMT +1",
    "GMT +2",
    "GMT +3",
    "GMT +4",
    "GMT +5",
    "GMT +6",
    "GMT +7",
    "GMT +8",
    "GMT +9",
    "GMT +10",
    "GMT +11",
    "GMT +12",
    "GMT -1",
    "GMT -2",
    "GMT -3",
    "GMT -4",
    "GMT -5",
    "GMT -6",
    "GMT -7",
    "GMT -8",
    "GMT -9",
    "GMT -10",
    "GMT -11",
    "GMT -12",
]

DEBT_NAME_LIST = [
    "Credit Card",
    "Car Loan",
    "Mortgage",
    "Student Loan",
    "Personal Loan",
    "Medical Debt",
    "Child Support",
    "Tax Debt",
    "Court Judgment",
    "Debt Consolidation Loan",
    "Home Equity Loan",
    "Business Loan",
    "Personal Guarantee",
    "Secured Loan",
    "Unsecured Loan",
    "Title Loan",
]

NUMBER_ADVISORS_FOR_IDENTITY = [1,1,1,2,2]
NUMBER_OF_CLIENTS_PER_ADVISOR = [8,9,10,9,8,9,10,14,15,20,25,30,10,8,9]
NUMBER_OF_DEBTS_PER_CLIENT = [5,6,7,8,9,10, 10, 10, 11, 12, 20, 18]

class Identity:
    @classmethod
    def list(cls):
        cursor = DB.cursor()  # Use dictionary cursor to get column names
        cursor.execute("""
            SELECT 
                identity_uuid, 
                identity_type, 
                name, 
                email, 
                note, 
                avatar_uri, 
                active,
                create_ts
            FROM 
                auth_fence.identity
            ORDER BY 
                name
        """)

        identities = []
        for row in cursor.fetchall():
            identities.append(Identity(row))

        return identities
        
    def __init__(self, tuple):
        self.uuid = tuple[0]
        self.type = tuple[1]
        self.name = tuple[2]
        self.email = tuple[3]
        self.note = tuple[4]
        self.avatar_uri = tuple[5]
        self.active = tuple[6]
        self.create_ts = tuple[7]



AGENCY_LIST = []
class Agency:
    def __init__(self, *, seed):
        self.uuid = generate_uuidv7(seed)
        self.esid = generate_agency_esid(seed)
        self.create_ts = generate_timestamp(seed)
        self.name = pick_one_of(seed, AGENCY_NAME_LIST)
        address = pick_one_of(seed, ADDRESS_LIST)
        self.line1 = address['line1']
        self.line2 = address['line2']
        self.city = address['city']
        self.state = address['state']
        self.zip = address['zip']
        self.active = active_or_not(seed)
        self.admin_note = generate_note(seed)
        self.data = {}

        # insert into the AGENCY_LIST
        AGENCY_LIST.append(self)

        # Insert into the database
        CURSOR.execute("""
            INSERT INTO df4l.agency (
                agency_uuid,
                agency_esid,
                create_ts,
                name,
                active,
                admin_note,
                data
            ) VALUES (
                %(agency_uuid)s,
                %(agency_esid)s,
                %(create_ts)s,
                %(name)s,
                %(active)s,
                %(admin_note)s,
                %(data)s::jsonb
            )
        """, {
            'agency_uuid': self.uuid,
            'agency_esid': self.esid,
            'create_ts': self.create_ts,
            'name': self.name,
            'active': self.active,
            'admin_note': self.admin_note,
            'data': json.dumps(self.data)
        })
        DB.commit()
    
    def __repr__(self):
        return f"Name: {self.name} uuid: {self.uuid} esid: {self.esid} create_ts: {self.create_ts} address: {self.line1} {self.line2} {self.city} {self.state} {self.zip} active: {self.active} admin_note: {self.admin_note} data: {self.data}"

ADVISOR_LIST = []
class Advisor:
    def __init__(self, *, seed, agency_uuid, identity_uuid):
        self.uuid = generate_uuidv7(seed)
        self.identity_uuid = identity_uuid
        self.agency_uuid = agency_uuid
        self.create_ts = generate_timestamp(seed)
        self.esid = generate_advisor_esid(seed)
        #TODO: gbu_advisor_esid = None
        self.name = pick_one_of(seed, NAME_LIST)
        self.email = generate_email(seed, self.name['first_name'].lower())
        self.phone = generate_phone(seed)
        self.address = pick_one_of(seed, ADDRESS_LIST)
        self.active = active_or_not(seed)
        self.admin_note = generate_note(seed)
        self.data = {}

        # insert into the ADVISOR_LIST
        ADVISOR_LIST.append(self)

        # Insert into the database
        CURSOR.execute("""
            INSERT INTO df4l.advisor (
                advisor_uuid,
                agency_uuid,
                identity_uuid,
                create_ts,
                advisor_esid,
                first_name,
                last_name,
                email,
                phone,
                address1,
                address2,
                city,
                state,
                zip,
                country,
                active,
                admin_note,
                data
            ) VALUES (
                %(advisor_uuid)s,
                %(agency_uuid)s,
                %(identity_uuid)s,
                %(create_ts)s,
                %(advisor_esid)s,
                %(first_name)s,
                %(last_name)s,
                %(email)s,
                %(phone)s,
                %(address1)s,
                %(address2)s,
                %(city)s,
                %(state)s,
                %(zip)s,
                %(country)s,
                %(active)s,
                %(admin_note)s,
                %(data)s::jsonb
            )
        """, {
            'advisor_uuid': self.uuid,
            'agency_uuid': self.agency_uuid,
            'identity_uuid': self.identity_uuid,
            'create_ts': self.create_ts,
            'advisor_esid': self.esid,
            'first_name': self.name['first_name'],
            'last_name': self.name['last_name'],
            'email': self.email,
            'phone': self.phone,
            'address1': self.address['line1'],
            'address2': self.address['line2'],
            'city': self.address['city'],
            'state': self.address['state'],
            'zip': self.address['zip'],
            'country': self.address['country'],
            'active': self.active,
            'admin_note': self.admin_note,
            'data': json.dumps(self.data)
        })
        DB.commit()

        # Generate clients for this advisor
        for i in range(pick_one_of(seed, NUMBER_OF_CLIENTS_PER_ADVISOR)):
            Client0(advisor_uuid=self.uuid, seed=f'{seed}-(0){i}')

        for i in range(pick_one_of(seed, NUMBER_OF_CLIENTS_PER_ADVISOR)):
            Client(advisor_uuid=self.uuid, seed=f'{seed}-(1){i}')
        
    def __repr__(self):
        return f'Advisor {self.name} ({self.email})'


class Client0:
    def __init__(self, *, advisor_uuid, seed):
        self.uuid = generate_uuidv7(seed)
        self.advisor_uuid = advisor_uuid
        self.create_ts = generate_timestamp(seed)
        self.first_name = pick_one_of(seed, FIRST_NAME_LIST)
        self.last_name = pick_one_of(seed, LAST_NAME_LIST)
        self.email = generate_email(seed, self.first_name.lower())
        self.phone = generate_phone(seed)
        self.phone2 = generate_phone(seed)
        self.address = pick_one_of(seed, ADDRESS_LIST)
        self.timezone = pick_one_of(seed, TIMEZONE_LIST)
        self.monthly_budget = generate_monthly_budget(seed)
        #TODO: self.file_znid_doc_to_append = None
        self.active = active_or_not(seed)
        self.note = generate_note(seed)
        self.annual_insurance_premium = generate_insurance_premium(seed)
        self.annual_insurance_pua = generate_insurance_pua(seed)
        self.net_cash_at_end = generate_net_cash_at_end(seed)
        self.debt_free_start_date = generate_date(seed)
        self.debt_free_active = active_or_not(seed)
        #TODO: self.debt_free_extra_pua_list = []
        self.data = {}

        # Insert into the database
        CURSOR.execute("""
            INSERT INTO df4l.client0 (
                client_uuid,
                advisor_uuid,
                create_ts,
                first_name,
                last_name,
                email,
                phone,
                phone2,
                address1,
                address2,
                city,
                state,
                zip,
                country,
                time_zone,
                monthly_budget,
                active,
                note,
                annual_insurance_premium,
                annual_insurance_pua,
                net_cash_at_end,
                debt_free_start_date,
                debt_free_active,
                data
            ) VALUES (
                %(client_uuid)s,
                %(advisor_uuid)s,
                %(create_ts)s,
                %(first_name)s,
                %(last_name)s,
                %(email)s,
                %(phone)s,
                %(phone2)s,
                %(address1)s,
                %(address2)s,
                %(city)s,
                %(state)s,
                %(zip)s,
                %(country)s,
                %(time_zone)s,
                %(monthly_budget)s,
                %(active)s,
                %(note)s,
                %(annual_insurance_premium)s,
                %(annual_insurance_pua)s,
                %(net_cash_at_end)s,
                %(debt_free_start_date)s,
                %(debt_free_active)s,
                %(data)s::jsonb
            )
        """, {
            'client_uuid': self.uuid,
            'advisor_uuid': self.advisor_uuid,
            'create_ts': self.create_ts,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'email': self.email,
            'phone': self.phone,
            'phone2': self.phone2,
            'address1': self.address['line1'],
            'address2': self.address['line2'],
            'city': self.address['city'],
            'state': self.address['state'],
            'zip': self.address['zip'],
            'country': self.address['country'],
            'time_zone': self.timezone,
            'monthly_budget': self.monthly_budget,
            'active': self.active,
            'note': self.note,
            'annual_insurance_premium': self.annual_insurance_premium,
            'annual_insurance_pua': self.annual_insurance_pua,
            'net_cash_at_end': self.net_cash_at_end,
            'debt_free_start_date': self.debt_free_start_date,
            'debt_free_active': self.debt_free_active,
            'data': json.dumps(self.data)
        })
        DB.commit()

        # Generate debts for this client
        for i in range(pick_one_of(seed, NUMBER_OF_DEBTS_PER_CLIENT)):
            Client0Debt(client_uuid=self.uuid, seed=f'{seed}-{i}')

"""
CREATE TABLE df4l.client (
    client_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    advisor_uuid uuid NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    first_name varchar(64) NOT NULL,
    last_name varchar(64) NOT NULL,
    email varchar(255),
    phone varchar(20),
    phone2 varchar(20),
    address1 varchar(128),
    address2 varchar(128),
    city varchar(32),
    state varchar(16),
    zip varchar(16),
    country varchar(32),
    active bool NOT NULL DEFAULT true,
    time_zone varchar(32),
    note varchar(4096),
    CONSTRAINT "client_pkey" PRIMARY KEY ("client_uuid"),
    CONSTRAINT fk_advisor FOREIGN KEY (advisor_uuid) REFERENCES df4l.advisor(advisor_uuid) ON UPDATE RESTRICT ON DELETE RESTRICT
);
"""

class Client:
    def __init__(self, *, advisor_uuid, seed):
        self.client_uuid = generate_uuidv7(seed)
        self.advisor_uuid = advisor_uuid
        self.create_ts = generate_timestamp(seed)
        self.first_name = pick_one_of(seed, FIRST_NAME_LIST)
        self.last_name = pick_one_of(seed, LAST_NAME_LIST)
        self.email = generate_email(seed, self.first_name.lower())
        self.phone = generate_phone(seed)
        self.phone2 = generate_phone(seed)
        self.address = pick_one_of(seed, ADDRESS_LIST)
        self.active = active_or_not(seed)
        self.timezone = pick_one_of(seed, TIMEZONE_LIST)
        self.note = generate_note(seed)
        
        # Insert into the database
        CURSOR.execute("""
            INSERT INTO df4l.client (
                client_uuid,
                advisor_uuid,
                create_ts,
                first_name,
                last_name,
                email,
                phone,
                phone2,
                address1,
                address2,
                city,
                state,
                zip,
                country,
                active,
                time_zone,
                note
            ) VALUES (
                %(client_uuid)s,
                %(advisor_uuid)s,
                %(create_ts)s,
                %(first_name)s,
                %(last_name)s,
                %(email)s,
                %(phone)s,
                %(phone2)s,
                %(address1)s,
                %(address2)s,
                %(city)s,
                %(state)s,
                %(zip)s,
                %(country)s,
                %(active)s,
                %(time_zone)s,
                %(note)s
            )
        """, {
            'client_uuid': self.client_uuid,
            'advisor_uuid': self.advisor_uuid,
            'create_ts': self.create_ts,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'email': self.email,
            'phone': self.phone,
            'phone2': self.phone2,
            'address1': self.address['line1'],
            'address2': self.address['line2'],
            'city': self.address['city'],
            'state': self.address['state'],
            'zip': self.address['zip'],
            'country': self.address['country'],
            'active': self.active,
            'time_zone': self.timezone,
            'note': self.note
        })
        DB.commit()

        # Generate debts for this client
        for i in range(pick_one_of(seed, NUMBER_OF_DEBTS_PER_CLIENT)):
            ClientDebt(client_uuid=self.client_uuid, seed=f'{seed}-debt-{i}')
    

CLIENT_DEBT_LIST = []
class Client0Debt:
    def __init__(self, *, client_uuid, seed):
        self.uuid = generate_uuidv7(seed)
        self.client_uuid = client_uuid
        self.create_ts = generate_timestamp(seed)
        self.name = pick_one_of(seed, DEBT_NAME_LIST)
        self.balance = generate_balance(seed)
        self.balance_date = generate_date(seed)
        self.interest_rate = generate_interest_rate()
        self.monthly_payment = generate_monthly_payment(self.balance, seed)
        self.active = active_or_not(seed)
        self.note = generate_note(seed)
        self.data = {}

        # Insert into the database
        CURSOR.execute("""
            INSERT INTO df4l.client0_debt (
                client_debt_uuid,
                client_uuid,
                create_ts,
                name,
                balance,
                balance_date,
                interest_rate,
                monthly_payment,
                active,
                note,
                data
            ) VALUES (
                %(client_debt_uuid)s,
                %(client_uuid)s,
                %(create_ts)s,
                %(name)s,
                %(balance)s,
                %(balance_date)s,
                %(interest_rate)s,
                %(monthly_payment)s,
                %(active)s,
                %(note)s,
                %(data)s::jsonb
            )
        """, {
            'client_debt_uuid': self.uuid,
            'client_uuid': self.client_uuid,
            'create_ts': self.create_ts,
            'name': self.name,
            'balance': self.balance,
            'balance_date': self.balance_date,
            'interest_rate': self.interest_rate,
            'monthly_payment': self.monthly_payment,
            'active': self.active,
            'note': self.note,
            'data': json.dumps(self.data)
        })
        DB.commit()

    

CLIENT_NOTE_LIST = []
class Client0Note:
    def __init__(self, *, client_uuid, seed):
        self.uuid = generate_uuidv7(seed)
        self.client_uuid = client_uuid
        self.create_ts = generate_timestamp(seed)
        self.note = generate_client_note(seed)
        self.data = {}

    def __repr__(self):
        return f"""CLIENT NOTE OBJECT:
        uuid: {self.uuid} 
        client_uuid: {self.client_uuid} 
        create_ts: {self.create_ts} 
        note: {self.note}
        data: {self.data}
        """

'''
#Generate 10 agencies
for i in range(10):
    AGENCY_LIST.append(Agency(seed=SEED + str(i)))

#Generate 100 advisors
#for i in range(100):
#    if pick_one_of(SEED + str(i), ADVISOR_PART_OF_AGENCY):
#        agency_uuid = pick_one_of(SEED + str(i), AGENCY_LIST).uuid
#    else:
#        agency_uuid = None
#
#    ADVISOR_LIST.append(Advisor(agency_uuid=agency_uuid, seed=SEED + str(i)))

#Generate 100 clients
for i in range(100):
    advisor_uuid = pick_one_of(SEED + str(i), ADVISOR_LIST).uuid
    CLIENT_LIST.append(Client(advisor_uuid=advisor_uuid, seed=SEED + str(i)))

#Generate 3 debts for each client
for client in CLIENT_LIST:
    for i in range(3):
        CLIENT_DEBT_LIST.append(ClientDebt(client_uuid=client.uuid, seed=SEED + str(client.uuid) + str(i)))

#Generate 25 client notes
for i in range(25):
    client_uuid = pick_one_of(SEED + str(i), CLIENT_LIST).uuid
    CLIENT_NOTE_LIST.append(ClientNote(client_uuid=client.uuid, seed=SEED + str(client_uuid) + str(i)))

'''

##########################################################################################################################


class ClientDebt:
    def __init__(self, *, client_uuid, seed):
        self.client_debt_uuid = generate_uuidv7(seed)
        self.client_uuid = client_uuid
        self.create_ts = generate_timestamp(seed)
        self.active = active_or_not(seed)

        # Determine if this is a CRS debt (85%) or manual debt (15%)
        if is_crs_debt(seed):
            # CRS debt - has debt_esid, manual fields must be NULL
            self.client_debt_esid = generate_crs_debt_esid(seed)
            self.name = None
            self.balance = None
            self.balance_date = None
            self.interest = None
            self.payment = None
            self.note = None
        else:
            # Manual debt - no debt_esid, can have manual fields
            self.client_debt_esid = None
            self.name = pick_one_of(seed, DEBT_NAME_LIST)
            self.balance = generate_balance(seed) / 100.0  # More reasonable balance
            self.balance_date = generate_date(seed)
            self.interest = generate_interest_rate()
            self.payment = generate_monthly_payment(self.balance, seed)
            self.note = generate_note(seed)

        # Insert into the database
        CURSOR.execute("""
            INSERT INTO df4l.client_debt (
                client_debt_uuid,
                client_debt_esid,
                client_uuid,
                active,
                create_ts,
                name,
                balance,
                balance_date,
                interest,
                payment,
                note
            ) VALUES (
                %(client_debt_uuid)s,
                %(client_debt_esid)s,
                %(client_uuid)s,
                %(active)s,
                %(create_ts)s,
                %(name)s,
                %(balance)s,
                %(balance_date)s,
                %(interest)s,
                %(payment)s,
                %(note)s
            )
        """, {
            'client_debt_uuid': self.client_debt_uuid,
            'client_debt_esid': self.client_debt_esid,
            'client_uuid': self.client_uuid,
            'active': self.active,
            'create_ts': self.create_ts,
            'name': self.name,
            'balance': self.balance,
            'balance_date': self.balance_date,
            'interest': self.interest,
            'payment': self.payment,
            'note': self.note
        })
        DB.commit()




##########################################################################################################################

def get_identities():

    """Retrieve all identities from auth_fence.identity table"""
    cursor = DB.cursor()  # Use dictionary cursor to get column names
    cursor.execute("""
        SELECT 
            identity_uuid, 
            identity_type, 
            name, 
            email, 
            note, 
            avatar_uri, 
            active,
            create_ts
        FROM 
            auth_fence.identity
        ORDER BY 
            name
    """)
    
    identities = cursor.fetchall()
    cursor.close()
    
    return identities
    
    
def generate_advisors():
    # Clear existing advisor list
    ADVISOR_LIST.clear()

    #generate 3 advisors for each identity
    for identity in identities:
        for i in range(3):
            # Create a unique seed for this identity
            identity_seed = f"{SEED}-identity-{identity['identity_uuid']}-{i}"
            
            # Determine if advisor belongs to an agency
            if pick_one_of(identity_seed, ADVISOR_PART_OF_AGENCY):
                agency_uuid = pick_one_of(identity_seed, AGENCY_LIST).uuid
            else:
                agency_uuid = None
            
            # Create advisor
            advisor = Advisor(agency_uuid=agency_uuid, seed=identity_seed)
            
            # Override email with identity email
            advisor.email = identity['email']
            
            # Store identity_uuid in advisor data
            advisor.data['identity_uuid'] = str(identity['identity_uuid'])
            
            # Add to advisor list
            ADVISOR_LIST.append(advisor)
            print(f"Generated advisor for identity: {identity['name']} ({identity['email']})")



identities = get_identities()


##########################################################################################################################

def transfer_agencies(target_db):
    AgencyMap = {} #map between agency_znid and agency_uuid 
    try:
        # Insert into target database
        cursor = target_db.cursor()
        for agency in AGENCY_LIST:
            print(f"Processing agency: {agency.name}")
            try:
                # Convert data to JSON string if it's a dict

                cursor.execute("""
                    INSERT INTO df4l.agency (
                        agency_uuid,
                        agency_esid,
                        create_ts,
                        name,
                        active,
                        admin_note,
                        data
                    ) VALUES (
                        %(agency_uuid)s,
                        %(agency_esid)s,
                        %(create_ts)s,
                        %(name)s,
                        %(active)s,
                        %(admin_note)s,
                        %(data)s::jsonb
                    )
                """, {
                    'agency_uuid': agency.uuid,
                    'agency_esid': agency.esid,
                    'create_ts': agency.create_ts,
                    'name': agency.name,
                    'active': agency.active,
                    'admin_note': agency.admin_note,
                    'data': json.dumps(agency.data)
                })
                target_db.commit()
            except Exception as e:
                target_db.rollback()
                print(f"Error processing agency {agency['name']}: {e}")
                print(f"Data type: {type(agency['data'])}")
                print(f"Data content: {agency['data']}")
                print("##############################################################################################")
                continue
        
        cursor.close()
        print("Transfer completed successfully")
        print("")
        print("")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during transfer: {e}")
        print("##############################################################################################")
        raise
    return AgencyMap

def transfer_advisors(target_db, AgencyMap):
    try:
        # Insert into target database
        cursor = target_db.cursor()
        for advisor in ADVISOR_LIST:
            advisor_name = f"{advisor.name['first_name']} {advisor.name['last_name']}"
            print(f"Processing advisor: {advisor_name}")
            try:
                # Handle None agency_uuid properly
                agency_uuid = None
                if advisor.agency_uuid is not None:
                    agency_uuid = AgencyMap.get(advisor.agency_uuid)
                                
                cursor.execute("""
                    INSERT INTO df4l.advisor (
                        advisor_uuid,
                        agency_uuid,
                        create_ts,
                        advisor_esid,
                        first_name,
                        last_name,
                        email,
                        phone,
                        address1,
                        address2,
                        city,
                        zip,
                        country,
                        active,
                        admin_note,
                        data
                    ) VALUES (
                        %(advisor_uuid)s,
                        %(agency_uuid)s,
                        %(create_ts)s,
                        %(advisor_esid)s,
                        %(first_name)s,
                        %(last_name)s,
                        %(email)s,
                        %(phone)s,
                        %(address1)s,
                        %(address2)s,
                        %(city)s,
                        %(zip)s,
                        %(country)s,
                        %(active)s,
                        %(admin_note)s,
                        %(data)s::jsonb
                    )
                """, {
                    'advisor_uuid': advisor.uuid,
                    'agency_uuid': agency_uuid,
                    'create_ts': advisor.create_ts,
                    'advisor_esid': advisor.esid,
                    'first_name': advisor.name['first_name'],
                    'last_name': advisor.name['last_name'],
                    'email': advisor.email,
                    'phone': advisor.phone,
                    'address1': advisor.address['line1'],
                    'address2': advisor.address['line2'],
                    'city': advisor.address['city'],
                    'zip': advisor.address['zip'],
                    'country': advisor.address['country'],
                    'active': advisor.active,
                    'admin_note': advisor.admin_note,
                    'data': json.dumps(advisor.data)
                })
                

                target_db.commit()
                print(f"Successfully inserted advisor: {advisor_name}")
            except Exception as e:
                target_db.rollback()
                print(f"Error processing advisor {advisor_name}: {e}")
                print("##############################################################################################")
                continue

        cursor.close()
        print("Advisor transfer completed successfully")
        print("")
        print("")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during advisor transfer: {e}")
        print("##############################################################################################")
        raise
    
    return

def transfer_clients(target_db):
    try:
        # Insert into target database
        cursor = target_db.cursor()
        for client in CLIENT_LIST:
            client_name = f"{client.name['first_name']} {client.name['last_name']}"
            print(f"Processing client: {client_name}")
            try:

                cursor.execute("""
                    INSERT INTO df4l.client0 (
                        client_uuid,
                        advisor_uuid,
                        create_ts,
                        name,
                        email,
                        phone,
                        phone2,
                        address1,
                        address2,
                        city,
                        state,
                        zip,
                        country,
                        time_zone,
                        monthly_budget,
                        
                        active,
                        note,
                        annual_insurance_premium,
                        annual_insurance_pua,
                        net_cash_at_end,
                        debt_free_start_date,
                        debt_free_active,
                        
                        data
                    ) VALUES (
                        %(client_uuid)s,
                        %(advisor_uuid)s,
                        %(create_ts)s,
                        %(name)s,
                        %(email)s,
                        %(phone)s,
                        %(phone2)s,
                        %(address1)s,
                        %(address2)s,
                        %(city)s,
                        %(state)s,
                        %(zip)s,
                        %(country)s,
                        %(time_zone)s,
                        %(monthly_budget)s,
                        
                        %(active)s,
                        %(note)s,
                        %(annual_insurance_premium)s,
                        %(annual_insurance_pua)s,
                        %(net_cash_at_end)s,
                        %(debt_free_start_date)s,
                        %(debt_free_active)s,

                        %(data)s::jsonb
                    )
                """, {
                    'client_uuid': client.uuid,
                    'advisor_uuid': client.advisor_uuid,
                    'create_ts': client.create_ts,
                    'name': client_name,
                    'email': client.email,
                    'phone': client.phone,
                    'phone2': client.phone2,
                    'address1': client.address['line1'],
                    'address2': client.address['line2'],
                    'city': client.address['city'],
                    'state': client.address['state'],
                    'zip': client.address['zip'],
                    'country': client.address['country'],
                    'time_zone': client.timezone,
                    'monthly_budget': client.monthly_budget,
                    #'file_znid_doc_to_append': client.file_znid_doc_to_append,
                    'active': client.active,
                    'note': client.note,
                    'annual_insurance_premium': client.annual_insurance_premium,
                    'annual_insurance_pua': client.annual_insurance_pua,
                    'net_cash_at_end': client.net_cash_at_end,
                    'debt_free_start_date': client.debt_free_start_date,
                    'debt_free_active': client.debt_free_active,
                    #'debt_free_extra_pua_list': json.dumps(client.debt_free_extra_pua_list),
                    'data': json.dumps(client.data)
                })
                

                target_db.commit()
                print(f"Successfully inserted client: {client_name}")
            except Exception as e:
                target_db.rollback()
                print(f"Error processing client {client_name}: {e}")
                print("##############################################################################################")
                continue

        cursor.close()
        print("Client transfer completed successfully")
        print("")
        print("")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during client transfer: {e}")
        print("##############################################################################################")
        raise

    return

def transfer_client_debts(target_db):
    try:
        # Insert into target database
        cursor = target_db.cursor()
        for client_debt in CLIENT_DEBT_LIST:
            print(f"Processing client debt: {client_debt.name}")
            try:

                cursor.execute("""
                    INSERT INTO df4l.client0_debt (
                        client_debt_uuid,
                        client_uuid,
                        create_ts,
                        name,
                        balance,
                        balance_date,
                        interest_rate,
                        monthly_payment,
                        active,
                        note,
                        data
                    ) VALUES (
                        %(client_debt_uuid)s,
                        %(client_uuid)s,
                        %(create_ts)s,
                        %(name)s,
                        %(balance)s,
                        %(balance_date)s,
                        %(interest_rate)s,
                        %(monthly_payment)s,
                        %(active)s,
                        %(note)s,
                        %(data)s::jsonb
                    )
                """, {
                    'client_debt_uuid': client_debt.uuid,
                    'client_uuid': client_debt.client_uuid,
                    'create_ts': client_debt.create_ts,
                    'name': client_debt.name,
                    'balance': client_debt.balance,
                    'balance_date': client_debt.balance_date,
                    'interest_rate': client_debt.interest_rate,
                    'monthly_payment': client_debt.monthly_payment,
                    'active': client_debt.active,
                    'note': client_debt.note,
                    'data': json.dumps(client_debt.data)
                })

                target_db.commit()
                print(f"Successfully inserted client debt: {client_debt.name}")
            except Exception as e:
                target_db.rollback()
                print(f"Error processing client debt {client_debt.name}: {e}")
                print("##############################################################################################")
                continue

        cursor.close()
        print("Client debt transfer completed successfully")
        print("")
        print("")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during client debt transfer: {e}")
        print("##############################################################################################")
        raise
    
    return

def transfer_client_notes(target_db):
    try:
        # Insert into target database
        cursor = target_db.cursor()
        for client_note in CLIENT_NOTE_LIST:
            print(f"Processing client note: {client_note.note}")
            try:

                cursor.execute("""
                    INSERT INTO df4l.client0_note (
                        client_note_uuid,
                        client_uuid,
                        create_ts,
                        note,
                        data
                    ) VALUES (
                        %(client_note_uuid)s,
                        %(client_uuid)s,
                        %(create_ts)s,
                        %(note)s,
                        %(data)s::jsonb
                    )
                """, {
                    'client_note_uuid': client_note.uuid,
                    'client_uuid': client_note.client_uuid,
                    'create_ts': client_note.create_ts,
                    'note': client_note.note,
                    'data': json.dumps(client_note.data)
                })

                target_db.commit()
                print(f"Successfully inserted client note: {client_note.note}")
            except Exception as e:
                target_db.rollback()
                print(f"Error processing client note {client_note.note}: {e}")
                print("##############################################################################################")
                continue

        cursor.close()
        print("Client note transfer completed successfully")
        print("")
        print("")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during client note transfer: {e}")
        print("##############################################################################################")
        raise
    
    return


def main():
    try:
        #print("Connecting to source database...")
        #db1 = get_source_db()
        #print("Connected to source database successfully")
        
        print("Connecting to target database...")
        print("Connected to target database successfully")
        
        # Create a cursor and execute truncate statements
        CURSOR.execute("TRUNCATE TABLE df4l.agency CASCADE")
        CURSOR.execute("TRUNCATE TABLE df4l.advisor CASCADE")
        CURSOR.execute("TRUNCATE TABLE df4l.client CASCADE")
        CURSOR.execute("TRUNCATE TABLE df4l.client0 CASCADE")
        CURSOR.execute("TRUNCATE TABLE df4l.client0_debt CASCADE")
        CURSOR.execute("TRUNCATE TABLE df4l.client0_note CASCADE")
        DB.commit()
        

    except Exception as e:
        print(f"Error during truncate: {e}")
        raise


    # Create a few agencies
    for i in range(pick_one_of(SEED, AGENCY_COUNT)):
        Agency(seed=f'{SEED}-{i}')

    identities = Identity.list()

    for i, identity in enumerate(identities):
        for n in range(pick_one_of(f'{SEED}-{i}', NUMBER_ADVISORS_FOR_IDENTITY)):
            agency_uuid = None
            Advisor(seed=f'{SEED}-{i}-{n}', agency_uuid=agency_uuid, identity_uuid=identity.uuid)

    



if __name__ == "__main__":    
    main()



# print the agencies out
#for agency in AGENCY_LIST:
#    print(agency)

# print the advisors out
#for advisor in ADVISOR_LIST:
#    print(advisor)


# print the clients out
#for client in CLIENT_LIST:
#    print(client)

#for client_debt in CLIENT_DEBT_LIST:
#    print(client_debt)

#for client_note in CLIENT_NOTE_LIST:
#    print(client_note)

