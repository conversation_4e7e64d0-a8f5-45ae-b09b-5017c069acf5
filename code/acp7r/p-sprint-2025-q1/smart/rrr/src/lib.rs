#[path = "libλ.rs"]
pub mod libλ;

pub mod module;
pub mod web;

///////////////////////////////////////////////////////////////////////////////////////////////////

#[derive(serde::Deserialize)]
pub struct AppConfig {
    pub redis: approck_redis::ModuleConfig,
    pub webserver: approck::server::ModuleConfig,
    pub auth_fence: auth_fence::types::ModuleConfig,
    pub postgres: approck_postgres::ModuleConfig,
}

pub struct AppStruct {
    pub redis: approck_redis::ModuleStruct,
    pub webserver: approck::server::Module,
    pub auth_fence: auth_fence::types::ModuleStruct,
    pub postgres: approck_postgres::ModuleStruct,
}

#[derive(Debug)]
pub struct IdentityStruct {
    auth_fence: Option<auth_fence::api::identity::Identity>,
}

pub use crate::web::Document::Document as DocumentStruct;

///////////////////////////////////////////////////////////////////////////////////////////////////

impl approck::App for AppStruct {
    type Config = AppConfig;
    type Identity = IdentityStruct;
    async fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            webserver: approck::server::Module::new(config.webserver).await?,
            redis: approck_redis::ModuleStruct::new(config.redis).await?,
            auth_fence: auth_fence::types::ModuleStruct::new(config.auth_fence).await?,
            postgres: approck_postgres::ModuleStruct::new(config.postgres).await?,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        // get the crate name using the env! macro
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {}", crate_name);
        Ok(())
    }

    async fn auth(&self, req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        use auth_fence::App;
        let auth_fence = self.auth_fence_system();

        let mut redis = match self.redis.get_dbcx().await {
            Ok(redis) => redis,
            Err(e) => {
                return Err(granite::process_error!(
                    "Error getting Redis connection: {}",
                    e
                ));
            }
        };

        match auth_fence
            .get_user_identity(&req.session_token(), &mut redis)
            .await
        {
            Ok(user_info) => match user_info {
                Some(user_info) => Ok(IdentityStruct {
                    auth_fence: Some(user_info),
                }),
                None => Ok(IdentityStruct { auth_fence: None }),
            },
            Err(_e) => Ok(IdentityStruct { auth_fence: None }),
        }
    }
}

impl approck::Identity for IdentityStruct {}
