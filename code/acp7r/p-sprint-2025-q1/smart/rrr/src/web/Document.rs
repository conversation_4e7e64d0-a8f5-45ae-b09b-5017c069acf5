bux::document! {

    pub struct Document {}

    impl Document {
        pub fn new(
            _app: &'static crate::AppStruct,
            identity: &crate::IdentityStruct,
            _req: &approck::server::Request,
        ) -> Self {
            use bux::document::Cliffy;
            use bux::document::{Base, Nav2};

            let mut this = Self {
                ..Default::default()
            };

            // Base setup
            this.set_uri(_req.path());
            this.set_title("RRR"); // default title
            this.set_site_name("RRR");
            this.set_owner("SMART Publishing");
            this.add_logo("/", "https://asset7.net/Zagula/Smart/RRR/3R-white-logo.svg");

            // Nav2 setup
            this.set_identity(identity);

            this
        }
    }

    impl bux::document::Base for Document {
        fn render_body(&self) -> maud::Markup {
            bux::document::Cliffy::render_body(self)
        }
    }

    impl bux::document::Nav1 for Document {}
    impl bux::document::Nav2 for Document {}
    impl bux::document::PageNav for Document {}
    impl bux::document::FooterSocial for Document {}
    impl bux::document::FooterLinks for Document {}
    impl bux::document::Cliffy for Document {}

    impl rrr_public::Document for Document {}
    impl auth_fence::Document for Document {}
}
