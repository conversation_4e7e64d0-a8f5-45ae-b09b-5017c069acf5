[package]
name = "mediaproctor"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "mediaproctor"

[[bin]]
name = "mp-process"
path = "src/mp-process.rs"

[[bin]]
name = "mp-stream"
path = "src/mp-stream.rs"

[dependencies]
clap = { workspace = true, features = ["derive"] }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tokio = { workspace = true, features = ["full"] }
reqwest = { workspace = true, features = ["json", "stream", "blocking"] }
anyhow = { workspace = true }
tempfile = { workspace = true }
num_cpus = { workspace = true }
sha2 = { workspace = true }
iso8601-timestamp = { workspace = true }
sxd-document = { workspace = true }
