#[path = "libλ.rs"]
pub mod libλ;

pub mod module;
pub mod web;

///////////////////////////////////////////////////////////////////////////////////////////////////

pub trait App: approck::server::App + ace_server_zero::App {}

pub trait Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}

pub trait Document: bux::document::Base {}

///////////////////////////////////////////////////////////////////////////////////////////////////

#[derive(Debug, serde::Deserialize)]
pub struct AppConfig {
    pub webserver: approck::server::ModuleConfig,
}

pub struct AppStruct {
    pub webserver: approck::server::Module,
    pub zero_system: ace_server_zero::ZeroSystem,
}

#[derive(Debug)]
pub enum IdentityStruct {
    Anonymous,
}

bux::document! {
    pub struct DocumentStruct {}
    impl ace_server_dashboard::Document for DocumentStruct {}
}

///////////////////////////////////////////////////////////////////////////////////////////////////

impl approck::App for AppStruct {
    type Config = AppConfig;
    type Identity = IdentityStruct;

    async fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            webserver: approck::server::Module::new(config.webserver).await?,
            zero_system: ace_server_zero::ZeroSystem::init(),
        })
    }

    async fn init(&'static self) -> granite::Result<()> {
        // get the crate name using the env! macro
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {}", crate_name);
        let ace_core_app = self.zero_system.ace_core_app;
        if ace_core_app.local_file_path.exists() {
            dotenv::from_path(&ace_core_app.local_file_path)
                .expect("Failed to apply LOCAL.env file");
        }
        ace_server_agent::App::init(self);
        Ok(())
    }

    async fn auth(&self, _req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        Ok(IdentityStruct::Anonymous)
    }
}

///////////////////////////////////////////////////////////////////////////////////////////////////

impl crate::App for AppStruct {}
impl crate::Identity for IdentityStruct {}
impl crate::Document for DocumentStruct {}
