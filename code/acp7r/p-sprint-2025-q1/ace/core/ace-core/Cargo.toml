[package]
name = "ace-core"
version = "2.0.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "ace_core"


[dependencies]
granite = { workspace = true }
garbage = { path = "../../shared/garbage"}
ace-aws = { path = "../../core/ace-aws" }
ace-db = { path = "../../core/ace-db" }
ace-graph = { path = "../../core/ace-graph" }
ace-proc = { path = "../../core/ace-proc" }

#aws-config = "0.55.2"
#aws-sdk-dynamodb = "0.27.0"
#aws-sdk-ec2 = "0.27.0"
anyhow = { workspace = true }
async-trait = { workspace = true }
base64_light = { workspace = true }
clap = { workspace = true }
chrono = { workspace = true }
comfy-table = { workspace = true }
console = { workspace = true }
dialoguer = { workspace = true }
error-stack = { workspace = true }
flate2 = { workspace = true }
futures = { workspace = true }
git2 = { workspace = true }
glob = { workspace = true }
hcl-rs = { workspace = true }
hostname = { workspace = true }
indicatif = { workspace = true }
ipnetwork = { workspace = true }
openssl = { workspace = true }
regex = { workspace = true }
reqwest = { workspace = true, features = ["json"] }
serde = { workspace = true }
serde_json = { workspace = true }
serde_toml = { workspace = true }
serde_with = { workspace = true }
serde_yaml = { workspace = true }
sha2 = { workspace = true }
shell-quote = { workspace = true }
shell-words = { workspace = true }
ssh-key = { workspace = true }
tar = { workspace = true }
tempfile = { workspace = true }
tokio = { workspace = true, features = ["full", "test-util"] }
toml = { workspace = true }
trust-dns-client = { workspace = true }
trust-dns-proto = { workspace = true }
ureq = { workspace = true }
url = { workspace = true }
urlencoding = { workspace = true }
uuid = { workspace = true, features = ["v4"] }
zip = { workspace = true }
form_urlencoded = { workspace = true }
fs-more = { workspace = true }




[build-dependencies]
urlencoding = { workspace = true }
