[package]
name = "appcove-appcove"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.acp]
app.port = 3016

extends = ["appcove-appcove-zero", "appcove-appcove-public", "approck", "bux", "granite", "auth-fence"]

[dependencies]
appcove-appcove-zero = { path = "../appcove-appcove-zero" }
appcove-appcove-public = { path = "../appcove-appcove-public" }
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-redis = { workspace = true }
approck-postgres = { workspace = true }
auth-fence = { workspace = true }


clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
toml = { workspace = true }
chrono = { workspace = true, features = ["serde"] }


