.html{
    height: 100%;
}
.container {
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    padding-right: 1rem;
    padding-left: 1rem;
}

.row-group {
    display: flex;
    flex-wrap: wrap;
    margin-right: 1.5rem;
    margin-left: 1.5rem;
}

.row-group > .col-quarter,
.row-group > .col-half,
.row-group > .col-wide,
.row-group > .col-small {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
    box-sizing: border-box;
}

.col-quarter {
    flex: 0 0 auto;
    width: 33.333333%;
}

.col-half {
    flex: 0 0 auto;
    width: 41.666667%;
}

.col-wide {
    flex: 0 0 auto;
    width: 66.666667%;
}

.col-small {
    flex: 0 0 auto;
    width: 16.666667%;
}

.flex-group {
    display: flex;
}

.justify-center {
    justify-content: center;
}

.text-bold {
    font-weight: bold;
}

.text-small {
    font-size: 0.875em;
}

.text-upper {
    text-transform: uppercase;
}

.list-plain {
    padding-left: 0;
    margin-top:2rem;
    list-style: none;
}
.section-title {
    color: #00a29a;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.staff-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 2.3rem;
}

.staff-card {
    background-color: #f6f2eb;
    padding: 0.5rem;
    width: calc(40% - 0.2rem);
    border-radius: 15px;
    box-sizing: border-box;
    font-size: 1.5rem;
    line-height: 1.5;
}

.tenure {
    display: block;
    color: #00a29a;
    font-weight: bold;
    margin-top: 0.25rem;
}

.contact-section {
    margin-top: 2rem;
    font-size: 1.5rem;
}

.contact-title {
    color: #00a29a;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.contact-info {
    display: flex;
    justify-content:space-between;
    margin-bottom: 0.5rem;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: #000;
}

.logo .highlight {
    color: #00a29a;
}

/* Additional page-specific styles */
body {
    margin: 0;
    padding: 0;
    font-family: 'Inter' Segoe UI, Tahoma, Geneva, 'Verdan', 'sans-serif';
    font-size: 18px;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

}

.appcove-title {
    font-size: 5rem;
    font-weight: 300;
    color: #21536c;
}

.panel {
    border: nonem;
    border-radius: 1rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.content {
    padding: 3rem;
    background-color: #ffffff;
    color: #333;
}

.highlight-text {
    color: #00bfa5;
    font-weight: 600;
    font-size: 1rem;
    margin-right: 0.5rem;
}

.important-text {
    color: #21536c;
    font-weight: bold;
    font-size: 1.17rem;
    line-height: 1.4;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 3rem;
    font-weight: 600;
    margin-top: 1rem;
    color: #21536c;
}

.p-notable {
    font-weight: 600;
    color: #0e6b78;

}

.timeline-dot {
    height: 20px;
    width: 20px;
    background-color: #00bfa5;
    border-radius: 100%;
    display: inline-block;
    margin-right: 8px;
    position: absolute;
    left: -31px;
    top: 3px;
}

.timeline-year {
    font-weight: bold;
    color: #0e6b78;
}

.active-project {
    font-weight: 600;
    color: #00bfa5;
    font-style: italic;
}

.inactive-project {
    color: #21536c;
    font-weight: 600;
    font-style: italic;
}

.timeline-container {
    position: relative;
    margin-left: 10px;
    padding-left: 20px;
    border-left: 3px dotted #102f53;
    font-family: 'Inter', sans serif;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.problem-answer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1.5rem 1.5rem 1rem 1.5rem;
    text-size-adjust: 100px;
    font-size: 3rem;
    font-weight: 600;
    color: #003366;
}

.problem-answer-header h5 {
    font-weight: bold;
    color: #21536c;
    margin: 0;
}

.arrow-icon-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
}

.arrow-icon {
    background-color: #007b8a;
    color: white;
    font-size: 1.5rem;
    border-radius: 50%;
    padding: 0.75rem;
}

.dotted-border {
    border-bottom: 2px dotted #0096a7;
    margin-left: 2rem;
    margin-right: 2rem;
}

.timeline-panel {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background-color: #fffbe9;
    border-radius: 0.5rem;
    padding: 1rem;
}